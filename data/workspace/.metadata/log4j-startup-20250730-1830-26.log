2025-07-30 18:30:26,296 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:30:26,296 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:30:26,296 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:30:26,296 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:30:26,297 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:30:26,297 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:26,297 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:30:30,567 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:30:30,716 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-30 18:30:30,716 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:30:30,753 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0369 s. ]
2025-07-30 18:30:30,807 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:30:30,907 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 22 s. ]
2025-07-30 18:30:31,117 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.83 s. ]
2025-07-30 18:30:31,194 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:31,194 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:30:31,217 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-30 18:30:31,217 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:31,217 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 18:30:31,222 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 18:30:31,231 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:30:31,369 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:30:31,469 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:30:32,023 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-07-30 18:30:32,036 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,036 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:30:32,347 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:30:32,361 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.34 s. ]
2025-07-30 18:30:32,397 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,397 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:30:32,401 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:30:32,455 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:30:32,502 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:30:32,540 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:30:32,584 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:30:32,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:30:32,630 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:30:32,689 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:30:32,725 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:30:32,725 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-07-30 18:30:32,725 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,725 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:30:32,740 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:30:32,740 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,740 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:30:32,852 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:30:32,860 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:30:32,983 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-30 18:30:32,985 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,985 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:30:32,995 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:30:32,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:30:32,995 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:30:37,175 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.18 s. ]
2025-07-30 18:30:37,176 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:30:37,176 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.9 s. ]
2025-07-30 18:30:37,176 [main] INFO  com.polarion.platform.startup - ****************************************************************
