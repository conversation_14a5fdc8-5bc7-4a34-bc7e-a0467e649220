2025-07-30 18:00:50,070 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0442 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0124 s [50% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-30 18:00:50,187 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0355 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0708 s, system: 0.107 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.105 s [86% log2 (10x)] (13x), ObjectMaps: 0.0555 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.193 s, system: 0.281 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.049 s, system: 0.0904 s], Allocated memory: 7.0 MB, transactions: 0, ObjectMaps: 0.0623 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.233 s, system: 0.339 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.109 s, system: 0.211 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.079 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:00:50,962 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.0984 s, system: 0.137 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.132 s [38% log2 (10x), 21% log (1x), 16% info (5x), 16% getLatestRevision (3x)] (24x), ObjectMaps: 0.0756 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:00:50,963 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.53 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.357 s [62% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 18:00:51,215 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.216 s [100% getReadConfiguration (48x)] (48x), svn: 0.0801 s [83% info (18x)] (38x)
2025-07-30 18:00:51,535 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.242 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.184 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:00:51,779 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.13 s, CPU [user: 0.0354 s, system: 0.0103 s], Allocated memory: 11.1 MB
2025-07-30 18:00:51,841 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 27, PersistenceEngineListener: 0.289 s [100% doFinishStartup (1x)] (1x), commit: 0.0839 s [100% Revision (1x)] (1x), Lucene: 0.0514 s [100% refresh (1x)] (1x), DB: 0.0159 s [36% update (3x), 31% query (1x), 25% execute (1x)] (8x)
