2025-07-30 17:34:52,288 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0745 s [69% update (144x), 31% query (12x)] (221x), svn: 0.0192 s [63% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-30 17:34:52,412 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0381 s [62% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 17:34:53,208 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.204 s, system: 0.279 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:34:53,207 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.792 s, CPU [user: 0.273 s, system: 0.353 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.159 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:34:53,208 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.792 s, CPU [user: 0.0499 s, system: 0.0919 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0761 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:34:53,208 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.792 s, CPU [user: 0.103 s, system: 0.145 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.128 s [44% log2 (10x), 19% info (5x), 17% log (1x)] (24x), ObjectMaps: 0.0833 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:34:53,209 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.0788 s, system: 0.116 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0915 s [80% log2 (10x)] (13x), ObjectMaps: 0.0586 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:34:53,208 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.114 s, system: 0.208 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0687 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:34:53,210 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.571 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.333 s [61% log2 (36x), 14% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 17:34:53,371 [main | u:p] INFO  TXLOGGER - Tx 6616abd1d1001_0_6616abd1d1001_0_: finished. Total: 0.118 s, CPU [user: 0.0867 s, system: 0.00501 s], Allocated memory: 21.8 MB
2025-07-30 17:34:53,514 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.249 s [100% getReadConfiguration (48x)] (48x), svn: 0.0858 s [86% info (18x)] (38x)
2025-07-30 17:34:53,879 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.293 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.214 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:34:54,160 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.132 s, CPU [user: 0.0104 s, system: 0.00251 s], Allocated memory: 735.6 kB, GC: 0.065 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:34:54,236 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.215 s, CPU [user: 0.0283 s, system: 0.00682 s], Allocated memory: 2.4 MB, GC: 0.065 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:34:54,241 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.218 s, CPU [user: 0.00475 s, system: 0.00237 s], Allocated memory: 810.5 kB, GC: 0.065 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:34:54,241 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.22 s, CPU [user: 0.0297 s, system: 0.00562 s], Allocated memory: 2.3 MB, GC: 0.065 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:34:54,262 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.101 s, CPU [user: 0.027 s, system: 0.0102 s], Allocated memory: 12.9 MB
2025-07-30 17:34:54,279 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.254 s, CPU [user: 0.0357 s, system: 0.0112 s], Allocated memory: 11.0 MB, GC: 0.065 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:34:54,338 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.444 s [100% doFinishStartup (1x)] (1x), Lucene: 0.176 s [100% refresh (1x)] (1x), commit: 0.0598 s [100% Revision (1x)] (1x)
2025-07-30 17:34:56,722 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.279 s [90% info (158x)] (168x)
2025-07-30 17:34:56,980 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616abd537445_0_6616abd537445_0_: finished. Total: 0.247 s, CPU [user: 0.128 s, system: 0.0167 s], Allocated memory: 20.2 MB, resolve: 0.0843 s [62% User (2x), 36% Project (1x)] (5x), Lucene: 0.0297 s [100% search (1x)] (1x), svn: 0.0231 s [34% getLatestRevision (2x), 29% log (1x), 21% testConnection (1x)] (8x), ObjectMaps: 0.02 s [56% getPrimaryObjectProperty (2x), 34% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 17:34:57,506 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.699 s, CPU [user: 0.00557 s, system: 0.00168 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 17:34:57,507 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.262 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.0995 s [66% User (3x), 30% Project (1x)] (7x), Lucene: 0.0434 s [68% search (1x), 24% add (1x)] (3x), svn: 0.0288 s [37% getLatestRevision (3x), 27% testConnection (2x), 24% log (1x)] (10x), ObjectMaps: 0.0246 s [47% getPrimaryObjectLocation (3x), 45% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0214 s [100% WorkItem (22x)] (22x)
2025-07-30 17:34:57,507 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.789 s, CPU [user: 0.155 s, system: 0.0219 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.618 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0479 s [86% buildBaselineSnapshots (1x)] (24x)
2025-07-30 17:34:57,844 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abd535840_0_6616abd535840_0_: finished. Total: 1.12 s, CPU [user: 0.29 s, system: 0.0742 s], Allocated memory: 47.1 MB, svn: 0.664 s [47% getDatedRevision (181x), 36% getDir2 content (25x)] (307x), resolve: 0.313 s [100% Category (96x)] (96x), ObjectMaps: 0.108 s [47% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 17:34:58,023 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abd65e848_0_6616abd65e848_0_: finished. Total: 0.109 s, CPU [user: 0.0523 s, system: 0.00878 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0472 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), svn: 0.047 s [54% info (19x), 39% getFile content (15x)] (36x), resolve: 0.0368 s [100% User (9x)] (9x), ObjectMaps: 0.0166 s [48% getPrimaryObjectProperty (8x), 28% getLastPromoted (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 17:34:58,236 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abd68144a_0_6616abd68144a_0_: finished. Total: 0.182 s, CPU [user: 0.0645 s, system: 0.00594 s], Allocated memory: 19.9 MB, svn: 0.126 s [57% getDir2 content (17x), 43% getFile content (44x)] (62x), RepositoryConfigService: 0.0889 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 17:34:58,856 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abd6af04b_0_6616abd6af04b_0_: finished. Total: 0.62 s, CPU [user: 0.312 s, system: 0.013 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.45 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.307 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-07-30 17:34:59,249 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abd761c4e_0_6616abd761c4e_0_: finished. Total: 0.298 s, CPU [user: 0.12 s, system: 0.00733 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.199 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.189 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 17:34:59,249 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.52 s, CPU [user: 0.906 s, system: 0.119 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.45 s [40% getDir2 content (115x), 35% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.83 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.383 s [82% Category (96x)] (117x), ObjectMaps: 0.141 s [47% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 17:34:59,249 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.07 s [45% getDatedRevision (362x), 28% getDir2 content (115x), 25% getFile content (807x)] (1324x), RepositoryConfigService: 0.83 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.383 s [82% Category (96x)] (118x), ObjectMaps: 0.141 s [47% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
