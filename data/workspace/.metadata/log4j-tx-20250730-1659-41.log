2025-07-30 16:59:46,482 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0651 s [65% update (144x), 34% query (12x)] (221x), svn: 0.0106 s [54% getLatestRevision (2x), 36% testConnection (1x)] (4x)
2025-07-30 16:59:46,598 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.035 s [63% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.773 s, CPU [user: 0.252 s, system: 0.341 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.774 s, CPU [user: 0.125 s, system: 0.218 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.1 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.774 s, CPU [user: 0.218 s, system: 0.282 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.148 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.774 s, CPU [user: 0.0502 s, system: 0.0695 s], Allocated memory: 6.8 MB, transactions: 0, svn: 0.0839 s [82% log2 (5x)] (7x), ObjectMaps: 0.0478 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.774 s, CPU [user: 0.0816 s, system: 0.102 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.109 s [83% log2 (10x)] (13x), ObjectMaps: 0.0742 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:59:47,375 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.774 s, CPU [user: 0.111 s, system: 0.142 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.179 s [56% log2 (10x), 18% log (1x), 12% info (5x)] (24x), ObjectMaps: 0.078 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:59:47,376 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.573 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.454 s [68% log2 (36x), 11% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 16:59:47,632 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.218 s [100% getReadConfiguration (48x)] (48x), svn: 0.0798 s [83% info (18x)] (38x)
2025-07-30 16:59:47,964 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.257 s [78% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.2 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 16:59:48,216 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.233 s [100% doFinishStartup (1x)] (1x), commit: 0.0478 s [100% Revision (1x)] (1x), Lucene: 0.0407 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.017 s [100% objectsToInv (1x)] (1x), DB: 0.0159 s [47% execute (1x), 29% update (3x), 17% query (1x)] (8x)
2025-07-30 16:59:51,052 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.367 s [89% info (158x)] (168x)
2025-07-30 16:59:51,058 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 3, persistence listener: 0.0154 s [81% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.00532 s [43% WorkItemActivityCreator (1x), 25% PlanActivityCreator (1x), 20% TestRunActivityCreator (1x)] (4x)
2025-07-30 16:59:51,672 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a3cce4443_0_6616a3cce4443_0_: finished. Total: 0.612 s, CPU [user: 0.167 s, system: 0.0256 s], Allocated memory: 21.5 MB, resolve: 0.224 s [73% User (2x), 26% Project (1x)] (5x), ObjectMaps: 0.103 s [77% getPrimaryObjectLocation (2x), 13% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0706 s [100% search (1x)] (1x), GC: 0.057 s [100% G1 Young Generation (1x)] (1x), svn: 0.04 s [45% getLatestRevision (2x), 27% testConnection (1x), 14% log (1x)] (8x)
2025-07-30 16:59:52,718 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.48 s, CPU [user: 0.00804 s, system: 0.00201 s], Allocated memory: 315.5 kB, transactions: 1
2025-07-30 16:59:52,722 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 26, notification worker: 0.621 s [100% RevisionActivityCreator (2x)] (2x), resolve: 0.315 s [80% User (3x), 19% Project (1x)] (7x), ObjectMaps: 0.113 s [80% getPrimaryObjectLocation (3x), 12% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0963 s [73% search (1x), 14% refresh (1x)] (3x), svn: 0.0539 s [45% getLatestRevision (3x), 33% testConnection (2x), 10% log (1x)] (10x), Incremental Baseline: 0.0418 s [100% WorkItem (22x)] (22x)
2025-07-30 16:59:52,723 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.67 s, CPU [user: 0.198 s, system: 0.0346 s], Allocated memory: 18.2 MB, transactions: 23, svn: 1.21 s [99% getDatedRevision (181x)] (183x), Lucene: 0.157 s [93% buildBaselineSnapshots (1x)] (24x)
2025-07-30 16:59:53,217 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3cce5045_0_6616a3cce5045_0_: finished. Total: 2.16 s, CPU [user: 0.4 s, system: 0.111 s], Allocated memory: 46.6 MB, svn: 1.2 s [46% getDir2 content (25x), 39% getDatedRevision (181x)] (307x), resolve: 0.768 s [100% Category (96x)] (96x), ObjectMaps: 0.28 s [46% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (387x)
2025-07-30 16:59:54,476 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3cf1ec48_0_6616a3cf1ec48_0_: finished. Total: 1.14 s, CPU [user: 0.113 s, system: 0.0329 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.509 s [68% getReadConfiguration (162x), 32% getReadUserConfiguration (10x)] (172x), resolve: 0.271 s [100% User (9x)] (9x), svn: 0.258 s [65% info (19x), 32% getFile content (15x)] (36x), ObjectMaps: 0.124 s [53% getLastPromoted (8x), 27% getPrimaryObjectProperty (8x)] (32x)
2025-07-30 16:59:54,686 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d03d449_0_6616a3d03d449_0_: finished. Total: 0.2 s, CPU [user: 0.047 s, system: 0.00949 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0877 s [89% getReadConfiguration (54x)] (77x), svn: 0.0394 s [98% getFile content (12x)] (13x)
2025-07-30 16:59:55,199 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d06f84a_0_6616a3d06f84a_0_: finished. Total: 0.513 s, CPU [user: 0.129 s, system: 0.0312 s], Allocated memory: 19.9 MB, svn: 0.37 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.197 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 16:59:56,702 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d0efc4b_0_6616a3d0efc4b_0_: finished. Total: 1.5 s, CPU [user: 0.537 s, system: 0.145 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.14 s [94% getReadConfiguration (8682x)] (9021x), svn: 0.706 s [58% getFile content (412x), 38% getDir2 content (21x)] (444x), GC: 0.115 s [100% G1 Young Generation (3x)] (3x)
2025-07-30 16:59:56,742 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a8f4775-7f000001-1475ce17-49c44384] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.592 s, CPU [user: 0.284 s, system: 0.0595 s], Allocated memory: 43.0 MB, transactions: 2, PolarionAuthenticator: 0.511 s [100% authenticate (1x)] (1x), GC: 0.04 s [100% G1 Young Generation (2x)] (2x)
2025-07-30 16:59:57,000 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a8f4a4b-7f000001-1475ce17-8119e929] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/lib/ember/radiobutton.js': Total: 0.125 s, CPU [user: 0.00224 s, system: 0.00108 s], Allocated memory: 48.7 kB, transactions: 0, PolarionAuthenticator: 0.0873 s [100% authenticate (1x)] (1x), interceptor: 0.0867 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 16:59:57,021 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a8f4a12-7f000001-1475ce17-3622ee5f] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.198 s, CPU [user: 0.0252 s, system: 0.00603 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0849 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.018 s [100% readUserData (1x)] (1x)
2025-07-30 16:59:57,227 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d26784e_0_6616a3d26784e_0_: finished. Total: 0.524 s, CPU [user: 0.0766 s, system: 0.0106 s], Allocated memory: 19.0 MB, svn: 0.475 s [57% getDir2 content (14x), 36% info (37x)] (81x), RepositoryConfigService: 0.115 s [65% getReadConfiguration (124x), 35% getExistingPrefixes (12x)] (148x)
2025-07-30 16:59:57,304 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a8f4b8e-7f000001-1475ce17-b08bf16a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.105 s, CPU [user: 0.0123 s, system: 0.00267 s], Allocated memory: 1.1 MB, transactions: 0
2025-07-30 16:59:57,304 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a8f4b61-7f000001-1475ce17-756d6ebc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.151 s, CPU [user: 0.0726 s, system: 0.011 s], Allocated memory: 9.5 MB, transactions: 0, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 16:59:57,343 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a8f4b8e-7f000001-1475ce17-a5045fb0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753865997108': Total: 0.144 s, CPU [user: 0.0421 s, system: 0.00583 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 16:59:57,437 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a8f4b8e-7f000001-1475ce17-7eb837c4 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865997110] INFO  TXLOGGER - Tx 6616a3d2f9450_0_6616a3d2f9450_0_: finished. Total: 0.152 s, CPU [user: 0.0696 s, system: 0.0114 s], Allocated memory: 5.7 MB, svn: 0.0188 s [44% info (2x), 31% testConnection (1x), 24% getFile content (2x)] (6x)
2025-07-30 16:59:57,447 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a8f4b8e-7f000001-1475ce17-7eb837c4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865997110': Total: 0.248 s, CPU [user: 0.0868 s, system: 0.015 s], Allocated memory: 7.8 MB, transactions: 1, RepositoryConfigService: 0.152 s [100% getReadConfiguration (1x)] (1x), svn: 0.0188 s [44% info (2x), 31% testConnection (1x), 24% getFile content (2x)] (6x)
2025-07-30 16:59:57,457 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d2eac4f_0_6616a3d2eac4f_0_: finished. Total: 0.23 s, CPU [user: 0.0508 s, system: 0.0091 s], Allocated memory: 6.9 MB, svn: 0.203 s [56% info (35x), 36% getDir2 content (8x)] (52x), RepositoryConfigService: 0.0756 s [53% getExistingPrefixes (9x), 47% getReadConfiguration (38x)] (54x)
2025-07-30 16:59:57,500 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a8f4b8e-7f000001-1475ce17-a22daa4d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753865997109': Total: 0.302 s, CPU [user: 0.0573 s, system: 0.00726 s], Allocated memory: 5.7 MB, transactions: 1, RepositoryConfigService: 0.18 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 16:59:58,136 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a3d324452_0_6616a3d324452_0_: finished. Total: 0.678 s, CPU [user: 0.217 s, system: 0.0506 s], Allocated memory: 393.3 MB, svn: 0.491 s [48% info (152x), 31% getDir2 content (17x), 20% getFile content (185x)] (355x), RepositoryConfigService: 0.453 s [53% getReadConfiguration (2787x), 47% getExistingPrefixes (89x)] (3025x)
2025-07-30 16:59:58,136 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 7.08 s, CPU [user: 1.61 s, system: 0.414 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.79 s [42% getDir2 content (104x), 26% getFile content (807x), 19% info (258x)] (1364x), RepositoryConfigService: 2.61 s [79% getReadConfiguration (12019x), 15% getExistingPrefixes (259x)] (12691x), resolve: 1.1 s [70% Category (96x), 25% User (9x)] (117x), ObjectMaps: 0.427 s [41% getPrimaryObjectProperty (108x), 30% getLastPromoted (108x), 29% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 16:59:58,136 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 60, svn: 5.06 s [33% getDatedRevision (362x), 31% getDir2 content (104x), 19% getFile content (810x)] (1558x), RepositoryConfigService: 2.95 s [82% getReadConfiguration (12022x)] (12694x), resolve: 1.1 s [70% Category (96x), 25% User (11x)] (122x), PolarionAuthenticator: 0.922 s [100% authenticate (35x)] (35x), ObjectMaps: 0.427 s [41% getPrimaryObjectProperty (108x), 30% getLastPromoted (108x), 29% getPrimaryObjectLocation (114x)] (442x), interceptor: 0.38 s [100% IAuthenticatorManager.getAuthenticators (70x)] (70x), Lucene: 0.264 s [55% buildBaselineSnapshots (2x), 26% search (5x)] (54x)
2025-07-30 17:00:01,484 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.107421875
