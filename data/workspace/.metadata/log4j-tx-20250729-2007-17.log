2025-07-29 20:07:22,748 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0652 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0169 s [63% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-29 20:07:22,853 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0282 s [58% getDir2 content (2x), 35% info (3x)] (6x)
2025-07-29 20:07:23,566 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.709 s, CPU [user: 0.191 s, system: 0.283 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 20:07:23,566 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.709 s, CPU [user: 0.0752 s, system: 0.116 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0762 s [80% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0548 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 20:07:23,566 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.709 s, CPU [user: 0.048 s, system: 0.0984 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0511 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 20:07:23,566 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.111 s, system: 0.218 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0689 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.045 s [82% log2 (5x)] (7x)
2025-07-29 20:07:23,567 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.709 s, CPU [user: 0.244 s, system: 0.336 s], Allocated memory: 71.0 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0953 s [44% info (5x), 18% log (1x), 17% log2 (5x), 8% getLatestRevision (2x)] (18x)
2025-07-29 20:07:23,567 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.0771 s, system: 0.146 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0864 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0668 s [81% log2 (10x)] (13x)
2025-07-29 20:07:23,567 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.499 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.328 s [60% log2 (36x), 13% info (5x), 11% getLatestRevision (9x)] (61x)
2025-07-29 20:07:23,807 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.208 s [100% getReadConfiguration (48x)] (48x), svn: 0.0739 s [86% info (18x)] (38x)
2025-07-29 20:07:24,105 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.234 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.18 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 20:07:24,342 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.223 s [100% doFinishStartup (1x)] (1x), commit: 0.0544 s [100% Revision (1x)] (1x), Lucene: 0.0341 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0147 s [100% objectsToInv (1x)] (1x), DB: 0.0121 s [39% update (3x), 27% query (1x), 24% execute (1x)] (8x)
2025-07-29 20:10:17,676 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.342822265625
2025-07-29 20:10:27,679 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.16640625
2025-07-29 20:10:37,676 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.23720703125
2025-07-29 20:10:47,677 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.069677734375
2025-07-29 21:46:37,655 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.60283203125
2025-07-29 21:46:47,653 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.417236328125
2025-07-29 21:46:57,649 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.246044921875
