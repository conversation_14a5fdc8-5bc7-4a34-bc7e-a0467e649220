2025-07-30 17:05:33,526 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0618 s [61% update (144x), 39% query (12x)] (221x), svn: 0.0158 s [59% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-07-30 17:05:33,643 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0378 s [54% getDir2 content (2x), 38% info (3x)] (6x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.247 s, system: 0.34 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.118 s, system: 0.212 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0659 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.749 s, CPU [user: 0.107 s, system: 0.121 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.145 s [50% log2 (10x), 17% info (5x), 12% log (1x), 10% testConnection (1x)] (24x), ObjectMaps: 0.0539 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.212 s, system: 0.28 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.749 s, CPU [user: 0.0566 s, system: 0.103 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0693 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0431 s [74% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-07-30 17:05:34,398 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.0887 s, system: 0.142 s], Allocated memory: 12.2 MB, transactions: 0, svn: 0.0765 s [77% log2 (10x), 16% getLatestRevision (2x)] (13x), ObjectMaps: 0.0739 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:05:34,399 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.502 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.333 s [61% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 17:05:34,640 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.204 s [100% getReadConfiguration (48x)] (48x), svn: 0.0774 s [82% info (18x)] (38x)
2025-07-30 17:05:34,997 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.28 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.207 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:05:35,224 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.112 s, CPU [user: 0.0362 s, system: 0.0108 s], Allocated memory: 11.1 MB
2025-07-30 17:05:35,264 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.251 s [100% doFinishStartup (1x)] (1x), commit: 0.0531 s [100% Revision (1x)] (1x), Lucene: 0.0459 s [100% refresh (1x)] (1x), DB: 0.0228 s [52% query (1x), 25% update (3x), 16% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0161 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0151 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-30 17:05:37,747 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.407 s [88% info (158x)] (168x)
2025-07-30 17:05:38,012 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a51f76440_0_6616a51f76440_0_: finished. Total: 0.257 s, CPU [user: 0.132 s, system: 0.0152 s], Allocated memory: 20.0 MB, resolve: 0.0778 s [66% User (2x), 32% Project (1x)] (5x), svn: 0.0212 s [37% getLatestRevision (2x), 23% log (1x), 23% testConnection (1x)] (8x), Lucene: 0.0208 s [100% search (1x)] (1x), ObjectMaps: 0.0183 s [47% getPrimaryObjectLocation (2x), 46% getPrimaryObjectProperty (2x)] (11x), GlobalHandler: 0.0131 s [97% applyTxChanges (1x)] (4x)
2025-07-30 17:05:38,650 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.891 s, CPU [user: 0.00652 s, system: 0.00131 s], Allocated memory: 529.9 kB, transactions: 1
2025-07-30 17:05:38,652 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.272 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0961 s [68% User (3x), 26% Project (1x)] (7x), svn: 0.0304 s [43% getLatestRevision (3x), 29% testConnection (2x), 16% log (1x)] (10x), Lucene: 0.0281 s [74% search (1x), 26% refresh (1x)] (2x), ObjectMaps: 0.0221 s [56% getPrimaryObjectLocation (3x), 38% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0209 s [100% WorkItem (22x)] (22x), persistence listener: 0.016 s [68% indexRefreshPersistenceListener (1x), 13% PlanActivityCreator (1x)] (7x), GlobalHandler: 0.0137 s [94% applyTxChanges (2x)] (7x)
2025-07-30 17:05:38,653 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.911 s, CPU [user: 0.18 s, system: 0.0278 s], Allocated memory: 18.5 MB, transactions: 24, svn: 0.782 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0607 s [82% buildBaselineSnapshots (1x)] (24x)
2025-07-30 17:05:39,091 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a51f76845_0_6616a51f76845_0_: finished. Total: 1.34 s, CPU [user: 0.366 s, system: 0.0893 s], Allocated memory: 47.1 MB, svn: 0.795 s [52% getDatedRevision (181x), 29% getDir2 content (25x)] (307x), resolve: 0.436 s [100% Category (96x)] (96x), ObjectMaps: 0.15 s [41% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 17:05:39,626 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a520db448_0_6616a520db448_0_: finished. Total: 0.442 s, CPU [user: 0.101 s, system: 0.0183 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.197 s [56% getReadConfiguration (162x), 44% getReadUserConfiguration (10x)] (172x), svn: 0.15 s [63% info (19x), 32% getFile content (15x)] (36x), resolve: 0.137 s [100% User (9x)] (9x), ObjectMaps: 0.0655 s [47% getPrimaryObjectProperty (8x), 34% getLastPromoted (8x)] (32x), GlobalHandler: 0.0309 s [55% applyTxChanges (2x), 45% get (26x)] (28x)
2025-07-30 17:05:39,754 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5214a849_0_6616a5214a849_0_: finished. Total: 0.125 s, CPU [user: 0.0402 s, system: 0.00264 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0712 s [96% getReadConfiguration (54x)] (77x), svn: 0.0367 s [100% getFile content (12x)] (13x)
2025-07-30 17:05:40,194 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5216a84a_0_6616a5216a84a_0_: finished. Total: 0.439 s, CPU [user: 0.129 s, system: 0.0139 s], Allocated memory: 19.9 MB, svn: 0.312 s [67% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.178 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:05:41,626 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a521d884b_0_6616a521d884b_0_: finished. Total: 1.43 s, CPU [user: 0.455 s, system: 0.0469 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.05 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.76 s [61% getFile content (412x), 39% getDir2 content (21x)] (434x)
2025-07-30 17:05:41,747 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5233e84c_0_6616a5233e84c_0_: finished. Total: 0.121 s, CPU [user: 0.0262 s, system: 0.00357 s], Allocated memory: 17.8 MB, svn: 0.109 s [86% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0229 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 17:05:42,116 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5236804e_0_6616a5236804e_0_: finished. Total: 0.324 s, CPU [user: 0.132 s, system: 0.00959 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.213 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.202 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-07-30 17:05:42,116 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.36 s, CPU [user: 1.3 s, system: 0.193 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.45 s [39% getDir2 content (115x), 38% getFile content (807x), 17% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.76 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.619 s [70% Category (96x), 22% User (9x)] (117x), ObjectMaps: 0.237 s [45% getPrimaryObjectProperty (108x), 28% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
2025-07-30 17:05:42,121 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.24 s [36% getDatedRevision (362x), 30% getDir2 content (115x), 29% getFile content (807x)] (1325x), RepositoryConfigService: 1.76 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.619 s [70% Category (96x), 22% User (10x)] (118x), ObjectMaps: 0.237 s [45% getPrimaryObjectProperty (108x), 28% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
2025-07-30 17:05:49,363 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a94a97d-7f000001-5ecfa2a4-56ad9500] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.437 s, CPU [user: 0.241 s, system: 0.0583 s], Allocated memory: 43.0 MB, transactions: 2, PolarionAuthenticator: 0.388 s [100% authenticate (1x)] (1x)
2025-07-30 17:05:49,796 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a94ac5a-7f000001-5ecfa2a4-32c335e3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.138 s, CPU [user: 0.0738 s, system: 0.0124 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 17:05:49,796 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a94ac7b-7f000001-5ecfa2a4-3554a775] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.105 s, CPU [user: 0.0199 s, system: 0.00509 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 17:05:49,820 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a94ac7b-7f000001-5ecfa2a4-4e717f48] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753866349620': Total: 0.129 s, CPU [user: 0.0403 s, system: 0.00604 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 17:05:49,875 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a94ac81-7f000001-5ecfa2a4-dfe9a886] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753866349621': Total: 0.177 s, CPU [user: 0.0702 s, system: 0.0125 s], Allocated memory: 8.3 MB, transactions: 1, RepositoryConfigService: 0.0787 s [100% getReadConfiguration (1x)] (1x), svn: 0.0122 s [71% testConnection (1x), 29% getFile content (2x)] (4x)
2025-07-30 17:05:49,882 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a94ac81-7f000001-5ecfa2a4-91709b02] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866349622': Total: 0.185 s, CPU [user: 0.0311 s, system: 0.00491 s], Allocated memory: 4.4 MB, transactions: 1, RepositoryConfigService: 0.099 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 17:06:26,444 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.116 s, CPU [user: 0.00182 s, system: 0.0044 s], Allocated memory: 130.7 kB, transactions: 0, PullingJob: 0.113 s [100% collectChanges (1x)] (1x), svn: 0.113 s [100% getLatestRevision (1x)] (1x), GC: 0.1 s [100% G1 Young Generation (1x)] (1x)
