2025-07-30 17:56:17,782 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:17,783 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:56:22,114 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:56:22,256 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-30 17:56:22,256 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:56:22,316 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0604 s. ]
2025-07-30 17:56:22,372 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:56:22,491 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-07-30 17:56:22,717 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.94 s. ]
2025-07-30 17:56:22,800 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:22,800 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:56:22,903 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.19 s. ]
2025-07-30 17:56:22,903 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:22,904 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 17:56:22,931 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (3/9)
2025-07-30 17:56:22,956 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:56:23,265 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:56:23,338 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:56:23,888 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.99 s. ]
2025-07-30 17:56:23,905 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:23,905 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:56:24,167 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:56:24,182 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-07-30 17:56:24,214 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:24,214 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:56:24,218 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:56:24,287 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:56:24,342 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:56:24,392 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:56:24,426 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:56:24,442 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:56:24,457 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:56:24,487 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:56:24,540 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:56:24,540 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-07-30 17:56:24,540 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:24,540 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:56:24,560 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:56:24,560 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:24,560 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:56:24,689 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:56:24,695 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:56:24,836 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.28 s. ]
2025-07-30 17:56:24,838 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:24,838 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:56:24,847 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:56:24,847 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:56:24,847 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
