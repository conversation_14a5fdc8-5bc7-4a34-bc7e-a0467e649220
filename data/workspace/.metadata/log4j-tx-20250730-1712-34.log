2025-07-30 17:12:39,037 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0516 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0219 s [55% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-07-30 17:12:39,271 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0365 s [60% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.0516 s, system: 0.0955 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0597 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.0794 s, system: 0.123 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0932 s [81% log2 (10x)] (13x), ObjectMaps: 0.0614 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.0844 s, system: 0.143 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0821 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0678 s [75% log2 (10x), 18% getLatestRevision (2x)] (13x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.74 s, CPU [user: 0.192 s, system: 0.288 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.242 s, system: 0.357 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:12:40,014 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.128 s, system: 0.224 s], Allocated memory: 26.3 MB, transactions: 0, svn: 0.0763 s [26% log2 (5x), 25% info (5x), 22% log (1x), 13% getLatestRevision (2x)] (18x), ObjectMaps: 0.0701 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:12:40,015 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.522 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.314 s [63% log2 (36x), 15% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 17:12:40,162 [main | u:p] INFO  TXLOGGER - Tx 6616a6bbda401_0_6616a6bbda401_0_: finished. Total: 0.12 s, CPU [user: 0.091 s, system: 0.00726 s], Allocated memory: 21.8 MB
2025-07-30 17:12:40,332 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.275 s [100% getReadConfiguration (48x)] (48x), svn: 0.0972 s [85% info (18x)] (38x)
2025-07-30 17:12:40,662 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.257 s [78% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.207 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:12:40,890 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.112 s, CPU [user: 0.0249 s, system: 0.00618 s], Allocated memory: 10.9 MB
2025-07-30 17:12:40,936 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.257 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0609 s [100% refresh (1x)] (1x), commit: 0.0486 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0137 s [100% objectsToInv (1x)] (1x)
2025-07-30 17:12:43,805 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.677 s [94% info (158x)] (168x)
2025-07-30 17:12:44,071 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a6bf8bc45_0_6616a6bf8bc45_0_: finished. Total: 0.247 s, CPU [user: 0.141 s, system: 0.018 s], Allocated memory: 20.3 MB, resolve: 0.0777 s [64% User (2x), 34% Project (1x)] (5x), Lucene: 0.026 s [100% search (1x)] (1x), svn: 0.0243 s [27% getLatestRevision (2x), 26% log (1x), 25% testConnection (1x), 15% getFile content (2x)] (8x), ObjectMaps: 0.0221 s [47% getPrimaryObjectLocation (2x), 45% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 17:12:44,644 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.758 s, CPU [user: 0.00657 s, system: 0.00184 s], Allocated memory: 316.1 kB, transactions: 1
2025-07-30 17:12:44,644 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.263 s [96% RevisionActivityCreator (2x)] (6x), resolve: 0.0944 s [70% User (3x), 28% Project (1x)] (7x), Lucene: 0.0561 s [47% add (1x), 46% search (1x)] (3x), svn: 0.0333 s [33% getLatestRevision (3x), 33% testConnection (2x), 19% log (1x)] (10x), ObjectMaps: 0.0257 s [54% getPrimaryObjectLocation (3x), 38% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0243 s [100% WorkItem (22x)] (22x), persistence listener: 0.0192 s [72% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x)
2025-07-30 17:12:44,645 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.848 s, CPU [user: 0.157 s, system: 0.0257 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.682 s [98% getDatedRevision (181x)] (183x)
2025-07-30 17:12:45,048 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6bf89440_0_6616a6bf89440_0_: finished. Total: 1.23 s, CPU [user: 0.328 s, system: 0.0871 s], Allocated memory: 47.1 MB, svn: 0.713 s [53% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.37 s [100% Category (96x)] (96x), ObjectMaps: 0.133 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 17:12:45,239 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6c0d1448_0_6616a6c0d1448_0_: finished. Total: 0.114 s, CPU [user: 0.0584 s, system: 0.00828 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0551 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (162x)] (172x), svn: 0.0506 s [58% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0333 s [100% User (9x)] (9x), ObjectMaps: 0.0158 s [55% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x), 22% getLastPromoted (8x)] (32x)
2025-07-30 17:12:45,459 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6c0f684a_0_6616a6c0f684a_0_: finished. Total: 0.185 s, CPU [user: 0.0716 s, system: 0.00699 s], Allocated memory: 19.9 MB, svn: 0.13 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.086 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:12:46,282 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6c124c4b_0_6616a6c124c4b_0_: finished. Total: 0.822 s, CPU [user: 0.378 s, system: 0.0236 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.641 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.437 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x)
2025-07-30 17:12:46,404 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6c1f284c_0_6616a6c1f284c_0_: finished. Total: 0.121 s, CPU [user: 0.0241 s, system: 0.0032 s], Allocated memory: 17.8 MB, svn: 0.111 s [89% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0195 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 17:12:46,711 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6c21944e_0_6616a6c21944e_0_: finished. Total: 0.274 s, CPU [user: 0.114 s, system: 0.00965 s], Allocated memory: 384.5 MB, RepositoryConfigService: 0.177 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.171 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x)
2025-07-30 17:12:46,711 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.9 s, CPU [user: 1.04 s, system: 0.149 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.69 s [37% getDir2 content (115x), 37% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.02 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.442 s [84% Category (96x)] (117x), ObjectMaps: 0.165 s [47% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 17:12:46,711 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.37 s [44% getDatedRevision (362x), 27% getDir2 content (115x), 26% getFile content (807x)] (1324x), RepositoryConfigService: 1.02 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.442 s [84% Category (96x)] (118x), ObjectMaps: 0.165 s [47% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 17:12:52,928 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a9b1ff0-7f000001-03702c19-2a5b060e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.463 s, CPU [user: 0.237 s, system: 0.0746 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.421 s [100% authenticate (1x)] (1x)
2025-07-30 17:12:53,102 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a9b21fe-7f000001-03702c19-0797889e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.111 s, CPU [user: 0.0237 s, system: 0.00704 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0355 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0176 s [100% readUserData (1x)] (1x)
2025-07-30 17:12:53,481 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a9b2320-7f000001-03702c19-79909c15] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.2 s, CPU [user: 0.0973 s, system: 0.0181 s], Allocated memory: 9.8 MB, transactions: 0
2025-07-30 17:12:53,481 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a9b233e-7f000001-03702c19-be5de8da] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.17 s, CPU [user: 0.0176 s, system: 0.00472 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 17:12:53,508 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a9b233f-7f000001-03702c19-e916551f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753866773229': Total: 0.196 s, CPU [user: 0.0392 s, system: 0.00685 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 17:12:53,589 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a9b2343-7f000001-03702c19-d4e2201d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753866773230': Total: 0.273 s, CPU [user: 0.0928 s, system: 0.0171 s], Allocated memory: 9.0 MB, transactions: 1, RepositoryConfigService: 0.0979 s [100% getReadConfiguration (1x)] (1x), svn: 0.0166 s [74% testConnection (1x), 26% getFile content (2x)] (4x)
2025-07-30 17:12:53,600 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a9b2345-7f000001-03702c19-be817b83] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866773231': Total: 0.282 s, CPU [user: 0.0405 s, system: 0.00798 s], Allocated memory: 4.5 MB, transactions: 1, RepositoryConfigService: 0.127 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 17:14:11,312 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a9c51c7-7f000001-03702c19-79a746be] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 0.551 s, CPU [user: 0.109 s, system: 0.0523 s], Allocated memory: 6.8 MB, transactions: 0
2025-07-30 17:15:11,583 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a9cbcdf-7f000001-03702c19-49f9eb2c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 33.4 s, CPU [user: 0.363 s, system: 0.0233 s], Allocated memory: 955.5 kB, transactions: 0
2025-07-30 17:20:05,652 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.164 s, CPU [user: 0.00244 s, system: 0.0133 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.0781 s [100% collectChanges (1x)] (1x), svn: 0.077 s [100% getLatestRevision (1x)] (1x)
2025-07-30 17:26:02,839 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5aa72eea-7f000001-03702c19-5dd0d6f1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866773232': Total: 0.107 s, CPU [user: 0.0136 s, system: 0.0411 s], Allocated memory: 1.2 MB, transactions: 0, PolarionAuthenticator: 0.0192 s [100% authenticate (1x)] (1x), interceptor: 0.0163 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
