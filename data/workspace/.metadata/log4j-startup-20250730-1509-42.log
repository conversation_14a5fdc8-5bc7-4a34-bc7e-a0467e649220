2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:42,268 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:09:46,493 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:09:46,681 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.188 s. ]
2025-07-30 15:09:46,681 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:09:46,770 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0882 s. ]
2025-07-30 15:09:46,838 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:09:46,992 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:09:47,250 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.99 s. ]
2025-07-30 15:09:47,362 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:47,362 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:09:47,398 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 15:09:47,399 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:47,399 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 15:09:47,409 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 15:09:47,422 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:09:47,610 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:09:47,691 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:09:48,293 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.89 s. ]
2025-07-30 15:09:48,310 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:48,310 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:09:48,687 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:09:48,702 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.41 s. ]
2025-07-30 15:09:48,737 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:48,737 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:09:48,743 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:09:48,829 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:09:48,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:09:48,952 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:09:49,026 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:09:49,058 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:09:49,088 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:09:49,160 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:09:49,228 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:09:49,228 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.53 s. ]
2025-07-30 15:09:49,228 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:49,228 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:09:49,248 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:09:49,268 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:49,268 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:09:49,459 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-07-30 15:09:49,486 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-07-30 15:09:49,654 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:09:49,655 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:09:49,755 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.51 s. ]
2025-07-30 15:09:49,756 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:49,756 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:09:49,764 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:09:49,764 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:09:49,764 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:09:52,519 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.75 s. ]
2025-07-30 15:09:52,520 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:09:52,520 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.2 s. ]
2025-07-30 15:09:52,520 [main] INFO  com.polarion.platform.startup - ****************************************************************
