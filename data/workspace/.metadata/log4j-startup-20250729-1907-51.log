2025-07-29 19:07:51,695 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:07:51,695 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:07:51,696 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:07:51,696 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:07:51,696 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:07:51,696 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:51,696 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:07:56,053 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:07:56,236 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.184 s. ]
2025-07-29 19:07:56,237 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:07:56,286 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0499 s. ]
2025-07-29 19:07:56,339 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:07:56,445 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 103 s. ]
2025-07-29 19:07:56,648 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.96 s. ]
2025-07-29 19:07:56,730 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:56,730 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:07:56,757 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 19:07:56,758 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:56,758 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:07:56,762 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 19:07:56,762 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 19:07:56,763 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 19:07:56,763 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-29 19:07:56,762 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-29 19:07:56,763 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 19:07:56,769 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:07:56,909 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:07:56,983 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:07:57,422 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.66 s. ]
2025-07-29 19:07:57,434 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:57,434 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:07:57,637 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:07:57,646 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-29 19:07:57,672 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:57,672 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:07:57,675 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:07:57,729 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:07:57,797 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:07:57,832 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:07:57,860 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:07:57,894 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:07:57,928 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:07:57,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:07:58,013 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:07:58,013 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-07-29 19:07:58,013 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:58,013 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:07:58,027 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:07:58,028 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:58,028 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:07:58,127 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:07:58,129 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:07:58,248 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 19:07:58,249 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:58,249 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:07:58,256 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:07:58,256 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:07:58,256 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:08:00,711 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.45 s. ]
2025-07-29 19:08:00,711 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:08:00,711 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.01 s. ]
2025-07-29 19:08:00,711 [main] INFO  com.polarion.platform.startup - ****************************************************************
