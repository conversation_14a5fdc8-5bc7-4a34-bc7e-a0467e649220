2025-07-30 16:05:43,738 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:05:43,738 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:05:43,738 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:05:43,738 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:05:43,738 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:05:43,739 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:43,739 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:05:49,200 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:05:49,398 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.198 s. ]
2025-07-30 16:05:49,398 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:05:49,473 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0746 s. ]
2025-07-30 16:05:49,539 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:05:49,875 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 16:05:50,242 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.51 s. ]
2025-07-30 16:05:50,566 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:50,566 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:05:50,620 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.38 s. ]
2025-07-30 16:05:50,621 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:50,621 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:05:50,634 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 16:05:50,634 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 16:05:50,632 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 16:05:50,634 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 16:05:50,632 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 16:05:50,632 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 16:05:50,666 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:05:50,974 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:05:51,056 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:05:51,620 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.0 s. ]
2025-07-30 16:05:51,635 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:51,635 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:05:51,926 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:05:51,942 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 16:05:51,969 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:51,969 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:05:51,974 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:05:52,040 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:05:52,095 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:05:52,150 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:05:52,209 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:05:52,242 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:05:52,273 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:05:52,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:05:52,405 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:05:52,405 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.46 s. ]
2025-07-30 16:05:52,405 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:52,405 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:05:52,428 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 16:05:52,428 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:52,428 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:05:52,545 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:05:52,551 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:05:52,896 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.47 s. ]
2025-07-30 16:05:52,901 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:52,901 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:05:52,918 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 16:05:52,918 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:05:52,918 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:05:58,788 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 5.87 s. ]
2025-07-30 16:05:58,789 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:05:58,789 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 15 s. ]
2025-07-30 16:05:58,789 [main] INFO  com.polarion.platform.startup - ****************************************************************
