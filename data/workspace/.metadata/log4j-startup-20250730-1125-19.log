2025-07-30 11:25:19,232 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:25:19,232 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:25:19,232 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:25:19,233 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:25:19,233 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:25:19,233 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:19,233 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:25:23,524 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:25:23,678 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.153 s. ]
2025-07-30 11:25:23,678 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:25:23,743 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0651 s. ]
2025-07-30 11:25:23,801 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:25:23,999 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 62 s. ]
2025-07-30 11:25:24,213 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.99 s. ]
2025-07-30 11:25:24,297 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:24,297 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:25:24,325 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:25:24,326 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:24,326 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 11:25:24,330 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:25:24,341 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:25:24,559 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:25:24,639 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:25:25,157 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.83 s. ]
2025-07-30 11:25:25,168 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,168 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:25:25,361 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:25:25,374 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 11:25:25,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,398 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:25:25,402 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:25:25,454 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:25:25,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:25:25,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:25:25,544 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:25:25,567 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:25:25,591 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:25:25,622 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:25:25,649 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:25:25,649 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.27 s. ]
2025-07-30 11:25:25,649 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,649 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:25:25,662 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:25:25,663 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,663 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:25:25,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:25:25,766 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:25:25,921 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 11:25:25,922 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,922 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:25:25,935 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:25:25,935 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:25:25,935 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:25:37,291 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 11.36 s. ]
2025-07-30 11:25:37,291 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:25:37,291 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 18.1 s. ]
2025-07-30 11:25:37,291 [main] INFO  com.polarion.platform.startup - ****************************************************************
