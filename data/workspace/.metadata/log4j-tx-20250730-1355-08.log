2025-07-30 13:55:13,939 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0455 s [57% update (144x), 42% query (12x)] (221x), svn: 0.0151 s [58% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-07-30 13:55:14,057 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0327 s [61% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 13:55:14,796 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.249 s, system: 0.343 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.14 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:55:14,796 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.0839 s, system: 0.117 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0943 s [83% log2 (10x)] (13x), ObjectMaps: 0.0488 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:55:14,796 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.194 s, system: 0.278 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:55:14,796 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.0549 s, system: 0.0967 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0691 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0387 s [71% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-07-30 13:55:14,797 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.108 s, system: 0.144 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.12 s [47% log2 (10x), 19% info (5x), 13% getLatestRevision (3x), 12% log (1x)] (24x), ObjectMaps: 0.0813 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:55:14,796 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.112 s, system: 0.211 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0642 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:55:14,798 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.515 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.327 s [64% log2 (36x), 14% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 13:55:14,960 [main | u:p] INFO  TXLOGGER - Tx 6616798c41401_0_6616798c41401_0_: finished. Total: 0.137 s, CPU [user: 0.0867 s, system: 0.00679 s], Allocated memory: 21.8 MB
2025-07-30 13:55:15,128 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.289 s [100% getReadConfiguration (48x)] (48x), svn: 0.0982 s [83% info (18x)] (38x)
2025-07-30 13:55:15,461 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.262 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.197 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:55:15,677 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.1 s, CPU [user: 0.024 s, system: 0.00694 s], Allocated memory: 10.5 MB
2025-07-30 13:55:15,728 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.251 s [100% doFinishStartup (1x)] (1x), commit: 0.0542 s [100% Revision (1x)] (1x), Lucene: 0.0462 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.015 s [100% objectsToInv (1x)] (1x)
2025-07-30 13:55:18,501 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.376 s [92% info (158x)] (168x)
2025-07-30 13:55:18,507 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.00144 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.000687 s [100% GET (2x)] (2x)
2025-07-30 13:55:18,743 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616798fdb844_0_6616798fdb844_0_: finished. Total: 0.232 s, CPU [user: 0.129 s, system: 0.0179 s], Allocated memory: 20.3 MB, resolve: 0.0726 s [59% User (2x), 37% Project (1x)] (5x), svn: 0.0241 s [43% getLatestRevision (2x), 25% log (1x), 15% testConnection (1x)] (8x), Lucene: 0.0234 s [100% search (1x)] (1x), ObjectMaps: 0.0194 s [48% getPrimaryObjectProperty (2x), 43% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 13:55:19,306 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.727 s, CPU [user: 0.00598 s, system: 0.00161 s], Allocated memory: 316.3 kB, transactions: 1
2025-07-30 13:55:19,307 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.248 s [97% RevisionActivityCreator (2x)] (5x), resolve: 0.0887 s [63% User (3x), 30% Project (1x)] (7x), Lucene: 0.043 s [54% search (1x), 38% add (1x)] (3x), svn: 0.0321 s [47% getLatestRevision (3x), 21% testConnection (2x), 19% log (1x)] (10x), Incremental Baseline: 0.031 s [100% WorkItem (22x)] (22x), ObjectMaps: 0.024 s [54% getPrimaryObjectLocation (3x), 39% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0185 s [72% indexRefreshPersistenceListener (1x), 16% TestRunActivityCreator (1x)] (7x)
2025-07-30 13:55:19,307 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.81 s, CPU [user: 0.157 s, system: 0.027 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.64 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0526 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (23x)] (24x)
2025-07-30 13:55:19,777 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616798fdb845_0_6616798fdb845_0_: finished. Total: 1.27 s, CPU [user: 0.331 s, system: 0.0847 s], Allocated memory: 47.0 MB, svn: 0.77 s [58% getDatedRevision (181x), 29% getDir2 content (25x)] (307x), resolve: 0.323 s [100% Category (96x)] (96x), ObjectMaps: 0.113 s [46% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 13:55:19,967 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679912c048_0_661679912c048_0_: finished. Total: 0.111 s, CPU [user: 0.0564 s, system: 0.00954 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0548 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0471 s [59% info (19x), 37% getFile content (15x)] (36x), resolve: 0.0305 s [100% User (9x)] (9x), ObjectMaps: 0.0141 s [47% getPrimaryObjectProperty (8x), 27% getPrimaryObjectLocation (8x), 25% getLastPromoted (8x)] (32x)
2025-07-30 13:55:20,168 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616799151c4a_0_6616799151c4a_0_: finished. Total: 0.162 s, CPU [user: 0.0629 s, system: 0.00628 s], Allocated memory: 19.8 MB, svn: 0.115 s [69% getDir2 content (17x), 31% getFile content (44x)] (62x), RepositoryConfigService: 0.063 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 13:55:21,003 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679917a44b_0_661679917a44b_0_: finished. Total: 0.834 s, CPU [user: 0.362 s, system: 0.0358 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.644 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.439 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x), GC: 0.044 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 13:55:21,440 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679926704e_0_661679926704e_0_: finished. Total: 0.324 s, CPU [user: 0.132 s, system: 0.00944 s], Allocated memory: 383.1 MB, RepositoryConfigService: 0.219 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.206 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 13:55:21,441 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.93 s, CPU [user: 1.03 s, system: 0.158 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.72 s [36% getDir2 content (115x), 35% getFile content (807x), 26% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.04 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.39 s [83% Category (96x)] (117x)
2025-07-30 13:55:21,441 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.36 s [45% getDatedRevision (362x), 26% getDir2 content (115x), 26% getFile content (807x)] (1324x), RepositoryConfigService: 1.04 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.39 s [83% Category (96x)] (118x), ObjectMaps: 0.142 s [46% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
