2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:18:55,861 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 14:19:01,056 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 14:19:01,231 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.175 s. ]
2025-07-30 14:19:01,231 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 14:19:01,315 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0835 s. ]
2025-07-30 14:19:01,400 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 14:19:01,575 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 14:19:01,854 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.0 s. ]
2025-07-30 14:19:01,999 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:01,999 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 14:19:02,032 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.18 s. ]
2025-07-30 14:19:02,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:02,033 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 14:19:02,040 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (3/9)
2025-07-30 14:19:02,050 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 14:19:02,264 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 14:19:02,379 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 14:19:03,083 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.05 s. ]
2025-07-30 14:19:03,102 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:03,102 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 14:19:03,444 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 14:19:03,460 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.38 s. ]
2025-07-30 14:19:03,499 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:03,499 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 14:19:03,503 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 14:19:03,568 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 14:19:03,630 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 14:19:03,698 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 14:19:03,753 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 14:19:03,787 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 14:19:03,825 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 14:19:03,893 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 14:19:03,960 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 14:19:03,960 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.5 s. ]
2025-07-30 14:19:03,960 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:03,960 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 14:19:03,983 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 14:19:03,983 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:03,983 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 14:19:04,135 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 14:19:04,143 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 14:19:04,356 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.37 s. ]
2025-07-30 14:19:04,358 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:04,358 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 14:19:04,369 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 14:19:04,369 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:19:04,369 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 14:19:09,288 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.92 s. ]
2025-07-30 14:19:09,289 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:19:09,289 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 13.4 s. ]
2025-07-30 14:19:09,289 [main] INFO  com.polarion.platform.startup - ****************************************************************
