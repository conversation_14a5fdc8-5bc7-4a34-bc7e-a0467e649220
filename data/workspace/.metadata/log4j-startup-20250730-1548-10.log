2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:10,884 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:48:16,883 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:48:17,106 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.222 s. ]
2025-07-30 15:48:17,106 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:48:17,220 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.114 s. ]
2025-07-30 15:48:17,296 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:48:17,428 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:48:17,707 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.83 s. ]
2025-07-30 15:48:17,806 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:17,806 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:48:17,842 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 15:48:17,843 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:17,843 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:48:17,857 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 15:48:17,857 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 15:48:17,857 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 15:48:17,857 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 15:48:17,858 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 15:48:17,858 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 15:48:17,881 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:48:18,046 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:48:18,143 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:48:18,759 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.92 s. ]
2025-07-30 15:48:18,773 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:18,773 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:48:19,058 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:48:19,078 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 15:48:19,170 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:19,170 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:48:19,182 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:48:19,258 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:48:19,315 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:48:19,378 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:48:19,453 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:48:19,495 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:48:19,547 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:48:19,644 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:48:19,719 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:48:19,719 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.64 s. ]
2025-07-30 15:48:19,719 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:19,719 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:48:19,738 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:48:19,738 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:19,738 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:48:20,179 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:48:20,194 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:48:20,684 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.95 s. ]
2025-07-30 15:48:20,690 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:20,690 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:48:20,718 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.03 s. ]
2025-07-30 15:48:20,719 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:48:20,719 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:48:27,226 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 6.51 s. ]
2025-07-30 15:48:27,226 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:48:27,226 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 16.3 s. ]
2025-07-30 15:48:27,226 [main] INFO  com.polarion.platform.startup - ****************************************************************
