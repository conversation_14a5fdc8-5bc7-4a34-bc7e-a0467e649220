2025-07-30 15:23:03,986 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0481 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0152 s [55% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 15:23:04,085 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0245 s [59% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 15:23:04,801 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.184 s, system: 0.286 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:23:04,802 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.104 s, system: 0.217 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0646 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:23:04,801 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.048 s, system: 0.104 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0661 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:23:04,802 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0739 s, system: 0.151 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.076 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0536 s [76% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-30 15:23:04,802 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0894 s, system: 0.123 s], Allocated memory: 13.3 MB, transactions: 0, svn: 0.123 s [50% log2 (10x), 16% info (5x), 13% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0448 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:23:04,801 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.238 s, system: 0.342 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.131 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:23:04,803 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.498 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.268 s [61% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 15:23:04,934 [main | u:p] INFO  TXLOGGER - Tx 66168da6bf401_0_66168da6bf401_0_: finished. Total: 0.104 s, CPU [user: 0.0836 s, system: 0.00435 s], Allocated memory: 21.8 MB
2025-07-30 15:23:05,057 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.215 s [100% getReadConfiguration (48x)] (48x), svn: 0.0727 s [84% info (18x)] (38x)
2025-07-30 15:23:05,406 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.283 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.208 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:23:05,622 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.107 s, CPU [user: 0.0417 s, system: 0.00969 s], Allocated memory: 11.6 MB
2025-07-30 15:23:05,651 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.231 s [100% doFinishStartup (1x)] (1x), commit: 0.0522 s [100% Revision (1x)] (1x), Lucene: 0.042 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0137 s [100% objectsToInv (1x)] (1x)
