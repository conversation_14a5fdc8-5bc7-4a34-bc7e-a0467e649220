2025-07-30 17:03:06,969 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:03:06,969 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:03:06,970 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:03:06,970 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:03:06,970 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:03:06,970 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:06,970 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:03:11,697 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:03:11,844 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-07-30 17:03:11,844 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:03:11,899 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0549 s. ]
2025-07-30 17:03:11,969 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:03:12,135 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 17:03:12,356 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.4 s. ]
2025-07-30 17:03:12,448 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:12,448 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:03:12,473 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 17:03:12,473 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:12,473 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 17:03:12,479 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 17:03:12,487 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:03:12,641 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:03:12,721 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:03:13,270 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.8 s. ]
2025-07-30 17:03:13,283 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:13,283 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:03:13,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:03:13,518 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 17:03:13,546 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:13,546 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:03:13,550 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:03:13,621 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:03:13,693 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:03:13,748 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:03:13,819 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:03:13,852 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:03:13,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:03:13,936 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:03:13,969 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:03:13,969 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-07-30 17:03:13,969 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:13,969 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:03:13,984 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:03:13,984 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:13,984 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:03:14,118 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:03:14,123 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:03:14,289 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-07-30 17:03:14,290 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:14,290 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:03:14,302 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:03:14,302 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:03:14,302 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:03:17,286 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.98 s. ]
2025-07-30 17:03:17,288 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:03:17,288 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-07-30 17:03:17,288 [main] INFO  com.polarion.platform.startup - ****************************************************************
