2025-07-29 19:41:40,710 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0369 s [53% query (12x), 47% update (144x)] (221x), svn: 0.0101 s [55% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-29 19:41:40,814 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0296 s [63% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.185 s, system: 0.267 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.223 s, system: 0.326 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.0701 s, system: 0.103 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0939 s [80% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0369 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.0463 s, system: 0.0978 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.073 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.693 s, CPU [user: 0.0749 s, system: 0.126 s], Allocated memory: 12.6 MB, transactions: 0, svn: 0.0813 s [84% log2 (10x)] (13x), ObjectMaps: 0.0673 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:41:41,511 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.694 s, CPU [user: 0.129 s, system: 0.205 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.0882 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0755 s [31% log2 (5x), 26% info (5x), 21% log (1x), 11% getLatestRevision (2x)] (18x)
2025-07-29 19:41:41,512 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.502 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.326 s [66% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 19:41:41,748 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.201 s [100% getReadConfiguration (48x)] (48x), svn: 0.0795 s [79% info (18x), 9% log2 (1x)] (38x)
2025-07-29 19:41:42,066 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.251 s [74% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.19 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:41:42,269 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.102 s, CPU [user: 0.0276 s, system: 0.00838 s], Allocated memory: 10.5 MB
2025-07-29 19:41:42,295 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.216 s [100% doFinishStartup (1x)] (1x), commit: 0.0426 s [100% Revision (1x)] (1x), Lucene: 0.0316 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0157 s [100% objectsToInv (1x)] (1x), DB: 0.0142 s [48% update (3x), 22% query (1x), 19% execute (1x)] (8x)
2025-07-29 19:43:36,752 [main | u:p | u:p] INFO  TXLOGGER - Tx 66157fb11903f_0_66157fb11903f_0_: finished. Total: 0.201 s, CPU [user: 0.0472 s, system: 0.011 s], Allocated memory: 7.5 MB, GlobalHandler: 0.135 s [100% applyTxChanges (1x)] (4x), GC: 0.12 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 19:43:37,293 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.702 s [51% getLatestRevision (39x), 44% info (158x)] (207x), PullingJob: 0.353 s [100% collectChanges (38x)] (38x), GlobalHandler: 0.135 s [100% applyTxChanges (1x)] (7x)
2025-07-29 19:43:37,297 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, persistence listener: 0.0125 s [86% indexRefreshPersistenceListener (1x)] (7x)
2025-07-29 19:43:38,081 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.00583 s, system: 0.00257 s], Allocated memory: 531.3 kB, transactions: 1
2025-07-29 19:43:38,081 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, notification worker: 0.0248 s [74% RevisionActivityCreator (2x), 22% WorkItemActivityCreator (1x)] (5x), Incremental Baseline: 0.0217 s [100% WorkItem (21x)] (21x), resolve: 0.021 s [68% User (1x), 32% Revision (2x)] (3x), Lucene: 0.0202 s [71% add (1x), 29% refresh (1x)] (2x), ObjectMaps: 0.00389 s [100% getPrimaryObjectLocation (1x)] (1x), GlobalHandler: 0.00198 s [62% get (3x), 38% applyTxChanges (2x)] (5x), EHCache: 0.00197 s [99% GET (11x)] (32x)
2025-07-29 19:43:38,082 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.792 s, CPU [user: 0.153 s, system: 0.0265 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.623 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0475 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (22x)] (23x)
2025-07-29 19:43:38,439 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157fb1d5c45_0_66157fb1d5c45_0_: finished. Total: 1.14 s, CPU [user: 0.368 s, system: 0.0878 s], Allocated memory: 54.6 MB, svn: 0.652 s [51% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.391 s [100% Category (96x)] (96x), ObjectMaps: 0.141 s [39% getPrimaryObjectProperty (96x), 38% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 19:43:38,662 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157fb307c48_0_66157fb307c48_0_: finished. Total: 0.135 s, CPU [user: 0.0613 s, system: 0.0102 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0629 s [53% getReadConfiguration (162x), 47% getReadUserConfiguration (10x)] (172x), svn: 0.0541 s [57% info (19x), 38% getFile content (16x)] (37x), resolve: 0.0396 s [100% User (9x)] (9x), ObjectMaps: 0.0207 s [46% getPrimaryObjectLocation (9x), 38% getPrimaryObjectProperty (9x)] (37x)
2025-07-29 19:43:38,919 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157fb33384a_0_66157fb33384a_0_: finished. Total: 0.217 s, CPU [user: 0.0716 s, system: 0.00774 s], Allocated memory: 19.8 MB, svn: 0.173 s [74% getDir2 content (17x), 26% getFile content (44x)] (62x), RepositoryConfigService: 0.0687 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 19:43:39,737 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157fb369c4b_0_66157fb369c4b_0_: finished. Total: 0.818 s, CPU [user: 0.375 s, system: 0.0228 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.624 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.45 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-29 19:43:40,087 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157fb45504e_0_66157fb45504e_0_: finished. Total: 0.226 s, CPU [user: 0.0971 s, system: 0.00515 s], Allocated memory: 385.9 MB, RepositoryConfigService: 0.142 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.141 s [51% getFile content (185x), 49% getDir2 content (20x)] (206x)
2025-07-29 19:43:40,087 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.79 s, CPU [user: 1.06 s, system: 0.147 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.63 s [40% getDir2 content (114x), 36% getFile content (809x), 20% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.958 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.477 s [82% Category (96x)] (117x), ObjectMaps: 0.175 s [41% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-29 19:43:40,087 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.26 s [42% getDatedRevision (362x), 29% getDir2 content (114x), 26% getFile content (809x)] (1328x), RepositoryConfigService: 0.958 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.477 s [82% Category (96x)] (118x), ObjectMaps: 0.175 s [41% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-29 19:43:48,338 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55fef0ae-0a465820-09c2e017-39159b78] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.451 s, CPU [user: 0.208 s, system: 0.0909 s], Allocated memory: 36.9 MB, transactions: 2, PolarionAuthenticator: 0.419 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0277 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 19:43:50,052 [ajp-nio-127.0.0.1-8889-exec-9 | cID:55fef7c8-0a465820-09c2e017-b88f30bd] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.347 s, CPU [user: 0.179 s, system: 0.0364 s], Allocated memory: 68.9 MB, transactions: 1, RPC: 0.167 s [43% decodeRequest (1x), 30% encodeResponse (1x), 27% writeResponse (1x)] (4x), PortalDataService: 0.0969 s [100% getInitData (1x)] (1x), GC: 0.058 s [100% G1 Young Generation (1x)] (1x), svn: 0.0269 s [28% testConnection (1x), 23% getDir2 content (1x), 19% info (2x), 14% getLatestRevision (1x)] (8x), RepositoryConfigService: 0.0189 s [100% getReadConfiguration (10x)] (15x), resolve: 0.0186 s [99% Project (4x)] (5x)
2025-07-29 19:43:50,641 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55fef970-0a465820-09c2e017-32d5a4d1 | u:admin] INFO  TXLOGGER - Tx 66157fbe63052_0_66157fbe63052_0_: finished. Total: 0.483 s, CPU [user: 0.138 s, system: 0.0278 s], Allocated memory: 17.2 MB, svn: 0.1 s [36% info (7x), 29% getDir2 content (2x), 26% getFile content (4x)] (15x), RepositoryConfigService: 0.083 s [76% getReadConfiguration (42x), 23% getReadUserConfiguration (33x)] (81x), resolve: 0.0482 s [95% RichPage (3x)] (8x)
2025-07-29 19:43:50,659 [ajp-nio-127.0.0.1-8889-exec-10 | cID:55fef970-0a465820-09c2e017-32d5a4d1] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.53 s, CPU [user: 0.16 s, system: 0.0334 s], Allocated memory: 20.0 MB, transactions: 1, PortalDataService: 0.49 s [100% requestPortalSite (1x)] (1x), svn: 0.1 s [36% info (7x), 29% getDir2 content (2x), 26% getFile content (4x)] (15x), RepositoryConfigService: 0.083 s [76% getReadConfiguration (42x), 23% getReadUserConfiguration (33x)] (81x), resolve: 0.0482 s [95% RichPage (3x)] (8x), RPC: 0.0364 s [50% decodeRequest (1x), 46% encodeResponse (1x)] (4x)
2025-07-29 19:43:51,225 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55fefbfd-0a465820-09c2e017-b863de35] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.444 s, CPU [user: 0.229 s, system: 0.037 s], Allocated memory: 27.7 MB, transactions: 1, RPC: 0.435 s [99% decodeRequest (1x)] (4x)
2025-07-29 19:49:07,215 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5603cf92-0a465820-09c2e017-7127d5bf] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.124 s, CPU [user: 0.0441 s, system: 0.036 s], Allocated memory: 3.1 MB, transactions: 0, PolarionAuthenticator: 0.0213 s [100% authenticate (1x)] (1x)
2025-07-29 19:49:07,674 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5603d0f8-0a465820-09c2e017-ac694eeb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.225 s, CPU [user: 0.0824 s, system: 0.0302 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 19:49:07,674 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5603d12d-0a465820-09c2e017-2c8cd322] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.172 s, CPU [user: 0.0177 s, system: 0.0101 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-29 19:49:07,681 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5603d131-0a465820-09c2e017-3d4deb5d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753789747414': Total: 0.176 s, CPU [user: 0.0237 s, system: 0.00974 s], Allocated memory: 1.2 MB, transactions: 0
2025-07-29 19:49:07,737 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5603d131-0a465820-09c2e017-45a7693e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753789747416': Total: 0.231 s, CPU [user: 0.0487 s, system: 0.0203 s], Allocated memory: 5.0 MB, transactions: 1, RepositoryConfigService: 0.099 s [100% getReadConfiguration (1x)] (1x), svn: 0.0269 s [84% testConnection (1x)] (3x)
2025-07-29 19:49:07,766 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5603d130-0a465820-09c2e017-61e39e6d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753789747415': Total: 0.262 s, CPU [user: 0.0319 s, system: 0.0086 s], Allocated memory: 3.1 MB, transactions: 1, RepositoryConfigService: 0.11 s [100% getReadConfiguration (1x)] (1x)
