2025-07-30 10:48:53,454 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0755 s [70% update (144x), 30% query (12x)] (221x), svn: 0.0173 s [54% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-30 10:48:53,579 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0341 s [58% getDir2 content (2x), 35% info (3x)] (6x)
2025-07-30 10:48:54,484 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.9 s, CPU [user: 0.27 s, system: 0.386 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.215 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 10:48:54,485 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.901 s, CPU [user: 0.208 s, system: 0.313 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.154 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:48:54,485 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.901 s, CPU [user: 0.0727 s, system: 0.109 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0941 s [30% log2 (5x), 26% info (5x), 15% log (1x), 11% testConnection (1x)] (18x), ObjectMaps: 0.0747 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:48:54,485 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.901 s, CPU [user: 0.0774 s, system: 0.134 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0803 s [80% log2 (10x)] (13x), ObjectMaps: 0.0695 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:48:54,485 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.9 s, CPU [user: 0.0856 s, system: 0.157 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0813 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0776 s [80% log2 (10x), 12% getLatestRevision (2x)] (13x)
2025-07-30 10:48:54,485 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.901 s, CPU [user: 0.114 s, system: 0.237 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0777 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:48:54,487 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.672 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.326 s [61% log2 (36x), 13% getLatestRevision (9x), 12% testConnection (6x)] (61x)
2025-07-30 10:48:54,641 [main | u:p] INFO  TXLOGGER - Tx 66164ee5fbc01_0_66164ee5fbc01_0_: finished. Total: 0.128 s, CPU [user: 0.0982 s, system: 0.00646 s], Allocated memory: 21.8 MB
2025-07-30 10:48:54,894 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.362 s [100% getReadConfiguration (48x)] (48x), svn: 0.169 s [88% info (18x)] (38x)
2025-07-30 10:48:55,318 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.332 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.25 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:48:55,560 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0307 s, system: 0.0104 s], Allocated memory: 11.0 MB
2025-07-30 10:48:55,595 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.259 s [100% doFinishStartup (1x)] (1x), commit: 0.0526 s [100% Revision (1x)] (1x), Lucene: 0.0401 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0176 s [100% objectsToInv (1x)] (1x)
2025-07-30 10:48:59,142 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.722 s [92% info (158x)] (170x)
2025-07-30 10:48:59,623 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66164eea86041_0_66164eea86041_0_: finished. Total: 0.461 s, CPU [user: 0.159 s, system: 0.024 s], Allocated memory: 22.3 MB, resolve: 0.112 s [58% User (2x), 35% Project (1x)] (5x), GC: 0.078 s [100% G1 Young Generation (1x)] (1x), hasLinkedResourcesQueryExpander: 0.0398 s [100% expand (1x)] (2x), Lucene: 0.0337 s [100% search (1x)] (1x), ObjectMaps: 0.0336 s [46% getPrimaryObjectProperty (2x), 45% getPrimaryObjectLocation (2x)] (11x), svn: 0.0281 s [41% getLatestRevision (3x), 25% log (1x), 14% testConnection (1x)] (9x)
2025-07-30 10:49:00,575 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.16 s, CPU [user: 0.00625 s, system: 0.00155 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 10:49:00,577 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.471 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.174 s [72% User (3x), 22% Project (1x)] (7x), Lucene: 0.0554 s [61% search (1x), 32% add (1x)] (3x), ObjectMaps: 0.0459 s [60% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), hasLinkedResourcesQueryExpander: 0.0398 s [100% expand (1x)] (2x), Incremental Baseline: 0.0379 s [100% WorkItem (22x)] (22x), svn: 0.0281 s [41% getLatestRevision (3x), 25% log (1x), 14% testConnection (1x)] (9x)
2025-07-30 10:49:00,578 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.44 s, CPU [user: 0.192 s, system: 0.0335 s], Allocated memory: 18.2 MB, transactions: 23, svn: 1.04 s [99% getDatedRevision (181x)] (183x), GC: 0.078 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0724 s [83% buildBaselineSnapshots (1x)] (24x)
2025-07-30 10:49:01,339 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eea84840_0_66164eea84840_0_: finished. Total: 2.18 s, CPU [user: 0.46 s, system: 0.12 s], Allocated memory: 46.6 MB, svn: 1.33 s [53% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.685 s [100% Category (96x)] (96x), ObjectMaps: 0.257 s [42% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 10:49:01,720 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eeccec48_0_66164eeccec48_0_: finished. Total: 0.221 s, CPU [user: 0.0868 s, system: 0.016 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.098 s [55% getReadConfiguration (162x), 45% getReadUserConfiguration (10x)] (172x), svn: 0.0797 s [55% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0506 s [100% User (9x)] (9x), ObjectMaps: 0.024 s [65% getPrimaryObjectProperty (8x), 20% getLastPromoted (8x)] (32x), Lucene: 0.0209 s [100% search (1x)] (1x)
2025-07-30 10:49:02,054 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eed1504a_0_66164eed1504a_0_: finished. Total: 0.274 s, CPU [user: 0.0863 s, system: 0.00965 s], Allocated memory: 19.9 MB, svn: 0.216 s [70% getDir2 content (17x), 30% getFile content (44x)] (62x), RepositoryConfigService: 0.101 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 10:49:03,239 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eed5984b_0_66164eed5984b_0_: finished. Total: 1.18 s, CPU [user: 0.503 s, system: 0.0408 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.853 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.667 s [61% getFile content (412x), 39% getDir2 content (21x)] (434x)
2025-07-30 10:49:03,405 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eee81c4c_0_66164eee81c4c_0_: finished. Total: 0.166 s, CPU [user: 0.0348 s, system: 0.00485 s], Allocated memory: 17.8 MB, svn: 0.155 s [90% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0231 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 10:49:03,794 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164eeeb584e_0_66164eeeb584e_0_: finished. Total: 0.347 s, CPU [user: 0.136 s, system: 0.0106 s], Allocated memory: 384.3 MB, svn: 0.226 s [51% getDir2 content (21x), 49% getFile content (185x)] (207x), RepositoryConfigService: 0.212 s [95% getReadConfiguration (2787x)] (3025x)
2025-07-30 10:49:03,794 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.65 s, CPU [user: 1.4 s, system: 0.218 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.79 s [40% getDir2 content (115x), 31% getFile content (807x), 25% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.36 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.794 s [86% Category (96x)] (117x), ObjectMaps: 0.303 s [46% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 10:49:03,794 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.84 s [45% getDatedRevision (362x), 29% getDir2 content (115x), 23% getFile content (807x)] (1325x), RepositoryConfigService: 1.36 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.794 s [86% Category (96x)] (118x), ObjectMaps: 0.303 s [46% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 10:50:30,826 [ajp-nio-127.0.0.1-8889-exec-2 | cID:593d0d2a-0a465820-22740b64-69a1a021] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.767 s, CPU [user: 0.28 s, system: 0.109 s], Allocated memory: 42.1 MB, transactions: 2, PolarionAuthenticator: 0.684 s [100% authenticate (1x)] (1x)
2025-07-30 10:50:31,218 [ajp-nio-127.0.0.1-8889-exec-4 | cID:593d10fb-0a465820-22740b64-c66c1c57] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.182 s, CPU [user: 0.0811 s, system: 0.0229 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 10:50:31,218 [ajp-nio-127.0.0.1-8889-exec-5 | cID:593d1118-0a465820-22740b64-ee902343] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.153 s, CPU [user: 0.0137 s, system: 0.00499 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 10:50:31,239 [ajp-nio-127.0.0.1-8889-exec-6 | cID:593d111f-0a465820-22740b64-0ba0d56a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753843830991': Total: 0.167 s, CPU [user: 0.0319 s, system: 0.00722 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 10:50:31,311 [ajp-nio-127.0.0.1-8889-exec-7 | cID:593d1120-0a465820-22740b64-98d81e99 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843830992] INFO  TXLOGGER - Tx 66164f4467c51_0_66164f4467c51_0_: finished. Total: 0.112 s, CPU [user: 0.055 s, system: 0.0123 s], Allocated memory: 7.9 MB, svn: 0.0184 s [63% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 10:50:31,317 [ajp-nio-127.0.0.1-8889-exec-7 | cID:593d1120-0a465820-22740b64-98d81e99] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843830992': Total: 0.244 s, CPU [user: 0.071 s, system: 0.0172 s], Allocated memory: 9.8 MB, transactions: 1, RepositoryConfigService: 0.113 s [100% getReadConfiguration (1x)] (1x), svn: 0.0184 s [63% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 10:50:31,346 [ajp-nio-127.0.0.1-8889-exec-8 | cID:593d1125-0a465820-22740b64-beb2dead] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753843830993': Total: 0.269 s, CPU [user: 0.0351 s, system: 0.00643 s], Allocated memory: 4.0 MB, transactions: 1, RepositoryConfigService: 0.146 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 10:52:16,946 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.143 s, CPU [user: 0.00225 s, system: 0.00292 s], Allocated memory: 130.6 kB, transactions: 0, PullingJob: 0.0845 s [100% collectChanges (1x)] (1x), svn: 0.0817 s [100% getLatestRevision (1x)] (1x)
2025-07-30 10:55:07,850 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.408935546875
2025-07-30 10:55:17,846 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.222412109375
2025-07-30 10:55:27,850 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.157421875
2025-07-30 10:55:37,850 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0396484375
2025-07-30 11:00:14,410 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.138 s, CPU [user: 0.00244 s, system: 0.00879 s], Allocated memory: 130.0 kB, transactions: 0, PullingJob: 0.109 s [100% collectChanges (1x)] (1x), svn: 0.108 s [100% getLatestRevision (1x)] (1x)
