2025-07-30 11:38:51,857 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0764 s [57% update (144x), 43% query (12x)] (221x), svn: 0.0167 s [49% getLatestRevision (2x), 41% testConnection (1x)] (4x)
2025-07-30 11:38:51,982 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0418 s [59% getDir2 content (2x), 35% info (3x)] (6x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.867 s, CPU [user: 0.0982 s, system: 0.1 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0975 s [81% log2 (10x)] (13x), ObjectMaps: 0.0633 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.868 s, CPU [user: 0.261 s, system: 0.271 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.133 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.868 s, CPU [user: 0.0599 s, system: 0.081 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0651 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0451 s [74% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.868 s, CPU [user: 0.134 s, system: 0.127 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.162 s [39% log2 (10x), 26% info (5x), 14% log (1x), 11% getLatestRevision (3x)] (24x), ObjectMaps: 0.102 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.866 s, CPU [user: 0.29 s, system: 0.326 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:38:52,856 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.868 s, CPU [user: 0.148 s, system: 0.195 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0824 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0458 s [73% log2 (5x), 17% testConnection (1x)] (7x)
2025-07-30 11:38:52,857 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.572 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.403 s [59% log2 (36x), 14% getLatestRevision (9x), 10% info (5x)] (61x)
2025-07-30 11:38:53,142 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.235 s [100% getReadConfiguration (48x)] (48x), svn: 0.0919 s [85% info (18x)] (38x)
2025-07-30 11:38:53,458 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.238 s [79% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.192 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:38:53,710 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.237 s [100% doFinishStartup (1x)] (1x), commit: 0.0446 s [100% Revision (1x)] (1x), Lucene: 0.0279 s [100% refresh (1x)] (1x), DB: 0.0191 s [40% query (1x), 26% update (3x), 21% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0164 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:38:56,780 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.589 s [91% info (158x)] (170x)
2025-07-30 11:38:57,144 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 4, resolve: 0.091 s [95% User (2x)] (3x), persistence listener: 0.0531 s [88% indexRefreshPersistenceListener (1x)] (7x), ObjectMaps: 0.0286 s [100% getPrimaryObjectLocation (2x)] (2x), notification worker: 0.0173 s [54% RevisionActivityCreator (1x), 22% PlanActivityCreator (1x), 14% WorkItemActivityCreator (1x)] (5x)
2025-07-30 11:38:57,521 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165a59efc42_0_66165a59efc42_0_: finished. Total: 0.689 s, CPU [user: 0.187 s, system: 0.0263 s], Allocated memory: 20.8 MB, linkedWorkItemsQueryExpander: 0.168 s [100% expand (1x)] (2x), resolve: 0.139 s [60% User (2x), 37% Project (1x)] (5x), svn: 0.0471 s [55% getLatestRevision (3x), 22% log (1x), 14% testConnection (1x)] (9x), Lucene: 0.0421 s [100% search (1x)] (1x), ObjectMaps: 0.0379 s [60% getPrimaryObjectProperty (2x), 29% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 11:38:58,161 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.00822 s, system: 0.00201 s], Allocated memory: 528.8 kB, transactions: 1
2025-07-30 11:38:58,161 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 25, notification worker: 0.69 s [100% RevisionActivityCreator (1x)] (1x), linkedWorkItemsQueryExpander: 0.168 s [100% expand (1x)] (2x), resolve: 0.14 s [60% User (3x), 36% Project (1x)] (6x), Lucene: 0.0738 s [57% search (1x), 23% add (1x)] (3x), svn: 0.0471 s [55% getLatestRevision (3x), 22% log (1x), 14% testConnection (1x)] (9x), ObjectMaps: 0.0379 s [60% getPrimaryObjectProperty (2x), 29% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 11:38:58,162 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.03 s, CPU [user: 0.181 s, system: 0.0282 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.848 s [98% getDatedRevision (181x)] (183x), Lucene: 0.059 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (23x)] (24x)
2025-07-30 11:38:58,939 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a59ea840_0_66165a59ea840_0_: finished. Total: 2.13 s, CPU [user: 0.421 s, system: 0.102 s], Allocated memory: 47.1 MB, svn: 1.48 s [50% getDatedRevision (181x), 39% getDir2 content (25x)] (307x), resolve: 0.473 s [100% Category (96x)] (96x), ObjectMaps: 0.166 s [47% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 11:38:59,230 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a5c1b448_0_66165a5c1b448_0_: finished. Total: 0.177 s, CPU [user: 0.0817 s, system: 0.0152 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0861 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0792 s [59% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0467 s [100% User (9x)] (9x), ObjectMaps: 0.0199 s [66% getPrimaryObjectProperty (8x), 19% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:38:59,580 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a5c5804a_0_66165a5c5804a_0_: finished. Total: 0.284 s, CPU [user: 0.0884 s, system: 0.0115 s], Allocated memory: 19.9 MB, svn: 0.218 s [77% getDir2 content (17x), 23% getFile content (44x)] (62x), RepositoryConfigService: 0.0844 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:39:00,814 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a5c9f04b_0_66165a5c9f04b_0_: finished. Total: 1.23 s, CPU [user: 0.484 s, system: 0.0587 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.972 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.672 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x), GC: 0.065 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 11:39:01,034 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a5dd384c_0_66165a5dd384c_0_: finished. Total: 0.22 s, CPU [user: 0.0524 s, system: 0.00647 s], Allocated memory: 17.8 MB, svn: 0.194 s [79% getDir2 content (18x), 21% getFile content (29x)] (48x), RepositoryConfigService: 0.0609 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 11:39:01,443 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165a5e1d84e_0_66165a5e1d84e_0_: finished. Total: 0.333 s, CPU [user: 0.137 s, system: 0.01 s], Allocated memory: 385.4 MB, RepositoryConfigService: 0.236 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.208 s [63% getFile content (185x), 37% getDir2 content (21x)] (207x)
2025-07-30 11:39:01,443 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.65 s, CPU [user: 1.36 s, system: 0.219 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3 s [42% getDir2 content (115x), 31% getFile content (807x), 25% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.51 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.573 s [83% Category (96x)] (117x)
2025-07-30 11:39:01,443 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.85 s [41% getDatedRevision (362x), 32% getDir2 content (115x), 24% getFile content (807x)] (1325x), RepositoryConfigService: 1.51 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.573 s [83% Category (96x)] (118x), ObjectMaps: 0.204 s [50% getPrimaryObjectProperty (108x), 29% getPrimaryObjectLocation (114x), 20% getLastPromoted (108x)] (442x)
2025-07-30 11:39:16,827 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5969b311-0a465820-5cef3456-276eb3d4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.714 s, CPU [user: 0.279 s, system: 0.102 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.639 s [100% authenticate (1x)] (1x), GC: 0.078 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:39:17,382 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5969b75f-0a465820-5cef3456-745238ef] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.167 s, CPU [user: 0.0826 s, system: 0.0182 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 11:39:17,382 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5969b78c-0a465820-5cef3456-0a9caa47] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.122 s, CPU [user: 0.0169 s, system: 0.00539 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 11:39:17,412 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5969b78c-0a465820-5cef3456-5920d3e7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753846757152': Total: 0.152 s, CPU [user: 0.043 s, system: 0.00961 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:39:17,462 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5969b78f-0a465820-5cef3456-595dad2c | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846757153] INFO  TXLOGGER - Tx 66165a6dfc451_0_66165a6dfc451_0_: finished. Total: 0.101 s, CPU [user: 0.0541 s, system: 0.0107 s], Allocated memory: 5.1 MB, svn: 0.016 s [71% testConnection (1x), 28% getFile content (2x)] (4x)
2025-07-30 11:39:17,484 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5969b78f-0a465820-5cef3456-8e85fe81] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846757154': Total: 0.221 s, CPU [user: 0.0287 s, system: 0.00439 s], Allocated memory: 3.5 MB, transactions: 1, RepositoryConfigService: 0.121 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:39:17,493 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5969b78f-0a465820-5cef3456-595dad2c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846757153': Total: 0.228 s, CPU [user: 0.0899 s, system: 0.0163 s], Allocated memory: 7.9 MB, transactions: 1, RepositoryConfigService: 0.101 s [100% getReadConfiguration (1x)] (1x), svn: 0.016 s [71% testConnection (1x), 28% getFile content (2x)] (4x)
2025-07-30 11:43:26,218 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.170166015625
2025-07-30 11:43:36,218 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.05234375
