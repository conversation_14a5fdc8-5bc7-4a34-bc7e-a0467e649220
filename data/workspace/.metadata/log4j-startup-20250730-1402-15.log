2025-07-30 14:02:15,867 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:02:15,868 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 14:02:15,869 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:02:15,869 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 14:02:15,870 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 14:02:15,870 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:15,870 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 14:02:20,666 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 14:02:20,827 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-30 14:02:20,827 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 14:02:20,914 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0867 s. ]
2025-07-30 14:02:20,975 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 14:02:21,114 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 14:02:21,362 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.51 s. ]
2025-07-30 14:02:21,473 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:21,473 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 14:02:21,496 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 14:02:21,496 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:21,496 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 14:02:21,501 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 14:02:21,501 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 14:02:21,502 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 14:02:21,501 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 14:02:21,501 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 14:02:21,501 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 14:02:21,509 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 14:02:21,675 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 14:02:21,833 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 14:02:22,544 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.05 s. ]
2025-07-30 14:02:22,559 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:22,559 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 14:02:22,837 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 14:02:22,851 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.31 s. ]
2025-07-30 14:02:22,887 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:22,887 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 14:02:22,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 14:02:22,992 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 14:02:23,055 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 14:02:23,126 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 14:02:23,200 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 14:02:23,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 14:02:23,283 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 14:02:23,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 14:02:23,417 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 14:02:23,417 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.57 s. ]
2025-07-30 14:02:23,417 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:23,417 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 14:02:23,434 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 14:02:23,435 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:23,435 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 14:02:23,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 14:02:23,558 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 14:02:23,720 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 14:02:23,720 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:23,720 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 14:02:23,735 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 14:02:23,736 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:02:23,736 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 14:02:27,308 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.57 s. ]
2025-07-30 14:02:27,309 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:02:27,309 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.4 s. ]
2025-07-30 14:02:27,309 [main] INFO  com.polarion.platform.startup - ****************************************************************
