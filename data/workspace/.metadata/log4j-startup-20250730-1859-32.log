2025-07-30 18:59:32,289 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:59:32,289 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:59:32,290 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:59:32,290 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:59:32,290 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:59:32,290 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:32,290 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:59:36,841 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:59:37,019 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.178 s. ]
2025-07-30 18:59:37,019 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:59:37,083 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0633 s. ]
2025-07-30 18:59:37,133 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:59:37,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 20 s. ]
2025-07-30 18:59:37,513 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.24 s. ]
2025-07-30 18:59:37,599 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:37,599 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:59:37,628 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 18:59:37,629 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:37,629 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:59:37,634 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 18:59:37,634 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 18:59:37,634 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 18:59:37,633 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 18:59:37,634 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 18:59:37,633 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 18:59:37,642 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:59:37,806 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:59:37,894 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:59:38,363 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-30 18:59:38,375 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:38,375 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:59:38,577 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:59:38,588 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 18:59:38,612 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:38,612 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:59:38,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:59:38,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:59:38,710 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:59:38,764 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:59:38,814 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:59:38,834 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:59:38,854 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:59:38,966 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:59:39,019 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:59:39,019 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.43 s. ]
2025-07-30 18:59:39,019 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:39,019 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:59:39,033 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:59:39,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:39,033 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:59:39,137 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:59:39,139 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:59:39,270 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-30 18:59:39,270 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:39,270 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:59:39,278 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:59:39,278 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:59:39,278 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:59:42,091 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.81 s. ]
2025-07-30 18:59:42,092 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:59:42,092 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.8 s. ]
2025-07-30 18:59:42,092 [main] INFO  com.polarion.platform.startup - ****************************************************************
