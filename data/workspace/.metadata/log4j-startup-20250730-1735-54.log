2025-07-30 17:35:54,371 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:54,372 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:35:58,980 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:35:59,120 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-07-30 17:35:59,120 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:35:59,194 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.074 s. ]
2025-07-30 17:35:59,250 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:35:59,370 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:35:59,586 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.22 s. ]
2025-07-30 17:35:59,672 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:59,672 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:35:59,697 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 17:35:59,698 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:59,698 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:35:59,703 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 17:35:59,703 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 17:35:59,704 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 17:35:59,704 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 17:35:59,704 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 17:35:59,704 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 17:35:59,711 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:35:59,908 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:36:00,003 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:36:00,423 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-30 17:36:00,433 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:00,433 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:36:00,634 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:36:00,645 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 17:36:00,680 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:00,680 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:36:00,684 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:36:00,742 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:36:00,797 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:36:00,846 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:36:00,891 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:36:00,919 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:36:00,940 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:36:00,989 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:36:01,033 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:36:01,033 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-07-30 17:36:01,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:01,033 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:36:01,046 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:36:01,047 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:01,047 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:36:01,148 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:36:01,150 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:36:01,296 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 17:36:01,297 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:01,297 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:36:01,305 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:36:01,305 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:36:01,305 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:36:03,749 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.44 s. ]
2025-07-30 17:36:03,749 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:36:03,750 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.38 s. ]
2025-07-30 17:36:03,750 [main] INFO  com.polarion.platform.startup - ****************************************************************
