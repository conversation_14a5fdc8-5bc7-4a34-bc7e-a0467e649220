2025-07-30 16:46:47,841 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0833 s [78% update (144x), 22% query (12x)] (221x), svn: 0.0175 s [75% getLatestRevision (2x), 19% testConnection (1x)] (4x)
2025-07-30 16:46:47,961 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0362 s [58% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.088 s, system: 0.0789 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.118 s [87% log2 (10x)] (13x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.0573 s, system: 0.0747 s], Allocated memory: 6.8 MB, transactions: 0, svn: 0.158 s [88% log2 (5x)] (7x), ObjectMaps: 0.0706 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.131 s, system: 0.173 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0814 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.282 s, system: 0.307 s], Allocated memory: 72.4 MB, transactions: 0, ObjectMaps: 0.128 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0772 s [37% log (1x), 25% log2 (5x), 18% info (5x), 10% getLatestRevision (2x)] (18x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.222 s, system: 0.243 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:46:48,834 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.869 s, CPU [user: 0.0994 s, system: 0.122 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.199 s [84% log2 (10x)] (13x), ObjectMaps: 0.106 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:46:48,835 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, svn: 0.601 s [76% log2 (36x), 8% getLatestRevision (9x)] (61x), ObjectMaps: 0.545 s [100% getAllPrimaryObjects (8x)] (59x)
2025-07-30 16:46:49,096 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.223 s [100% getReadConfiguration (48x)] (48x), svn: 0.0875 s [86% info (18x)] (38x)
2025-07-30 16:46:49,504 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.327 s [70% info (94x), 23% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.226 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 16:46:49,747 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.228 s [100% doFinishStartup (1x)] (1x), commit: 0.0405 s [100% Revision (1x)] (1x), Lucene: 0.0403 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0224 s [100% objectsToInv (1x)] (1x), DB: 0.0142 s [40% update (3x), 36% query (1x), 14% execute (1x)] (8x)
2025-07-30 16:46:56,765 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.43 s [85% info (158x)] (171x), PullingJob: 0.0278 s [100% collectChanges (2x)] (2x)
2025-07-30 16:46:57,103 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a0d8c5045_0_6616a0d8c5045_0_: finished. Total: 0.314 s, CPU [user: 0.146 s, system: 0.0218 s], Allocated memory: 20.3 MB, resolve: 0.123 s [69% User (2x), 28% Project (1x)] (5x), ObjectMaps: 0.0356 s [54% getPrimaryObjectLocation (2x), 38% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0304 s [100% search (1x)] (1x), svn: 0.027 s [43% getLatestRevision (2x), 21% testConnection (1x), 16% log (1x)] (8x)
2025-07-30 16:46:57,677 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.825 s, CPU [user: 0.00618 s, system: 0.00224 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 16:46:57,678 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.346 s [95% RevisionActivityCreator (2x)] (6x), resolve: 0.141 s [69% User (3x), 25% Project (1x)] (7x), Lucene: 0.0404 s [75% search (1x), 14% add (1x)] (3x), ObjectMaps: 0.0397 s [58% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0293 s [87% indexRefreshPersistenceListener (1x)] (7x), svn: 0.027 s [43% getLatestRevision (2x), 21% testConnection (1x), 16% log (1x)] (8x), Incremental Baseline: 0.0233 s [100% WorkItem (22x)] (22x)
2025-07-30 16:46:57,679 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.916 s, CPU [user: 0.166 s, system: 0.0277 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.729 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0559 s [86% buildBaselineSnapshots (1x)] (24x)
2025-07-30 16:46:58,266 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0d8c0840_0_6616a0d8c0840_0_: finished. Total: 1.5 s, CPU [user: 0.336 s, system: 0.0963 s], Allocated memory: 47.1 MB, svn: 0.867 s [57% getDatedRevision (181x), 26% getDir2 content (25x)] (307x), resolve: 0.434 s [100% Category (96x)] (96x), ObjectMaps: 0.155 s [41% getPrimaryObjectProperty (96x), 37% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x), GlobalHandler: 0.0835 s [80% applyTxChanges (2x), 20% get (98x)] (100x)
2025-07-30 16:46:58,579 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0da36c46_0_6616a0da36c46_0_: finished. Total: 0.311 s, CPU [user: 0.0295 s, system: 0.0136 s], Allocated memory: 2.0 MB, Lucene: 0.118 s [100% search (1x)] (1x), svn: 0.0769 s [63% getDir2 content (2x), 36% info (1x)] (4x)
2025-07-30 16:46:58,762 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0da84c47_0_6616a0da84c47_0_: finished. Total: 0.181 s, CPU [user: 0.0321 s, system: 0.0151 s], Allocated memory: 3.1 MB, resolve: 0.0999 s [100% Project (5x)] (5x), ObjectMaps: 0.0405 s [52% getPrimaryObjectProperty (4x), 24% getLastPromoted (4x), 24% getPrimaryObjectLocation (4x)] (17x), Lucene: 0.0389 s [100% search (1x)] (1x), svn: 0.0349 s [41% info (4x), 31% log (1x), 28% getFile content (4x)] (10x), GlobalHandler: 0.012 s [54% applyTxChanges (1x), 46% get (5x)] (6x)
2025-07-30 16:46:58,966 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0dab2848_0_6616a0dab2848_0_: finished. Total: 0.204 s, CPU [user: 0.0791 s, system: 0.0195 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0902 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), svn: 0.0791 s [50% info (19x), 44% getFile content (15x)] (36x), resolve: 0.0512 s [100% User (9x)] (9x), ObjectMaps: 0.0193 s [55% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x), 22% getLastPromoted (8x)] (32x), Lucene: 0.0155 s [100% search (1x)] (1x)
2025-07-30 16:46:59,350 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0dafa04a_0_6616a0dafa04a_0_: finished. Total: 0.301 s, CPU [user: 0.096 s, system: 0.0217 s], Allocated memory: 19.9 MB, svn: 0.209 s [63% getDir2 content (17x), 37% getFile content (44x)] (62x), RepositoryConfigService: 0.139 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 16:47:00,431 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0db4584b_0_6616a0db4584b_0_: finished. Total: 1.08 s, CPU [user: 0.438 s, system: 0.0883 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.835 s [93% getReadConfiguration (8682x)] (9021x), svn: 0.547 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-30 16:47:00,544 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0dc53c4c_0_6616a0dc53c4c_0_: finished. Total: 0.112 s, CPU [user: 0.023 s, system: 0.00276 s], Allocated memory: 17.8 MB, svn: 0.0991 s [82% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0268 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 16:47:01,011 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a0dc7b04e_0_6616a0dc7b04e_0_: finished. Total: 0.423 s, CPU [user: 0.14 s, system: 0.0113 s], Allocated memory: 384.3 MB, svn: 0.303 s [57% getDir2 content (21x), 43% getFile content (185x)] (207x), RepositoryConfigService: 0.228 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 16:47:01,011 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.24 s, CPU [user: 1.22 s, system: 0.276 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.28 s [38% getDir2 content (115x), 35% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.38 s [91% getReadConfiguration (12019x)] (12691x), resolve: 0.587 s [74% Category (96x), 17% Project (6x)] (117x), ObjectMaps: 0.215 s [44% getPrimaryObjectProperty (108x), 33% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 16:47:01,012 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.02 s [40% getDatedRevision (362x), 29% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.38 s [91% getReadConfiguration (12019x)] (12691x), resolve: 0.587 s [74% Category (96x), 17% Project (6x)] (118x), Lucene: 0.297 s [65% search (5x), 16% buildBaselineSnapshots (2x)] (54x), ObjectMaps: 0.215 s [44% getPrimaryObjectProperty (108x), 33% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 16:47:01,695 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a8374b7-7f000001-29ab89fc-e9c9c4ab] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.391 s, CPU [user: 0.233 s, system: 0.0429 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.334 s [100% authenticate (1x)] (1x)
2025-07-30 16:47:01,867 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a83766a-7f000001-29ab89fc-422a65bb] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.127 s, CPU [user: 0.023 s, system: 0.00536 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0573 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0331 s [100% readUserData (1x)] (1x), PolarionAuthenticator: 0.0118 s [100% authenticate (1x)] (1x)
2025-07-30 16:47:02,198 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a8377b1-7f000001-29ab89fc-9d711906] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.133 s, CPU [user: 0.0765 s, system: 0.0136 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 16:47:02,229 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a8377dd-7f000001-29ab89fc-c75846d7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753865222010': Total: 0.12 s, CPU [user: 0.0446 s, system: 0.0073 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 16:47:02,284 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a8377dd-7f000001-29ab89fc-e4800cd7 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753865222011] INFO  TXLOGGER - Tx 6616a0de08851_0_6616a0de08851_0_: finished. Total: 0.106 s, CPU [user: 0.06 s, system: 0.00989 s], Allocated memory: 5.6 MB, svn: 0.0111 s [60% getFile content (2x), 40% testConnection (1x)] (4x)
2025-07-30 16:47:02,323 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a8377dd-7f000001-29ab89fc-e4800cd7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753865222011': Total: 0.213 s, CPU [user: 0.0976 s, system: 0.0154 s], Allocated memory: 8.4 MB, transactions: 1, RepositoryConfigService: 0.107 s [100% getReadConfiguration (1x)] (1x), svn: 0.0111 s [60% getFile content (2x), 40% testConnection (1x)] (4x)
2025-07-30 16:47:02,351 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a8377dd-7f000001-29ab89fc-3be06737] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865222012': Total: 0.241 s, CPU [user: 0.0436 s, system: 0.00578 s], Allocated memory: 4.5 MB, transactions: 1, RepositoryConfigService: 0.157 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 16:48:59,017 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.11 s, CPU [user: 0.00217 s, system: 0.00214 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.109 s [100% collectChanges (1x)] (1x), svn: 0.109 s [100% getLatestRevision (1x)] (1x), GC: 0.093 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 16:50:29,160 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.134 s, CPU [user: 0.00235 s, system: 0.00436 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.11 s [100% collectChanges (1x)] (1x), svn: 0.109 s [100% getLatestRevision (1x)] (1x)
