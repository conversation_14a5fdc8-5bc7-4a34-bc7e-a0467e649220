2025-07-30 16:00:45,176 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:00:45,176 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:00:45,176 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:00:45,176 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:00:45,177 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:00:45,177 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:45,177 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:00:49,912 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:00:50,074 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.162 s. ]
2025-07-30 16:00:50,074 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:00:50,170 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0959 s. ]
2025-07-30 16:00:50,240 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:00:50,472 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 16:00:50,728 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.56 s. ]
2025-07-30 16:00:50,824 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:50,824 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:00:50,855 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 16:00:50,855 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:50,855 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 16:00:50,861 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 16:00:50,872 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:00:51,139 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:00:51,263 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:00:51,836 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.98 s. ]
2025-07-30 16:00:51,853 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:51,853 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:00:52,143 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:00:52,161 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 16:00:52,196 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:52,196 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:00:52,202 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:00:52,260 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:00:52,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:00:52,366 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:00:52,427 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:00:52,466 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:00:52,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:00:52,573 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:00:52,638 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:00:52,638 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.48 s. ]
2025-07-30 16:00:52,638 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:52,638 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:00:52,658 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 16:00:52,659 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:52,659 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:00:52,777 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:00:52,780 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:00:53,120 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.46 s. ]
2025-07-30 16:00:53,120 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:53,120 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:00:53,129 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 16:00:53,129 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:00:53,129 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:00:56,210 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.08 s. ]
2025-07-30 16:00:56,211 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:00:56,211 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11 s. ]
2025-07-30 16:00:56,211 [main] INFO  com.polarion.platform.startup - ****************************************************************
