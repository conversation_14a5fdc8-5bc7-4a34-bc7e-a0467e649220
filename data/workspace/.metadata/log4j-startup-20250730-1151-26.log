2025-07-30 11:51:26,124 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:51:26,124 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:51:26,124 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:51:26,124 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:51:26,124 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:51:26,125 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:26,125 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:51:30,277 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:51:30,416 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.139 s. ]
2025-07-30 11:51:30,417 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:51:30,493 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.076 s. ]
2025-07-30 11:51:30,558 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:51:30,657 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 48 s. ]
2025-07-30 11:51:30,865 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.75 s. ]
2025-07-30 11:51:30,945 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:30,945 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:51:30,970 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:51:30,970 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:30,970 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 11:51:30,975 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 11:51:30,983 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 11:51:31,105 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:51:31,221 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:51:31,679 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-30 11:51:31,690 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:31,690 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:51:31,890 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:51:31,899 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 11:51:31,923 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:31,923 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:51:31,926 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:51:31,982 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:51:32,023 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 11:51:32,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 11:51:32,099 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 11:51:32,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 11:51:32,137 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 11:51:32,178 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:51:32,203 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:51:32,203 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-07-30 11:51:32,203 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:32,203 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:51:32,217 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:51:32,231 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:32,231 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:51:32,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-07-30 11:51:32,348 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-07-30 11:51:32,436 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:51:32,437 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:51:32,509 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 11:51:32,509 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:32,509 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:51:32,516 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:51:32,516 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:32,516 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:51:34,712 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.2 s. ]
2025-07-30 11:51:34,713 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:51:34,713 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.59 s. ]
2025-07-30 11:51:34,713 [main] INFO  com.polarion.platform.startup - ****************************************************************
