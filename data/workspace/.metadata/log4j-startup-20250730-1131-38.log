2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:38,777 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:31:43,003 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:31:43,152 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-30 11:31:43,152 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:31:43,188 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0357 s. ]
2025-07-30 11:31:43,235 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:31:43,335 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 58 s. ]
2025-07-30 11:31:43,545 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.78 s. ]
2025-07-30 11:31:43,684 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:43,685 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:31:43,714 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-07-30 11:31:43,715 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:43,715 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:31:43,720 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 11:31:43,720 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 11:31:43,720 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-30 11:31:43,721 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 11:31:43,721 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 11:31:43,721 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:31:43,727 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:31:43,865 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:31:43,955 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:31:44,423 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-30 11:31:44,435 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:44,435 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:31:44,683 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:31:44,700 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-30 11:31:44,744 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:44,744 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:31:44,748 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:31:44,821 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:31:44,882 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:31:44,915 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:31:44,942 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:31:44,984 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:31:45,020 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:31:45,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:31:45,098 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:31:45,098 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.4 s. ]
2025-07-30 11:31:45,098 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:45,099 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:31:45,116 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:31:45,117 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:45,117 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:31:45,237 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:31:45,240 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:31:45,362 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 11:31:45,363 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:45,363 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:31:45,371 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:31:45,371 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:31:45,371 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:31:50,252 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.88 s. ]
2025-07-30 11:31:50,252 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:31:50,252 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.5 s. ]
2025-07-30 11:31:50,252 [main] INFO  com.polarion.platform.startup - ****************************************************************
