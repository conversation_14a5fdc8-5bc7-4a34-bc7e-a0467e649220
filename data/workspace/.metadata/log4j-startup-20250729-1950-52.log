2025-07-29 19:50:52,476 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:50:52,476 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:50:52,476 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:50:52,477 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:50:52,477 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:50:52,477 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:52,477 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:50:56,689 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:50:56,844 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.155 s. ]
2025-07-29 19:50:56,844 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:50:56,890 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0453 s. ]
2025-07-29 19:50:56,943 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:50:57,060 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 88 s. ]
2025-07-29 19:50:57,256 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.79 s. ]
2025-07-29 19:50:57,342 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:57,342 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:50:57,367 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 19:50:57,367 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:57,367 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 19:50:57,372 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-29 19:50:57,378 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:50:57,547 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:50:57,605 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:50:58,079 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-29 19:50:58,090 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,090 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:50:58,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:50:58,310 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 19:50:58,334 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,334 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:50:58,337 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:50:58,386 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:50:58,433 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:50:58,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:50:58,486 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:50:58,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:50:58,566 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:50:58,593 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:50:58,618 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:50:58,618 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-07-29 19:50:58,618 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,618 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:50:58,634 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 19:50:58,634 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,634 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:50:58,729 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:50:58,732 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:50:58,848 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-07-29 19:50:58,849 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,849 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:50:58,855 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:50:58,855 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:50:58,855 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:51:01,338 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.48 s. ]
2025-07-29 19:51:01,338 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:51:01,338 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.86 s. ]
2025-07-29 19:51:01,338 [main] INFO  com.polarion.platform.startup - ****************************************************************
