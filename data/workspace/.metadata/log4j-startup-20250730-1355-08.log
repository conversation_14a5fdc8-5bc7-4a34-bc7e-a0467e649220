2025-07-30 13:55:08,940 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:08,941 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:55:13,359 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:55:13,519 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-30 13:55:13,519 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:55:13,558 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0384 s. ]
2025-07-30 13:55:13,610 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:55:13,715 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 13:55:13,939 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.0 s. ]
2025-07-30 13:55:14,027 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:14,027 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:55:14,057 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 13:55:14,057 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:14,057 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 13:55:14,062 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 13:55:14,069 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:55:14,211 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:55:14,314 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:55:14,797 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 13:55:14,812 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:14,812 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:55:15,112 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:55:15,128 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 13:55:15,156 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:15,157 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:55:15,160 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:55:15,222 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:55:15,267 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:55:15,314 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:55:15,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:55:15,373 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:55:15,393 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:55:15,432 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:55:15,461 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:55:15,461 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.33 s. ]
2025-07-30 13:55:15,461 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:15,461 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:55:15,476 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 13:55:15,476 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:15,476 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:55:15,590 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:55:15,594 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:55:15,728 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 13:55:15,730 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:15,730 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:55:15,747 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 13:55:15,747 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:15,747 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:55:18,501 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.75 s. ]
2025-07-30 13:55:18,501 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:55:18,501 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.56 s. ]
2025-07-30 13:55:18,502 [main] INFO  com.polarion.platform.startup - ****************************************************************
