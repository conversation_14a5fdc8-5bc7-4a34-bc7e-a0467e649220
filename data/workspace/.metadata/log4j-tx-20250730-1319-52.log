2025-07-30 13:19:57,805 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0485 s [66% update (144x), 34% query (12x)] (221x), svn: 0.0143 s [65% getLatestRevision (2x), 25% testConnection (1x)] (4x)
2025-07-30 13:19:57,917 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0348 s [61% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.121 s, system: 0.201 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0673 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.253 s, system: 0.328 s], Allocated memory: 70.7 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0513 s, system: 0.0841 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0713 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.037 s [73% log2 (5x), 15% testConnection (1x)] (7x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.206 s, system: 0.27 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0855 s, system: 0.13 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0898 s [74% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0516 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:19:58,651 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.101 s, system: 0.148 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.129 s [42% log2 (10x), 24% info (5x), 14% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0844 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:19:58,651 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.515 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.322 s [59% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 13:19:58,783 [main | u:p] INFO  TXLOGGER - Tx 66167179b3c01_0_66167179b3c01_0_: finished. Total: 0.111 s, CPU [user: 0.0847 s, system: 0.00634 s], Allocated memory: 21.8 MB
2025-07-30 13:19:58,912 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.226 s [100% getReadConfiguration (48x)] (48x), svn: 0.0789 s [80% info (18x)] (38x)
2025-07-30 13:19:59,229 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.248 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.195 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:19:59,475 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.232 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0467 s [100% refresh (1x)] (1x), commit: 0.0453 s [100% Revision (1x)] (1x), DB: 0.0146 s [45% update (3x), 27% query (1x), 15% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0138 s [100% objectsToInv (1x)] (1x)
2025-07-30 13:20:01,967 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.364 s [91% info (158x)] (168x)
2025-07-30 13:20:01,973 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 3, persistence listener: 0.0123 s [84% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.00686 s [41% WorkItemActivityCreator (1x), 26% BuildActivityCreator (1x), 17% PlanActivityCreator (1x)] (4x)
2025-07-30 13:20:02,249 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616717cec844_0_6616717cec844_0_: finished. Total: 0.278 s, CPU [user: 0.156 s, system: 0.0187 s], Allocated memory: 20.7 MB, resolve: 0.0807 s [63% User (2x), 35% Project (1x)] (5x), Lucene: 0.0271 s [100% search (1x)] (1x), ObjectMaps: 0.0229 s [50% getPrimaryObjectProperty (2x), 40% getPrimaryObjectLocation (2x)] (11x), svn: 0.0227 s [32% log (1x), 28% getLatestRevision (2x), 19% testConnection (1x), 13% getFile content (2x)] (8x)
2025-07-30 13:20:02,770 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.706 s, CPU [user: 0.00656 s, system: 0.00128 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 13:20:02,771 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 25, notification worker: 0.283 s [100% RevisionActivityCreator (2x)] (2x), resolve: 0.097 s [67% User (3x), 29% Project (1x)] (7x), Lucene: 0.0529 s [51% search (1x), 42% add (1x)] (3x), svn: 0.0306 s [33% getLatestRevision (3x), 28% testConnection (2x), 24% log (1x)] (10x), ObjectMaps: 0.0274 s [50% getPrimaryObjectLocation (3x), 42% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0205 s [100% WorkItem (22x)] (22x)
2025-07-30 13:20:02,772 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.808 s, CPU [user: 0.152 s, system: 0.0237 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.631 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0415 s [72% buildBaselineSnapshots (1x), 28% buildBaseline (23x)] (24x)
2025-07-30 13:20:03,123 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616717cee045_0_6616717cee045_0_: finished. Total: 1.15 s, CPU [user: 0.308 s, system: 0.0792 s], Allocated memory: 47.1 MB, svn: 0.689 s [48% getDatedRevision (181x), 34% getDir2 content (25x)] (307x), resolve: 0.351 s [100% Category (96x)] (96x), ObjectMaps: 0.121 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 13:20:03,302 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616717e1b848_0_6616717e1b848_0_: finished. Total: 0.12 s, CPU [user: 0.0531 s, system: 0.00843 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0484 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (162x)] (172x), resolve: 0.046 s [100% User (9x)] (9x), svn: 0.0452 s [55% info (19x), 34% getFile content (15x)] (36x), ObjectMaps: 0.0181 s [61% getPrimaryObjectProperty (8x), 21% getLastPromoted (8x)] (32x), GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:20:03,528 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616717e4904a_0_6616717e4904a_0_: finished. Total: 0.164 s, CPU [user: 0.0586 s, system: 0.00822 s], Allocated memory: 19.9 MB, svn: 0.12 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.0621 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 13:20:04,191 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616717e7204b_0_6616717e7204b_0_: finished. Total: 0.662 s, CPU [user: 0.323 s, system: 0.0225 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.496 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.346 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x)
2025-07-30 13:20:04,536 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616717f3144e_0_6616717f3144e_0_: finished. Total: 0.243 s, CPU [user: 0.104 s, system: 0.00533 s], Allocated memory: 387.4 MB, RepositoryConfigService: 0.158 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.151 s [54% getFile content (185x), 46% getDir2 content (21x)] (207x)
2025-07-30 13:20:04,536 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.56 s, CPU [user: 0.93 s, system: 0.136 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.49 s [40% getDir2 content (115x), 34% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.836 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.419 s [84% Category (96x)] (117x), ObjectMaps: 0.146 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 13:20:04,536 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.12 s [45% getDatedRevision (362x), 28% getDir2 content (115x), 24% getFile content (807x)] (1324x), RepositoryConfigService: 0.836 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.419 s [84% Category (96x)] (118x), ObjectMaps: 0.146 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 13:20:23,642 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59c646b3-0a465820-007f3261-836d1bde] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.422 s, CPU [user: 0.23 s, system: 0.0591 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.373 s [100% authenticate (1x)] (1x)
2025-07-30 13:20:23,968 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59c64936-0a465820-007f3261-b9c22b3e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.106 s, CPU [user: 0.0131 s, system: 0.0039 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 13:20:23,969 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59c6491e-0a465820-007f3261-25c00ebe] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.13 s, CPU [user: 0.0618 s, system: 0.0119 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 13:20:23,987 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59c64934-0a465820-007f3261-c4e742a1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753852823804': Total: 0.127 s, CPU [user: 0.0325 s, system: 0.00528 s], Allocated memory: 2.2 MB, transactions: 0
2025-07-30 13:20:24,046 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59c64935-0a465820-007f3261-4dad25a7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753852823805': Total: 0.185 s, CPU [user: 0.0662 s, system: 0.0125 s], Allocated memory: 7.9 MB, transactions: 1, RepositoryConfigService: 0.0747 s [100% getReadConfiguration (1x)] (1x), svn: 0.0124 s [69% testConnection (1x), 31% getFile content (2x)] (4x)
2025-07-30 13:20:24,049 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59c64935-0a465820-007f3261-abb4b775] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753852823806': Total: 0.187 s, CPU [user: 0.0306 s, system: 0.00588 s], Allocated memory: 4.8 MB, transactions: 1, RepositoryConfigService: 0.0898 s [100% getReadConfiguration (1x)] (1x)
