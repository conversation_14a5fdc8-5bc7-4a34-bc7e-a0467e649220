2025-07-30 17:35:59,586 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0693 s [76% update (144x), 24% query (12x)] (221x), svn: 0.0116 s [48% getLatestRevision (2x), 32% testConnection (1x), 20% checkPath (1x)] (4x)
2025-07-30 17:35:59,697 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0344 s [58% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.722 s, CPU [user: 0.0797 s, system: 0.123 s], Allocated memory: 12.2 MB, transactions: 0, svn: 0.118 s [56% log2 (10x), 28% testConnection (1x)] (13x), ObjectMaps: 0.104 s [99% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.722 s, CPU [user: 0.188 s, system: 0.246 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.721 s, CPU [user: 0.0931 s, system: 0.109 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.189 s [66% log2 (10x), 11% info (5x), 9% log (1x)] (24x), ObjectMaps: 0.0582 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.722 s, CPU [user: 0.104 s, system: 0.18 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.721 s, CPU [user: 0.0474 s, system: 0.0809 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0689 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0547 s [69% log2 (5x), 15% testConnection (1x)] (7x)
2025-07-30 17:36:00,422 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.722 s, CPU [user: 0.237 s, system: 0.309 s], Allocated memory: 70.7 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:36:00,423 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.573 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.436 s [62% log2 (36x), 15% testConnection (6x), 13% getLatestRevision (9x)] (61x)
2025-07-30 17:36:00,645 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.192 s [100% getReadConfiguration (48x)] (48x), svn: 0.0712 s [86% info (18x)] (38x)
2025-07-30 17:36:01,033 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.311 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.221 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:36:01,248 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.108 s, CPU [user: 0.0239 s, system: 0.00776 s], Allocated memory: 10.5 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:36:01,296 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.249 s [100% doFinishStartup (1x)] (1x), commit: 0.0711 s [100% Revision (1x)] (1x), Lucene: 0.04 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0153 s [100% objectsToInv (1x)] (1x), DB: 0.0135 s [38% update (3x), 29% execute (1x), 23% query (1x)] (8x)
2025-07-30 17:36:03,749 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.295 s [90% info (158x)] (168x), RepositoryConfigService: 0.0156 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 17:36:03,985 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616ac16ad045_0_6616ac16ad045_0_: finished. Total: 0.22 s, CPU [user: 0.12 s, system: 0.0146 s], Allocated memory: 20.2 MB, resolve: 0.0718 s [66% User (2x), 32% Project (1x)] (5x), svn: 0.0211 s [37% getLatestRevision (2x), 31% testConnection (1x), 12% log (1x)] (8x), Lucene: 0.0188 s [100% search (1x)] (1x), ObjectMaps: 0.0183 s [60% getPrimaryObjectLocation (2x), 32% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 17:36:04,584 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.761 s, CPU [user: 0.00523 s, system: 0.00104 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 17:36:04,585 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.228 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.0879 s [70% User (3x), 26% Project (1x)] (7x), Lucene: 0.0323 s [58% search (1x), 25% add (1x)] (3x), svn: 0.0299 s [40% getLatestRevision (3x), 37% testConnection (2x), 9% log (1x)] (10x), ObjectMaps: 0.0229 s [68% getPrimaryObjectLocation (3x), 25% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0224 s [78% indexRefreshPersistenceListener (1x), 14% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0201 s [100% WorkItem (22x)] (22x)
2025-07-30 17:36:04,586 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.842 s, CPU [user: 0.161 s, system: 0.0243 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.676 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0491 s [79% buildBaselineSnapshots (1x), 21% buildBaseline (23x)] (24x)
2025-07-30 17:36:04,998 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ac16ab440_0_6616ac16ab440_0_: finished. Total: 1.24 s, CPU [user: 0.318 s, system: 0.0815 s], Allocated memory: 46.6 MB, svn: 0.737 s [52% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.377 s [100% Category (96x)] (96x), ObjectMaps: 0.128 s [44% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (387x)
2025-07-30 17:36:05,382 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ac181384a_0_6616ac181384a_0_: finished. Total: 0.183 s, CPU [user: 0.0577 s, system: 0.0075 s], Allocated memory: 19.9 MB, svn: 0.149 s [79% getDir2 content (17x), 21% getFile content (44x)] (62x), RepositoryConfigService: 0.0494 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:36:06,084 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ac184184b_0_6616ac184184b_0_: finished. Total: 0.702 s, CPU [user: 0.342 s, system: 0.0193 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.54 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.356 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-30 17:36:06,454 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ac190e44e_0_6616ac190e44e_0_: finished. Total: 0.252 s, CPU [user: 0.108 s, system: 0.00581 s], Allocated memory: 384.3 MB, svn: 0.159 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x), RepositoryConfigService: 0.156 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 17:36:06,454 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.7 s, CPU [user: 0.951 s, system: 0.133 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.58 s [39% getDir2 content (115x), 34% getFile content (807x), 24% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.854 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.428 s [88% Category (96x)] (117x), ObjectMaps: 0.148 s [45% getPrimaryObjectProperty (108x), 34% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 17:36:06,454 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.26 s [47% getDatedRevision (362x), 27% getDir2 content (115x), 24% getFile content (807x)] (1324x), RepositoryConfigService: 0.854 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.428 s [88% Category (96x)] (118x), ObjectMaps: 0.148 s [45% getPrimaryObjectProperty (108x), 34% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 17:36:29,041 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5ab0bb93-7f000001-69b62da1-24093562] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.477 s, CPU [user: 0.228 s, system: 0.0717 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.41 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0306 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 17:36:29,222 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5ab0bda7-7f000001-69b62da1-a08f6bb4] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.126 s, CPU [user: 0.0224 s, system: 0.00699 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0438 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0242 s [100% readUserData (1x)] (1x)
2025-07-30 17:36:29,545 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5ab0bec6-7f000001-69b62da1-abe5fcab] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.163 s, CPU [user: 0.081 s, system: 0.0182 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 17:36:29,545 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5ab0bee7-7f000001-69b62da1-4ba7a25c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.13 s, CPU [user: 0.0139 s, system: 0.00485 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 17:36:29,582 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5ab0bee7-7f000001-69b62da1-ea04b67a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753868189340': Total: 0.166 s, CPU [user: 0.0389 s, system: 0.00802 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 17:36:29,706 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5ab0bee7-7f000001-69b62da1-31894706 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753868189341] INFO  TXLOGGER - Tx 6616ac2fd5451_0_6616ac2fd5451_0_: finished. Total: 0.18 s, CPU [user: 0.0533 s, system: 0.0141 s], Allocated memory: 5.6 MB, GC: 0.076 s [100% G1 Young Generation (1x)] (1x), svn: 0.012 s [58% testConnection (1x), 42% getFile content (2x)] (4x)
2025-07-30 17:36:29,723 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5ab0bee7-7f000001-69b62da1-31894706] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753868189341': Total: 0.307 s, CPU [user: 0.0793 s, system: 0.0195 s], Allocated memory: 9.0 MB, transactions: 1, RepositoryConfigService: 0.182 s [100% getReadConfiguration (1x)] (1x), GC: 0.076 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:36:29,723 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5ab0bee7-7f000001-69b62da1-f5101ad7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753868189342': Total: 0.308 s, CPU [user: 0.0243 s, system: 0.00427 s], Allocated memory: 4.6 MB, transactions: 1, RepositoryConfigService: 0.196 s [100% getReadConfiguration (1x)] (1x), GC: 0.076 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:37:08,079 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5ab0f6e5-7f000001-69b62da1-91525ef6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 24.3 s, CPU [user: 1.77 s, system: 0.0497 s], Allocated memory: 6.9 MB, transactions: 0
2025-07-30 17:39:34,512 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5ab15fc4-7f000001-69b62da1-15ce8db0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 144 s, CPU [user: 0.117 s, system: 0.0262 s], Allocated memory: 1.0 MB, transactions: 0
