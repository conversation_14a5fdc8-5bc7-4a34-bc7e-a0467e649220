2025-07-30 10:40:06,154 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:40:06,154 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:40:06,154 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:40:06,155 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:40:06,155 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:40:06,155 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:06,155 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:40:11,287 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:40:11,459 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.171 s. ]
2025-07-30 10:40:11,459 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:40:11,504 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0446 s. ]
2025-07-30 10:40:11,573 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:40:11,706 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 102 s. ]
2025-07-30 10:40:11,926 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.78 s. ]
2025-07-30 10:40:12,010 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:12,010 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:40:12,045 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 10:40:12,046 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:12,046 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:40:12,052 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 10:40:12,053 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 10:40:12,052 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-30 10:40:12,052 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 10:40:12,052 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 10:40:12,052 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 10:40:12,060 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:40:12,216 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:40:12,306 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:40:12,813 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 10:40:12,826 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:12,826 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:40:13,120 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:40:13,133 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 10:40:13,191 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:13,191 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:40:13,196 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:40:13,293 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:40:13,360 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:40:13,397 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:40:13,430 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:40:13,481 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:40:13,535 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:40:13,588 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:40:13,633 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:40:13,633 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.5 s. ]
2025-07-30 10:40:13,633 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:13,633 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:40:13,647 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 10:40:13,661 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:13,661 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:40:13,789 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-07-30 10:40:13,812 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-07-30 10:40:13,915 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:40:13,916 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:40:14,024 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.38 s. ]
2025-07-30 10:40:14,025 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:14,025 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:40:14,038 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:40:14,038 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:40:14,038 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:40:17,512 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.47 s. ]
2025-07-30 10:40:17,514 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:40:17,514 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.4 s. ]
2025-07-30 10:40:17,515 [main] INFO  com.polarion.platform.startup - ****************************************************************
