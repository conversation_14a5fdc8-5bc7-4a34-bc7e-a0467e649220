2025-07-30 15:41:50,898 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.191 s [80% update (144x), 20% query (12x)] (221x), svn: 0.0219 s [55% getLatestRevision (2x), 24% testConnection (1x), 21% checkPath (1x)] (4x)
2025-07-30 15:41:51,110 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.11 s [78% getDir2 content (2x), 18% info (3x)] (6x)
2025-07-30 15:41:52,103 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.977 s, CPU [user: 0.092 s, system: 0.0805 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.117 s [80% log2 (10x)] (13x)
2025-07-30 15:41:52,103 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.977 s, CPU [user: 0.232 s, system: 0.27 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:41:52,103 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.977 s, CPU [user: 0.138 s, system: 0.197 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.114 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:41:52,103 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.978 s, CPU [user: 0.278 s, system: 0.332 s], Allocated memory: 70.0 MB, transactions: 0, ObjectMaps: 0.133 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:41:52,104 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.977 s, CPU [user: 0.0595 s, system: 0.0779 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0763 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0584 s [82% log2 (5x)] (7x)
2025-07-30 15:41:52,104 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.977 s, CPU [user: 0.124 s, system: 0.134 s], Allocated memory: 14.7 MB, transactions: 0, svn: 0.152 s [52% log2 (10x), 14% log (1x), 12% info (5x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.104 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:41:52,105 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.603 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.418 s [67% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 15:41:52,237 [main | u:p] INFO  TXLOGGER - Tx 661691f3a1001_0_661691f3a1001_0_: finished. Total: 0.104 s, CPU [user: 0.0752 s, system: 0.00702 s], Allocated memory: 21.8 MB
2025-07-30 15:41:52,433 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.286 s [100% getReadConfiguration (48x)] (48x), svn: 0.0957 s [84% info (18x)] (38x)
2025-07-30 15:41:52,854 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.337 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.256 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:41:53,127 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.0402 s, system: 0.0118 s], Allocated memory: 11.0 MB
2025-07-30 15:41:53,162 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.286 s [100% doFinishStartup (1x)] (1x), commit: 0.0559 s [100% Revision (1x)] (1x), Lucene: 0.0389 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0177 s [100% objectsToInv (1x)] (1x), DB: 0.0153 s [49% update (3x), 25% execute (1x), 18% query (1x)] (8x)
2025-07-30 15:41:56,488 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.418 s [90% info (158x)] (170x)
2025-07-30 15:41:56,939 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661691f7e7044_0_661691f7e7044_0_: finished. Total: 0.429 s, CPU [user: 0.158 s, system: 0.0252 s], Allocated memory: 20.3 MB, resolve: 0.133 s [64% User (2x), 33% Project (1x)] (5x), Lucene: 0.0691 s [100% search (1x)] (1x), ObjectMaps: 0.0403 s [45% getPrimaryObjectLocation (2x), 39% getPrimaryObjectProperty (2x)] (11x), svn: 0.0343 s [31% getLatestRevision (2x), 28% log (1x), 22% testConnection (1x)] (8x)
2025-07-30 15:41:57,705 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.00738 s, system: 0.0022 s], Allocated memory: 315.2 kB, transactions: 1
2025-07-30 15:41:57,706 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.475 s [94% RevisionActivityCreator (2x)] (6x), resolve: 0.168 s [68% User (3x), 26% Project (1x)] (7x), Lucene: 0.0993 s [70% search (1x), 23% add (2x)] (4x), ObjectMaps: 0.0453 s [51% getPrimaryObjectLocation (3x), 35% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0381 s [87% indexRefreshPersistenceListener (1x)] (7x), svn: 0.0343 s [31% getLatestRevision (2x), 28% log (1x), 22% testConnection (1x)] (8x), Incremental Baseline: 0.0247 s [100% WorkItem (22x)] (22x)
2025-07-30 15:41:57,707 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.23 s, CPU [user: 0.182 s, system: 0.0308 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.926 s [99% getDatedRevision (181x)] (183x)
2025-07-30 15:41:58,291 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691f7e8045_0_661691f7e8045_0_: finished. Total: 1.78 s, CPU [user: 0.4 s, system: 0.0996 s], Allocated memory: 47.1 MB, svn: 1.06 s [52% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.542 s [100% Category (96x)] (96x), ObjectMaps: 0.175 s [43% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 15:41:58,564 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691f9bec48_0_661691f9bec48_0_: finished. Total: 0.168 s, CPU [user: 0.0767 s, system: 0.014 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.0737 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (162x)] (172x), svn: 0.0662 s [57% info (19x), 36% getFile content (15x)] (36x), resolve: 0.0441 s [100% User (9x)] (9x), ObjectMaps: 0.0173 s [55% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 15:41:58,885 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691f9f704a_0_661691f9f704a_0_: finished. Total: 0.264 s, CPU [user: 0.0714 s, system: 0.00815 s], Allocated memory: 19.9 MB, svn: 0.225 s [73% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.0829 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 15:42:00,226 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691fa3944b_0_661691fa3944b_0_: finished. Total: 1.34 s, CPU [user: 0.558 s, system: 0.113 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.06 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.6 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-30 15:42:00,381 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691fb8884c_0_661691fb8884c_0_: finished. Total: 0.154 s, CPU [user: 0.0341 s, system: 0.00447 s], Allocated memory: 17.8 MB, svn: 0.138 s [82% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0355 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 15:42:00,953 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661691fbc084e_0_661691fbc084e_0_: finished. Total: 0.502 s, CPU [user: 0.198 s, system: 0.0155 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.327 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.325 s [54% getFile content (185x), 46% getDir2 content (21x)] (207x)
2025-07-30 15:42:00,953 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.45 s, CPU [user: 1.43 s, system: 0.269 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.54 s [40% getDir2 content (115x), 35% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.64 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.627 s [86% Category (96x)] (117x)
2025-07-30 15:42:00,953 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.47 s [42% getDatedRevision (362x), 29% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.64 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.627 s [86% Category (96x)] (118x), ObjectMaps: 0.205 s [45% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 15:42:05,794 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5a480119-7f000001-68d9c2c3-8b0396f2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.712 s, CPU [user: 0.294 s, system: 0.0855 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.488 s [100% authenticate (1x)] (1x)
2025-07-30 15:42:05,950 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a480412-7f000001-68d9c2c3-0e76237e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.107 s, CPU [user: 0.0219 s, system: 0.00688 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0617 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0414 s [100% readUserData (1x)] (1x)
2025-07-30 15:42:06,300 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5a480557-7f000001-68d9c2c3-5d5b7ca9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.13 s, CPU [user: 0.0164 s, system: 0.00465 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 15:42:06,301 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a480531-7f000001-68d9c2c3-c4fc21a6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.168 s, CPU [user: 0.079 s, system: 0.0173 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 15:42:06,340 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a480557-7f000001-68d9c2c3-809be01c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753861326083': Total: 0.172 s, CPU [user: 0.0494 s, system: 0.00865 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 15:42:06,379 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a480557-7f000001-68d9c2c3-7660df6b | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753861326085] INFO  TXLOGGER - Tx 6616920171051_0_6616920171051_0_: finished. Total: 0.103 s, CPU [user: 0.0502 s, system: 0.00993 s], Allocated memory: 5.6 MB, svn: 0.011 s [55% getFile content (2x), 45% testConnection (1x)] (4x)
2025-07-30 15:42:06,391 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a480557-7f000001-68d9c2c3-7660df6b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753861326085': Total: 0.224 s, CPU [user: 0.0708 s, system: 0.0146 s], Allocated memory: 7.7 MB, transactions: 1, RepositoryConfigService: 0.103 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 15:42:06,432 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a480557-7f000001-68d9c2c3-7e576cf1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753861326084': Total: 0.265 s, CPU [user: 0.0509 s, system: 0.00913 s], Allocated memory: 5.9 MB, transactions: 1, RepositoryConfigService: 0.132 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 15:45:47,657 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.195 s, CPU [user: 0.00259 s, system: 0.00411 s], Allocated memory: 130.6 kB, transactions: 0, PullingJob: 0.181 s [100% collectChanges (1x)] (1x), svn: 0.181 s [100% getLatestRevision (1x)] (1x)
