2025-07-30 18:00:44,784 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:00:44,784 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:00:44,784 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:00:44,785 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:00:44,785 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:00:44,785 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:44,785 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:00:49,444 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:00:49,576 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.132 s. ]
2025-07-30 18:00:49,576 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:00:49,625 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0483 s. ]
2025-07-30 18:00:49,690 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:00:49,839 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-07-30 18:00:50,069 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.29 s. ]
2025-07-30 18:00:50,157 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:50,157 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:00:50,187 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 18:00:50,188 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:50,188 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 18:00:50,194 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 18:00:50,201 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:00:50,369 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:00:50,476 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:00:50,963 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-07-30 18:00:50,976 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:50,976 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:00:51,201 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:00:51,215 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 18:00:51,248 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:51,248 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:00:51,255 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:00:51,312 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:00:51,358 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:00:51,402 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:00:51,449 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:00:51,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:00:51,484 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:00:51,512 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:00:51,535 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:00:51,535 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-30 18:00:51,536 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:51,536 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:00:51,552 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 18:00:51,552 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:51,552 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:00:51,663 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:00:51,666 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:00:51,841 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 18:00:51,842 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:51,842 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:00:51,857 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 18:00:51,857 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:00:51,857 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
