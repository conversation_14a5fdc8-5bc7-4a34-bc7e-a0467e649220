2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:47,343 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:34:51,614 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:34:51,764 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-30 17:34:51,764 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:34:51,853 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0893 s. ]
2025-07-30 17:34:51,932 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:34:52,061 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:34:52,288 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.95 s. ]
2025-07-30 17:34:52,379 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:52,379 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:34:52,411 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 17:34:52,412 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:52,412 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 17:34:52,417 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 17:34:52,424 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:34:52,569 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:34:52,673 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:34:53,209 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.8 s. ]
2025-07-30 17:34:53,237 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:53,237 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:34:53,501 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:34:53,514 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.31 s. ]
2025-07-30 17:34:53,543 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:53,543 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:34:53,547 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:34:53,599 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:34:53,643 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:34:53,690 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:34:53,743 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:34:53,769 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:34:53,795 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:34:53,845 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:34:53,879 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:34:53,879 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-07-30 17:34:53,879 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:53,879 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:34:53,893 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:34:53,893 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:53,893 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:34:54,051 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:34:54,056 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:34:54,338 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.44 s. ]
2025-07-30 17:34:54,339 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:54,339 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:34:54,347 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:34:54,347 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:34:54,347 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:34:56,722 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.38 s. ]
2025-07-30 17:34:56,722 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:34:56,722 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.38 s. ]
2025-07-30 17:34:56,722 [main] INFO  com.polarion.platform.startup - ****************************************************************
