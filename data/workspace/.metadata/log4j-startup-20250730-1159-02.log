2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:02,464 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:59:07,107 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:59:07,272 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.164 s. ]
2025-07-30 11:59:07,272 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:59:07,368 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0966 s. ]
2025-07-30 11:59:07,428 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:59:07,545 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 11:59:07,754 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.3 s. ]
2025-07-30 11:59:07,850 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:07,850 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:59:07,878 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 11:59:07,878 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:07,878 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 11:59:07,884 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:59:07,899 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 11:59:08,070 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:59:08,170 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:59:08,638 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-30 11:59:08,649 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:08,649 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:59:08,885 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:59:08,900 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 11:59:08,944 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:08,944 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:59:08,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:59:09,009 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:59:09,072 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 11:59:09,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 11:59:09,199 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 11:59:09,239 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 11:59:09,271 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 11:59:09,338 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:59:09,400 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:59:09,400 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.5 s. ]
2025-07-30 11:59:09,400 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:09,400 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:59:09,418 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:59:09,418 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:09,419 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:59:09,560 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:59:09,563 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:59:09,693 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-30 11:59:09,694 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:09,694 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:59:09,709 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 11:59:09,710 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:59:09,710 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:59:12,395 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.69 s. ]
2025-07-30 11:59:12,396 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:59:12,396 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.93 s. ]
2025-07-30 11:59:12,396 [main] INFO  com.polarion.platform.startup - ****************************************************************
