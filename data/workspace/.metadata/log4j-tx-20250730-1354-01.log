2025-07-30 13:54:07,024 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0765 s [75% update (144x), 25% query (12x)] (221x), svn: 0.0123 s [59% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-30 13:54:07,175 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.034 s [56% getDir2 content (2x), 35% info (3x)] (6x)
2025-07-30 13:54:07,972 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.0854 s, system: 0.0987 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.104 s [78% log2 (10x), 14% getLatestRevision (2x)] (13x), ObjectMaps: 0.0501 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:54:07,971 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.794 s, CPU [user: 0.139 s, system: 0.19 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0814 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:54:07,972 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.077 s, system: 0.0784 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0883 s [34% log2 (5x), 25% log (1x), 22% info (5x)] (18x), ObjectMaps: 0.0652 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:54:07,972 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.291 s, system: 0.323 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.151 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:54:07,972 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.219 s, system: 0.25 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:54:07,972 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.793 s, CPU [user: 0.0918 s, system: 0.118 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0783 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0634 s [77% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-30 13:54:07,973 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.538 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.332 s [63% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 13:54:08,116 [main | u:p] INFO  TXLOGGER - Tx 6616794affc01_0_6616794affc01_0_: finished. Total: 0.116 s, CPU [user: 0.0881 s, system: 0.00648 s], Allocated memory: 21.8 MB
2025-07-30 13:54:08,271 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.253 s [100% getReadConfiguration (48x)] (48x), svn: 0.0966 s [79% info (18x), 8% getLatestRevision (1x)] (38x)
2025-07-30 13:54:08,723 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.366 s [73% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.275 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:54:08,975 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.131 s, CPU [user: 0.0309 s, system: 0.0099 s], Allocated memory: 10.8 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:54:09,021 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.278 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0605 s [100% refresh (1x)] (1x), commit: 0.0581 s [100% Revision (1x)] (1x), DB: 0.0252 s [71% update (3x), 11% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0203 s [100% objectsToInv (1x)] (1x)
2025-07-30 13:54:11,731 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.359 s [90% info (158x)] (168x)
2025-07-30 13:54:12,242 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616794ea8844_0_6616794ea8844_0_: finished. Total: 0.496 s, CPU [user: 0.166 s, system: 0.0264 s], Allocated memory: 21.6 MB, resolve: 0.169 s [75% User (2x), 24% Project (1x)] (5x), svn: 0.0604 s [67% getLatestRevision (2x), 15% testConnection (1x)] (8x), Lucene: 0.0455 s [100% search (1x)] (1x), ObjectMaps: 0.0447 s [51% getPrimaryObjectProperty (2x), 40% getPrimaryObjectLocation (2x)] (11x), GC: 0.044 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:54:12,849 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.987 s, CPU [user: 0.0076 s, system: 0.00156 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 13:54:12,850 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.513 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.202 s [77% User (3x), 20% Project (1x)] (7x), svn: 0.0889 s [72% getLatestRevision (3x), 16% testConnection (2x)] (10x), Lucene: 0.0799 s [57% search (1x), 30% add (1x)] (3x), ObjectMaps: 0.0569 s [53% getPrimaryObjectLocation (3x), 40% getPrimaryObjectProperty (2x)] (12x), PullingJob: 0.0286 s [100% collectChanges (1x)] (1x)
2025-07-30 13:54:12,851 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.12 s, CPU [user: 0.18 s, system: 0.0306 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.803 s [99% getDatedRevision (181x)] (183x), Lucene: 0.113 s [87% buildBaselineSnapshots (1x)] (24x)
2025-07-30 13:54:13,369 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616794ea7440_0_6616794ea7440_0_: finished. Total: 1.63 s, CPU [user: 0.395 s, system: 0.104 s], Allocated memory: 46.6 MB, svn: 1.04 s [47% getDatedRevision (181x), 40% getDir2 content (25x)] (307x), resolve: 0.442 s [100% Category (96x)] (96x), ObjectMaps: 0.163 s [40% getPrimaryObjectProperty (96x), 39% getPrimaryObjectLocation (96x), 20% getLastPromoted (96x)] (387x)
2025-07-30 13:54:13,723 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679505c448_0_661679505c448_0_: finished. Total: 0.234 s, CPU [user: 0.0782 s, system: 0.016 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0974 s [54% getReadUserConfiguration (10x), 46% getReadConfiguration (162x)] (172x), svn: 0.0945 s [53% info (19x), 37% getFile content (15x)] (36x), resolve: 0.052 s [100% User (9x)] (9x), Lucene: 0.0379 s [100% search (1x)] (1x), ObjectMaps: 0.0262 s [68% getPrimaryObjectProperty (8x), 16% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 13:54:14,291 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167950a744a_0_66167950a744a_0_: finished. Total: 0.502 s, CPU [user: 0.105 s, system: 0.0116 s], Allocated memory: 19.9 MB, RepositoryConfigService: 0.333 s [99% getReadConfiguration (170x)] (192x), svn: 0.298 s [55% getFile content (44x), 45% getDir2 content (17x)] (62x)
2025-07-30 13:54:15,999 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616795124c4b_0_6616795124c4b_0_: finished. Total: 1.71 s, CPU [user: 0.696 s, system: 0.109 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.41 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.85 s [76% getFile content (412x), 24% getDir2 content (21x)] (434x)
2025-07-30 13:54:16,301 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167952d004c_0_66167952d004c_0_: finished. Total: 0.301 s, CPU [user: 0.0657 s, system: 0.0108 s], Allocated memory: 17.8 MB, svn: 0.248 s [79% getDir2 content (18x), 21% getFile content (29x)] (48x), RepositoryConfigService: 0.0964 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 13:54:17,071 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679533284e_0_661679533284e_0_: finished. Total: 0.676 s, CPU [user: 0.249 s, system: 0.0272 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.468 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.439 s [61% getFile content (185x), 39% getDir2 content (21x)] (207x)
2025-07-30 13:54:17,072 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.33 s, CPU [user: 1.69 s, system: 0.295 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.12 s [43% getFile content (807x), 39% getDir2 content (115x)] (1141x), RepositoryConfigService: 2.47 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.54 s [82% Category (96x)] (117x)
2025-07-30 13:54:17,072 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.93 s [34% getFile content (807x), 33% getDatedRevision (362x), 31% getDir2 content (115x)] (1325x), RepositoryConfigService: 2.47 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.541 s [82% Category (96x)] (118x), ObjectMaps: 0.207 s [45% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 13:54:40,926 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59e5aa8a-0a465820-31a57be9-d5bba428] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.531 s, CPU [user: 0.253 s, system: 0.0909 s], Allocated memory: 42.6 MB, transactions: 2, PolarionAuthenticator: 0.459 s [100% authenticate (1x)] (1x)
2025-07-30 13:54:41,147 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59e5acf2-0a465820-31a57be9-d94f5d62] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.136 s, CPU [user: 0.025 s, system: 0.00962 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0613 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0386 s [100% readUserData (1x)] (1x)
2025-07-30 13:54:41,505 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59e5ae4a-0a465820-31a57be9-27074b1a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.151 s, CPU [user: 0.098 s, system: 0.0229 s], Allocated memory: 10.0 MB, transactions: 0
2025-07-30 13:54:41,677 [ajp-nio-127.0.0.1-8889-exec-10 | cID:59e5af0b-0a465820-31a57be9-a2ddb49a | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753854881312] INFO  TXLOGGER - Tx 6616796bc7c51_0_6616796bc7c51_0_: finished. Total: 0.11 s, CPU [user: 0.0555 s, system: 0.0127 s], Allocated memory: 5.4 MB, svn: 0.0229 s [52% testConnection (1x), 48% getFile content (2x)] (4x)
2025-07-30 13:54:41,689 [ajp-nio-127.0.0.1-8889-exec-10 | cID:59e5af0b-0a465820-31a57be9-a2ddb49a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753854881312': Total: 0.142 s, CPU [user: 0.0671 s, system: 0.015 s], Allocated memory: 6.6 MB, transactions: 1, RepositoryConfigService: 0.111 s [100% getReadConfiguration (1x)] (1x), svn: 0.0229 s [52% testConnection (1x), 48% getFile content (2x)] (4x)
2025-07-30 13:54:41,731 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59e5af0b-0a465820-31a57be9-684e6835] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753854881311': Total: 0.184 s, CPU [user: 0.0453 s, system: 0.00566 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.141 s [100% getReadConfiguration (1x)] (1x)
