2025-07-30 10:48:47,845 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:47,845 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:48:47,846 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:47,846 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:48:47,846 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:48:47,846 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:47,846 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:48:52,401 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:48:52,579 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.177 s. ]
2025-07-30 10:48:52,579 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:48:52,648 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.069 s. ]
2025-07-30 10:48:52,725 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:48:53,158 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 83 s. ]
2025-07-30 10:48:53,454 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.61 s. ]
2025-07-30 10:48:53,550 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:53,550 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:48:53,579 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 10:48:53,580 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:53,580 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 10:48:53,587 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 10:48:53,593 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:48:53,731 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:48:53,850 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:48:54,487 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.91 s. ]
2025-07-30 10:48:54,500 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:54,500 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:48:54,876 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:48:54,894 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.41 s. ]
2025-07-30 10:48:54,931 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:54,931 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:48:54,935 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:48:54,996 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:48:55,058 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:48:55,089 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:48:55,115 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:48:55,156 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:48:55,192 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:48:55,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:48:55,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:48:55,318 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-30 10:48:55,318 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:55,318 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:48:55,335 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:48:55,335 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:55,336 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:48:55,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:48:55,474 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:48:55,595 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 10:48:55,595 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:55,595 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:48:55,602 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:48:55,603 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:55,603 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:48:59,142 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.54 s. ]
2025-07-30 10:48:59,142 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:59,142 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.3 s. ]
2025-07-30 10:48:59,142 [main] INFO  com.polarion.platform.startup - ****************************************************************
