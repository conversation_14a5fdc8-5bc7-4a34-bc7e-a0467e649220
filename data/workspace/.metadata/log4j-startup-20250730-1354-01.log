2025-07-30 13:54:01,264 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:54:01,264 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:54:01,264 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:54:01,264 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:54:01,264 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:54:01,265 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:01,265 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:54:06,349 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:54:06,508 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-07-30 13:54:06,509 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:54:06,592 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0828 s. ]
2025-07-30 13:54:06,686 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:54:06,806 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 13:54:07,023 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.77 s. ]
2025-07-30 13:54:07,144 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:07,144 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:54:07,175 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 13:54:07,175 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:07,175 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 13:54:07,180 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 13:54:07,187 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:54:07,338 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:54:07,403 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:54:07,972 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.8 s. ]
2025-07-30 13:54:07,988 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:07,988 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:54:08,253 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:54:08,271 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.3 s. ]
2025-07-30 13:54:08,299 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:08,299 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:54:08,302 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:54:08,367 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:54:08,421 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:54:08,481 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:54:08,533 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:54:08,568 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:54:08,605 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:54:08,664 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:54:08,723 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:54:08,723 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-07-30 13:54:08,723 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:08,723 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:54:08,742 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 13:54:08,742 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:08,743 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:54:08,853 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:54:08,856 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:54:09,021 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.28 s. ]
2025-07-30 13:54:09,022 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:09,022 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:54:09,034 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:54:09,034 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:54:09,034 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:54:11,731 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.7 s. ]
2025-07-30 13:54:11,731 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:54:11,731 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.5 s. ]
2025-07-30 13:54:11,731 [main] INFO  com.polarion.platform.startup - ****************************************************************
