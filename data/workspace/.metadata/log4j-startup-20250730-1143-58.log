2025-07-30 11:43:58,313 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:43:58,314 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:44:04,553 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:44:04,699 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.145 s. ]
2025-07-30 11:44:04,699 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:44:04,744 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0453 s. ]
2025-07-30 11:44:04,812 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:44:04,980 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 52 s. ]
2025-07-30 11:44:05,206 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.9 s. ]
2025-07-30 11:44:05,316 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:05,317 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:44:05,349 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-07-30 11:44:05,350 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:05,350 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 11:44:05,359 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 11:44:05,367 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:44:05,603 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:44:05,716 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:44:06,202 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.85 s. ]
2025-07-30 11:44:06,214 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:06,214 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:44:06,478 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:44:06,491 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-07-30 11:44:06,544 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:06,544 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:44:06,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:44:06,619 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:44:06,717 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:44:06,751 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:44:06,782 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:44:06,832 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:44:06,878 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:44:06,932 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:44:06,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:44:06,973 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.48 s. ]
2025-07-30 11:44:06,973 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:06,973 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:44:06,988 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:44:06,988 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:06,988 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:44:07,110 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:44:07,116 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:44:07,294 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.31 s. ]
2025-07-30 11:44:07,296 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:07,296 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:44:07,311 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 11:44:07,311 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:44:07,311 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:44:10,575 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.26 s. ]
2025-07-30 11:44:10,575 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:44:10,575 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.3 s. ]
2025-07-30 11:44:10,575 [main] INFO  com.polarion.platform.startup - ****************************************************************
