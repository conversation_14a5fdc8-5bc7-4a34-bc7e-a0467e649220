2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:28,633 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:05:32,881 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:05:33,041 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-30 17:05:33,041 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:05:33,100 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0586 s. ]
2025-07-30 17:05:33,185 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:05:33,314 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 17:05:33,525 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-07-30 17:05:33,615 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:33,615 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:05:33,643 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 17:05:33,643 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:33,643 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 17:05:33,650 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 17:05:33,659 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:05:33,807 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:05:33,911 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:05:34,399 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-30 17:05:34,410 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:34,410 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:05:34,625 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:05:34,640 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 17:05:34,673 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:34,673 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:05:34,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:05:34,740 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:05:34,782 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:05:34,826 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:05:34,876 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:05:34,901 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:05:34,922 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:05:34,958 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:05:34,997 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:05:34,997 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-07-30 17:05:34,997 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:34,997 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:05:35,012 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:05:35,012 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:35,012 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:05:35,120 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:05:35,123 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:05:35,264 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 17:05:35,265 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:35,265 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:05:35,273 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:05:35,273 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:05:35,273 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:05:37,747 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.47 s. ]
2025-07-30 17:05:37,747 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:05:37,747 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.11 s. ]
2025-07-30 17:05:37,747 [main] INFO  com.polarion.platform.startup - ****************************************************************
