2025-07-30 19:11:01,227 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0788 s [72% update (144x), 28% query (12x)] (221x), svn: 0.0171 s [67% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-07-30 19:11:01,338 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0297 s [61% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.204 s, system: 0.292 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.166 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.256 s, system: 0.35 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.136 s, system: 0.22 s], Allocated memory: 26.1 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0831 s [25% log2 (5x), 25% log (1x), 23% info (5x), 16% getLatestRevision (2x)] (18x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.0484 s, system: 0.0815 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0563 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.0883 s, system: 0.158 s], Allocated memory: 14.5 MB, transactions: 0, ObjectMaps: 0.0793 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0709 s [73% log2 (10x), 18% getLatestRevision (2x)] (13x)
2025-07-30 19:11:02,110 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.768 s, CPU [user: 0.0682 s, system: 0.0919 s], Allocated memory: 8.6 MB, transactions: 0, svn: 0.078 s [83% log2 (10x)] (13x), ObjectMaps: 0.064 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 19:11:02,110 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.595 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.303 s [61% log2 (36x), 15% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 19:11:02,331 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.19 s [100% getReadConfiguration (48x)] (48x), svn: 0.0751 s [85% info (18x)] (38x)
2025-07-30 19:11:02,678 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.264 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.199 s [100% getReadConfiguration (94x)] (94x)
2025-07-30 19:11:02,906 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.119 s, CPU [user: 0.0259 s, system: 0.00746 s], Allocated memory: 10.5 MB
2025-07-30 19:11:03,010 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.304 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0539 s [100% refresh (2x)] (2x), commit: 0.0521 s [67% Revision (1x), 33% BuildArtifact (1x)] (2x), DB: 0.046 s [48% update (45x), 21% execute (15x), 21% query (20x)] (122x), resolve: 0.0227 s [83% BuildArtifact (11x)] (13x), SubterraURITable: 0.022 s [100% addIfNotExistsDB (20x)] (20x), GlobalHandler: 0.0164 s [89% put (13x)] (26x)
2025-07-30 19:11:09,338 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.292 s [86% info (158x)] (171x)
2025-07-30 19:11:09,344 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, EHCache: 0.0000512 s [100% GET (1x)] (1x)
2025-07-30 19:11:09,621 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616c1da89042_0_6616c1da89042_0_: finished. Total: 0.272 s, CPU [user: 0.132 s, system: 0.0187 s], Allocated memory: 20.3 MB, resolve: 0.0917 s [61% User (2x), 36% Project (1x)] (5x), Lucene: 0.03 s [100% search (1x)] (1x), ObjectMaps: 0.029 s [47% getPrimaryObjectLocation (2x), 40% getPrimaryObjectProperty (2x)] (11x), svn: 0.0266 s [30% getLatestRevision (2x), 21% log (1x), 20% info (1x), 19% testConnection (1x)] (8x)
2025-07-30 19:11:10,137 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.697 s, CPU [user: 0.00496 s, system: 0.00149 s], Allocated memory: 501.1 kB, transactions: 1
2025-07-30 19:11:10,138 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.697 s, CPU [user: 0.00391 s, system: 0.00127 s], Allocated memory: 255.4 kB, transactions: 1
2025-07-30 19:11:10,138 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 69, notification worker: 0.291 s [96% RevisionActivityCreator (18x)] (54x), resolve: 0.104 s [64% User (3x), 32% Project (1x)] (7x), Lucene: 0.0477 s [63% search (1x), 19% add (1x)] (4x), ObjectMaps: 0.0322 s [53% getPrimaryObjectLocation (3x), 36% getPrimaryObjectProperty (2x)] (12x), svn: 0.0266 s [30% getLatestRevision (2x), 21% log (1x), 20% info (1x), 19% testConnection (1x)] (8x), Incremental Baseline: 0.0177 s [100% WorkItem (22x)] (22x)
2025-07-30 19:11:10,139 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.807 s, CPU [user: 0.151 s, system: 0.0216 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.613 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0494 s [82% buildBaselineSnapshots (1x)] (24x)
2025-07-30 19:11:10,489 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c1da89845_0_6616c1da89845_0_: finished. Total: 1.14 s, CPU [user: 0.288 s, system: 0.0747 s], Allocated memory: 47.1 MB, svn: 0.711 s [46% getDatedRevision (181x), 37% getDir2 content (25x)] (307x), resolve: 0.33 s [100% Category (96x)] (96x), ObjectMaps: 0.121 s [43% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 19:11:10,703 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c1dbb6870_0_6616c1dbb6870_0_: finished. Total: 0.149 s, CPU [user: 0.0536 s, system: 0.00992 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0646 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0606 s [55% info (19x), 39% getFile content (15x)] (36x), resolve: 0.0296 s [100% User (9x)] (9x), GC: 0.02 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0122 s [60% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x)] (32x)
2025-07-30 19:11:10,897 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c1dbe5c72_0_6616c1dbe5c72_0_: finished. Total: 0.154 s, CPU [user: 0.0504 s, system: 0.00693 s], Allocated memory: 19.9 MB, svn: 0.119 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.0557 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 19:11:11,621 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c1dc0c473_0_6616c1dc0c473_0_: finished. Total: 0.723 s, CPU [user: 0.324 s, system: 0.0281 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.52 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.387 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x)
2025-07-30 19:11:12,013 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c1dce1076_0_6616c1dce1076_0_: finished. Total: 0.264 s, CPU [user: 0.102 s, system: 0.00683 s], Allocated memory: 384.9 MB, svn: 0.171 s [53% getDir2 content (21x), 47% getFile content (185x)] (207x), RepositoryConfigService: 0.158 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 19:11:12,013 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.67 s, CPU [user: 0.898 s, system: 0.138 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.6 s [43% getDir2 content (115x), 32% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.864 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.388 s [85% Category (96x)] (117x), ObjectMaps: 0.141 s [45% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 19:11:12,013 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.21 s [42% getDatedRevision (362x), 31% getDir2 content (115x), 23% getFile content (807x)] (1324x), RepositoryConfigService: 0.864 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.388 s [85% Category (96x)] (118x), ObjectMaps: 0.141 s [45% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 19:11:29,931 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5b078971-0a465820-182261fc-0e6ffdcc] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/BubbleQueryService': Total: 12.6 s, CPU [user: 0.0749 s, system: 0.0214 s], Allocated memory: 8.0 MB, transactions: 2, PolarionAuthenticator: 12.5 s [100% authenticate (1x)] (1x), interceptor: 12.1 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 19:11:29,936 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5b07895f-0a465820-182261fc-b69916d5] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 12.6 s, CPU [user: 0.183 s, system: 0.0516 s], Allocated memory: 35.0 MB, transactions: 2, PolarionAuthenticator: 12.5 s [100% authenticate (1x)] (1x), interceptor: 12.1 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 19:11:29,936 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5b07895e-0a465820-182261fc-2bc7c486] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 12.6 s, CPU [user: 0.0903 s, system: 0.0469 s], Allocated memory: 9.6 MB, transactions: 2, PolarionAuthenticator: 12.5 s [100% authenticate (1x)] (1x), interceptor: 12.1 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 19:11:31,071 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5b07bd89-0a465820-182261fc-f0b1b911 | u:admin] INFO  TXLOGGER - Tx 6616c1ef63094_0_6616c1ef63094_0_: finished. Total: 0.371 s, CPU [user: 0.109 s, system: 0.0231 s], Allocated memory: 22.9 MB, resolve: 0.305 s [98% WorkItem (12x)] (15x), svn: 0.234 s [44% info (26x), 20% log (12x), 20% getDir2 content (12x)] (68x), ObjectMaps: 0.0746 s [80% getPrimaryObjectProperty (13x)] (66x), RepositoryConfigService: 0.0195 s [72% getReadConfiguration (425x), 28% getExistingPrefixes (24x)] (449x)
2025-07-30 19:11:31,087 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5b07bd89-0a465820-182261fc-f0b1b911] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.39 s, CPU [user: 0.122 s, system: 0.0273 s], Allocated memory: 26.8 MB, transactions: 1, WorkItemDataService: 0.372 s [100% loadInstances (1x)] (1x), resolve: 0.305 s [98% WorkItem (12x)] (15x), svn: 0.234 s [44% info (26x), 20% log (12x), 20% getDir2 content (12x)] (68x), ObjectMaps: 0.0746 s [80% getPrimaryObjectProperty (13x)] (66x)
