2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:51,016 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:26:55,418 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:26:55,582 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.163 s. ]
2025-07-29 19:26:55,582 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:26:55,657 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0752 s. ]
2025-07-29 19:26:55,718 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:26:55,851 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 86 s. ]
2025-07-29 19:26:56,075 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.07 s. ]
2025-07-29 19:26:56,173 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:56,173 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:26:56,206 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 19:26:56,207 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:56,207 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:26:56,214 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 19:26:56,215 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-29 19:26:56,214 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-29 19:26:56,215 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 19:26:56,214 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-29 19:26:56,215 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 19:26:56,221 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:26:56,397 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:26:56,491 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:26:57,029 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.82 s. ]
2025-07-29 19:26:57,042 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,042 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:26:57,268 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:26:57,281 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-29 19:26:57,306 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,307 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:26:57,310 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:26:57,366 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:26:57,419 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:26:57,451 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:26:57,475 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:26:57,519 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:26:57,564 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:26:57,606 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:26:57,655 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:26:57,655 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-07-29 19:26:57,655 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,655 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:26:57,671 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 19:26:57,671 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,671 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:26:57,780 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:26:57,782 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:26:57,941 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-29 19:26:57,942 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,942 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:26:57,951 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:26:57,951 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:26:57,951 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:27:00,688 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.74 s. ]
2025-07-29 19:27:00,688 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:27:00,688 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.67 s. ]
2025-07-29 19:27:00,688 [main] INFO  com.polarion.platform.startup - ****************************************************************
