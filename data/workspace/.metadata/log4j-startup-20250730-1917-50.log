2025-07-30 19:17:50,426 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:50,427 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 19:17:54,706 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 19:17:54,841 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.135 s. ]
2025-07-30 19:17:54,841 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 19:17:54,897 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0561 s. ]
2025-07-30 19:17:55,014 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 19:17:55,127 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-07-30 19:17:55,336 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.92 s. ]
2025-07-30 19:17:55,426 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:55,426 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 19:17:55,454 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 19:17:55,454 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:55,454 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 19:17:55,458 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 19:17:55,465 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 19:17:55,577 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-07-30 19:17:55,682 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-07-30 19:17:56,191 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 19:17:56,210 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:56,210 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 19:17:56,832 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 19:17:56,871 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.68 s. ]
2025-07-30 19:17:56,922 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:56,922 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 19:17:56,928 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 19:17:56,979 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 19:17:57,050 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-07-30 19:17:57,131 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 19:17:57,164 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-07-30 19:17:57,200 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-07-30 19:17:57,249 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 19:17:57,306 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-07-30 19:17:57,432 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-07-30 19:17:57,432 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.56 s. ]
2025-07-30 19:17:57,432 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:57,433 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 19:17:57,488 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.06 s. ]
2025-07-30 19:17:57,488 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:57,488 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 19:17:57,888 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 19:17:57,892 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 19:17:58,223 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.74 s. ]
2025-07-30 19:17:58,224 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:58,224 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 19:17:58,236 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 19:17:58,236 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:17:58,236 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 19:18:02,859 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.62 s. ]
2025-07-30 19:18:02,859 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:18:02,859 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.4 s. ]
2025-07-30 19:18:02,859 [main] INFO  com.polarion.platform.startup - ****************************************************************
