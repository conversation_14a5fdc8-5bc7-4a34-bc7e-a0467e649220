2025-07-30 15:13:20,877 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0928 s [72% update (144x), 28% query (12x)] (221x), svn: 0.0118 s [64% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-30 15:13:21,005 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0441 s [70% getDir2 content (2x), 25% info (3x)] (6x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.0946 s, system: 0.109 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.185 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.175 s [68% log2 (10x), 29% getLatestRevision (2x)] (13x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.27 s, system: 0.312 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.128 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.217 s, system: 0.249 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.0509 s, system: 0.0643 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0916 s [88% log2 (5x)] (7x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.145 s, system: 0.181 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.132 s [37% getLatestRevision (2x), 29% log2 (5x), 15% info (5x)] (18x), ObjectMaps: 0.0995 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:13:22,020 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.01 s, CPU [user: 0.0859 s, system: 0.0754 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.158 s [86% log2 (10x)] (13x), ObjectMaps: 0.127 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:13:22,021 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.813 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.596 s [66% log2 (36x), 22% getLatestRevision (9x)] (61x)
2025-07-30 15:13:22,251 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.193 s [100% getReadConfiguration (48x)] (48x), svn: 0.0786 s [82% info (18x)] (38x)
2025-07-30 15:13:22,631 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.306 s [73% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.229 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:13:22,899 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.127 s, CPU [user: 0.0291 s, system: 0.00743 s], Allocated memory: 10.5 MB
2025-07-30 15:13:22,976 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.108 s, CPU [user: 0.035 s, system: 0.00844 s], Allocated memory: 10.6 MB, commit: 0.105 s [100% Revision (1x)] (1x)
2025-07-30 15:13:22,986 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.341 s [100% doFinishStartup (1x)] (1x), commit: 0.105 s [100% Revision (1x)] (1x), Lucene: 0.0386 s [100% refresh (1x)] (1x), DB: 0.0254 s [54% query (1x), 25% update (3x), 14% execute (1x)] (8x), SubterraURITable: 0.0187 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-30 15:13:25,927 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.399 s [92% info (158x)] (168x)
2025-07-30 15:13:25,941 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, EHCache: 0.0000545 s [100% GET (2x)] (2x)
2025-07-30 15:13:26,174 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66168b716dc45_0_66168b716dc45_0_: finished. Total: 0.23 s, CPU [user: 0.126 s, system: 0.0162 s], Allocated memory: 20.3 MB, resolve: 0.0771 s [57% User (2x), 41% Project (1x)] (5x), Lucene: 0.03 s [100% search (1x)] (1x), svn: 0.0247 s [36% getLatestRevision (2x), 22% getFile content (2x), 20% log (1x), 16% testConnection (1x)] (8x), ObjectMaps: 0.019 s [51% getPrimaryObjectProperty (2x), 39% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 15:13:26,819 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.804 s, CPU [user: 0.00689 s, system: 0.00162 s], Allocated memory: 367.1 kB, transactions: 1
2025-07-30 15:13:26,820 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.249 s [96% RevisionActivityCreator (2x)] (6x), resolve: 0.127 s [69% User (4x), 25% Project (1x)] (8x), Lucene: 0.0447 s [67% search (1x), 24% add (1x)] (3x), svn: 0.0408 s [41% getLatestRevision (3x), 30% testConnection (2x), 13% getFile content (2x)] (10x), ObjectMaps: 0.0403 s [71% getPrimaryObjectLocation (4x), 24% getPrimaryObjectProperty (2x)] (13x), Incremental Baseline: 0.034 s [100% WorkItem (22x)] (22x), persistence listener: 0.02 s [73% indexRefreshPersistenceListener (1x), 16% WorkItemActivityCreator (1x)] (7x), PullingJob: 0.0163 s [100% collectChanges (1x)] (1x)
2025-07-30 15:13:26,821 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.811 s, CPU [user: 0.159 s, system: 0.0256 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.716 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0504 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 15:13:27,387 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b716b840_0_66168b716b840_0_: finished. Total: 1.45 s, CPU [user: 0.331 s, system: 0.0869 s], Allocated memory: 47.1 MB, svn: 0.875 s [61% getDatedRevision (181x), 25% getDir2 content (25x)] (307x), resolve: 0.344 s [100% Category (96x)] (96x), ObjectMaps: 0.122 s [43% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 15:13:27,499 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b72d7046_0_66168b72d7046_0_: finished. Total: 0.111 s, CPU [user: 0.0231 s, system: 0.00507 s], Allocated memory: 2.0 MB, svn: 0.0785 s [55% info (1x), 44% getDir2 content (2x)] (4x), Lucene: 0.0127 s [100% search (1x)] (1x)
2025-07-30 15:13:27,873 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b7305c48_0_66168b7305c48_0_: finished. Total: 0.297 s, CPU [user: 0.0816 s, system: 0.0225 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.105 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), resolve: 0.0998 s [100% User (9x)] (9x), svn: 0.0974 s [62% info (19x), 32% getFile content (15x)] (36x), ObjectMaps: 0.0387 s [47% getPrimaryObjectProperty (8x), 36% getLastPromoted (8x)] (32x)
2025-07-30 15:13:28,150 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b735dc4a_0_66168b735dc4a_0_: finished. Total: 0.222 s, CPU [user: 0.0632 s, system: 0.0104 s], Allocated memory: 19.9 MB, svn: 0.181 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.0677 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 15:13:29,079 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b739584b_0_66168b739584b_0_: finished. Total: 0.928 s, CPU [user: 0.403 s, system: 0.0345 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.705 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.519 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x)
2025-07-30 15:13:29,505 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168b749ac4e_0_66168b749ac4e_0_: finished. Total: 0.31 s, CPU [user: 0.123 s, system: 0.00831 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.211 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.203 s [60% getFile content (185x), 40% getDir2 content (21x)] (207x)
2025-07-30 15:13:29,505 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.57 s, CPU [user: 1.11 s, system: 0.184 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.11 s [35% getDir2 content (115x), 33% getFile content (807x), 25% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.15 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.503 s [68% Category (96x), 20% User (9x)] (117x), ObjectMaps: 0.181 s [45% getPrimaryObjectProperty (108x), 28% getPrimaryObjectLocation (114x), 27% getLastPromoted (108x)] (442x)
2025-07-30 15:13:29,505 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.83 s [44% getDatedRevision (362x), 26% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.15 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.503 s [68% Category (96x), 20% User (10x)] (118x), ObjectMaps: 0.181 s [45% getPrimaryObjectProperty (108x), 28% getPrimaryObjectLocation (114x), 27% getLastPromoted (108x)] (442x)
2025-07-30 15:14:17,764 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a2e8e1e-7f000001-3eff7ec9-1a1f8f5a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.517 s, CPU [user: 0.233 s, system: 0.0726 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.435 s [100% authenticate (1x)] (1x)
2025-07-30 15:14:18,104 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a2e910e-7f000001-3eff7ec9-14a13b55] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.106 s, CPU [user: 0.0687 s, system: 0.0144 s], Allocated memory: 10.0 MB, transactions: 0
2025-07-30 15:14:27,754 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a2eb686-7f000001-3eff7ec9-bf819306] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.161 s, CPU [user: 0.0706 s, system: 0.0157 s], Allocated memory: 7.7 MB, transactions: 0, RPC: 0.124 s [99% decodeRequest (1x)] (3x)
2025-07-30 15:14:28,023 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a2eb5ec-7f000001-3eff7ec9-5cfbac49] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.586 s, CPU [user: 0.273 s, system: 0.048 s], Allocated memory: 28.3 MB, transactions: 0, RPC: 0.553 s [100% decodeRequest (1x)] (3x), GC: 0.081 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:14:30,303 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a2eb8bd-7f000001-3eff7ec9-8e0daca1 | u:admin] INFO  TXLOGGER - Tx 66168bae32053_0_66168bae32053_0_: finished. Total: 2.13 s, CPU [user: 0.002 s, system: 0.000217 s], Allocated memory: 68.3 kB, rollback: true
2025-07-30 15:14:30,322 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a2eb8bd-7f000001-3eff7ec9-8e0daca1] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 2.16 s, CPU [user: 0.0134 s, system: 0.00318 s], Allocated memory: 1.4 MB, transactions: 1, WaitForConnection: 2.14 s [100% initialize (1x)] (1x)
