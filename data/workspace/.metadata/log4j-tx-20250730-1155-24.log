2025-07-30 11:55:29,261 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.051 s [67% update (144x), 33% query (12x)] (221x), svn: 0.0179 s [63% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 11:55:29,374 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0338 s [63% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.244 s, system: 0.296 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.196 s, system: 0.24 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.083 s, system: 0.112 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0837 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.076 s [72% log2 (10x), 20% getLatestRevision (2x)] (13x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.69 s, CPU [user: 0.135 s, system: 0.18 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.0825 s [27% log (1x), 27% log2 (5x), 21% info (5x), 13% getLatestRevision (2x)] (18x), ObjectMaps: 0.0623 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0511 s, system: 0.0832 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0559 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0348 s [73% log2 (5x), 15% getLatestRevision (1x)] (7x)
2025-07-30 11:55:30,067 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.073 s, system: 0.0904 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0963 s [75% log2 (10x), 18% getLatestRevision (2x)] (13x), ObjectMaps: 0.0481 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:55:30,068 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.48 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.326 s [60% log2 (36x), 17% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 11:55:30,300 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.199 s [100% getReadConfiguration (48x)] (48x), svn: 0.0804 s [88% info (18x)] (38x)
2025-07-30 11:55:30,596 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.228 s [74% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.177 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:55:30,805 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.102 s, CPU [user: 0.0185 s, system: 0.0036 s], Allocated memory: 2.4 MB
2025-07-30 11:55:30,811 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.107 s, CPU [user: 0.0036 s, system: 0.00166 s], Allocated memory: 811.0 kB
2025-07-30 11:55:30,813 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.11 s, CPU [user: 0.0229 s, system: 0.0044 s], Allocated memory: 2.5 MB
2025-07-30 11:55:30,853 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.149 s, CPU [user: 0.0268 s, system: 0.00671 s], Allocated memory: 10.5 MB
2025-07-30 11:55:30,898 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.287 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0816 s [100% refresh (1x)] (1x), commit: 0.062 s [100% Revision (1x)] (1x), DB: 0.0151 s [36% update (3x), 27% execute (1x), 20% commit (2x)] (8x)
2025-07-30 11:55:33,878 [main | u:p | u:p] INFO  TXLOGGER - Tx 66165e278103f_0_66165e278103f_0_: finished. Total: 0.113 s, CPU [user: 0.0423 s, system: 0.00345 s], Allocated memory: 7.5 MB, GlobalHandler: 0.0674 s [100% applyTxChanges (1x)] (4x), GC: 0.057 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:55:34,606 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.563 s [85% info (158x)] (170x), GlobalHandler: 0.0675 s [100% applyTxChanges (1x)] (7x)
2025-07-30 11:55:34,959 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165e2855845_0_66165e2855845_0_: finished. Total: 0.345 s, CPU [user: 0.157 s, system: 0.0229 s], Allocated memory: 20.7 MB, resolve: 0.112 s [68% User (2x), 31% Project (1x)] (5x), ObjectMaps: 0.0325 s [43% getPrimaryObjectLocation (2x), 42% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.029 s [100% search (1x)] (1x), svn: 0.0245 s [50% getLatestRevision (2x), 20% log (1x), 16% testConnection (1x)] (8x)
2025-07-30 11:55:35,549 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.834 s, CPU [user: 0.00574 s, system: 0.00124 s], Allocated memory: 315.4 kB, transactions: 1
2025-07-30 11:55:35,556 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.359 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.131 s [71% User (3x), 26% Project (1x)] (7x), ObjectMaps: 0.0403 s [54% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.037 s [78% search (1x), 22% refresh (1x)] (2x), Incremental Baseline: 0.0367 s [100% WorkItem (22x)] (22x), svn: 0.0245 s [50% getLatestRevision (2x), 20% log (1x), 16% testConnection (1x)] (8x)
2025-07-30 11:55:35,558 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.957 s, CPU [user: 0.156 s, system: 0.0292 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.74 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0523 s [74% buildBaselineSnapshots (1x), 26% buildBaseline (23x)] (24x)
2025-07-30 11:55:36,160 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165e2855444_0_66165e2855444_0_: finished. Total: 1.55 s, CPU [user: 0.358 s, system: 0.101 s], Allocated memory: 47.1 MB, svn: 0.991 s [59% getDatedRevision (181x), 27% getDir2 content (25x)] (307x), resolve: 0.414 s [100% Category (96x)] (96x), ObjectMaps: 0.146 s [46% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 11:55:36,404 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165e29ed448_0_66165e29ed448_0_: finished. Total: 0.159 s, CPU [user: 0.071 s, system: 0.0145 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.082 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.0778 s [57% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0411 s [100% User (9x)] (9x), ObjectMaps: 0.0173 s [62% getPrimaryObjectProperty (8x), 20% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:55:36,705 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165e2a2144a_0_66165e2a2144a_0_: finished. Total: 0.25 s, CPU [user: 0.0815 s, system: 0.0186 s], Allocated memory: 19.9 MB, svn: 0.187 s [66% getDir2 content (17x), 34% getFile content (44x)] (62x), RepositoryConfigService: 0.104 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 11:55:37,537 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165e2a6044b_0_66165e2a6044b_0_: finished. Total: 0.832 s, CPU [user: 0.348 s, system: 0.051 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.601 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.483 s [63% getFile content (412x), 37% getDir2 content (21x)] (434x), GC: 0.048 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 11:55:37,875 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165e2b4a44e_0_66165e2b4a44e_0_: finished. Total: 0.234 s, CPU [user: 0.096 s, system: 0.00517 s], Allocated memory: 384.7 MB, svn: 0.15 s [52% getDir2 content (21x), 48% getFile content (185x)] (207x), RepositoryConfigService: 0.141 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 11:55:37,876 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.26 s, CPU [user: 1.04 s, system: 0.205 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.04 s [36% getDir2 content (115x), 31% getFile content (807x), 29% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.999 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.496 s [83% Category (96x)] (117x), ObjectMaps: 0.178 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 11:55:37,876 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.78 s [47% getDatedRevision (362x), 27% getDir2 content (115x), 23% getFile content (807x)] (1325x), RepositoryConfigService: 0.999 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.496 s [83% Category (96x)] (118x), ObjectMaps: 0.178 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 11:57:51,218 [ajp-nio-127.0.0.1-8889-exec-2 | cID:597ab492-7f000001-05d4ece5-601d51ad] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.607 s, CPU [user: 0.256 s, system: 0.102 s], Allocated memory: 42.1 MB, transactions: 2, PolarionAuthenticator: 0.531 s [100% authenticate (1x)] (1x)
2025-07-30 11:57:51,675 [ajp-nio-127.0.0.1-8889-exec-4 | cID:597ab7ed-7f000001-05d4ece5-98901e73] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.205 s, CPU [user: 0.0706 s, system: 0.0154 s], Allocated memory: 9.5 MB, transactions: 0, GC: 0.055 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:57:51,714 [ajp-nio-127.0.0.1-8889-exec-7 | cID:597ab85d-7f000001-05d4ece5-c5b9fc88] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753847871426': Total: 0.131 s, CPU [user: 0.0384 s, system: 0.00639 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 11:57:51,877 [ajp-nio-127.0.0.1-8889-exec-8 | cID:597ab860-7f000001-05d4ece5-c3b10048 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847871428] INFO  TXLOGGER - Tx 66165eae2a451_0_66165eae2a451_0_: finished. Total: 0.218 s, CPU [user: 0.0584 s, system: 0.015 s], Allocated memory: 7.4 MB, svn: 0.0195 s [57% testConnection (1x), 43% getFile content (2x)] (4x)
2025-07-30 11:57:51,921 [ajp-nio-127.0.0.1-8889-exec-8 | cID:597ab860-7f000001-05d4ece5-c3b10048] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847871428': Total: 0.336 s, CPU [user: 0.0818 s, system: 0.02 s], Allocated memory: 9.4 MB, transactions: 1, RepositoryConfigService: 0.222 s [100% getReadConfiguration (1x)] (1x), svn: 0.0195 s [57% testConnection (1x), 43% getFile content (2x)] (4x)
2025-07-30 11:57:51,935 [ajp-nio-127.0.0.1-8889-exec-6 | cID:597ab860-7f000001-05d4ece5-226b7386] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847871427': Total: 0.35 s, CPU [user: 0.042 s, system: 0.00608 s], Allocated memory: 4.3 MB, transactions: 1, RepositoryConfigService: 0.27 s [100% getReadConfiguration (1x)] (1x)
