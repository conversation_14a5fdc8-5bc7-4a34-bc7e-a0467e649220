2025-07-30 11:55:24,319 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:55:24,319 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:55:24,319 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:55:24,319 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:55:24,320 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:55:24,320 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:24,320 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:55:28,660 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:55:28,811 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.151 s. ]
2025-07-30 11:55:28,811 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:55:28,855 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0433 s. ]
2025-07-30 11:55:28,919 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:55:29,052 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 11:55:29,260 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.95 s. ]
2025-07-30 11:55:29,345 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:29,345 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:55:29,374 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:55:29,374 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:29,374 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:55:29,379 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 11:55:29,379 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:55:29,379 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:55:29,379 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 11:55:29,379 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 11:55:29,380 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 11:55:29,388 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 11:55:29,525 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:55:29,636 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:55:30,068 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-07-30 11:55:30,080 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,080 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:55:30,290 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:55:30,300 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 11:55:30,326 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,326 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:55:30,332 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:55:30,382 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:55:30,420 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 11:55:30,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 11:55:30,492 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 11:55:30,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 11:55:30,521 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 11:55:30,556 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:55:30,595 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:55:30,595 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-07-30 11:55:30,596 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,596 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:55:30,611 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:55:30,611 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,611 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:55:30,714 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:55:30,717 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:55:30,898 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 11:55:30,898 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,898 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:55:30,909 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:55:30,909 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:55:30,909 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:55:34,605 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.7 s. ]
2025-07-30 11:55:34,606 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:55:34,606 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-07-30 11:55:34,606 [main] INFO  com.polarion.platform.startup - ****************************************************************
