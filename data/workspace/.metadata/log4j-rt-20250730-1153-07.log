2025-07-30 11:53:15,302 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 11:53:15,876 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 11:53:15,878 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 11:53:15,878 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 11:53:15,880 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-30 11:53:16,027 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 11:53:16,028 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 11:53:16,028 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-30 11:53:16,630 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 11:53:16,730 [ajp-nio-127.0.0.1-8889-exec-1 | cID:597686a8-7f000001-625ee079-2833df82] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 11:53:16,748 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 11:53:16,748 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
