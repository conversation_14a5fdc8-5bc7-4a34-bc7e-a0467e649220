2025-07-30 15:28:13,525 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:13,526 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:28:18,305 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:28:18,466 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.161 s. ]
2025-07-30 15:28:18,466 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:28:18,584 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.118 s. ]
2025-07-30 15:28:18,650 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:28:18,798 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 15:28:19,045 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.53 s. ]
2025-07-30 15:28:19,165 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:19,165 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:28:19,194 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 15:28:19,194 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:19,194 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 15:28:19,201 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 15:28:19,212 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:28:19,366 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:28:19,457 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:28:20,063 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.87 s. ]
2025-07-30 15:28:20,078 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:20,078 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:28:20,315 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:28:20,330 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-07-30 15:28:20,359 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:20,359 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:28:20,367 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:28:20,417 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:28:20,467 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:28:20,507 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:28:20,557 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:28:20,587 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:28:20,617 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:28:20,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:28:20,731 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:28:20,731 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.4 s. ]
2025-07-30 15:28:20,731 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:20,731 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:28:20,747 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:28:20,753 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:20,753 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:28:20,896 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:28:20,899 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:28:21,117 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.37 s. ]
2025-07-30 15:28:21,118 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:21,118 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:28:21,129 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:28:21,129 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:28:21,129 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:28:24,306 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.18 s. ]
2025-07-30 15:28:24,307 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:28:24,307 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.8 s. ]
2025-07-30 15:28:24,307 [main] INFO  com.polarion.platform.startup - ****************************************************************
