2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:39,187 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 22:08:43,663 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 22:08:43,835 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.172 s. ]
2025-07-29 22:08:43,835 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 22:08:43,903 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0681 s. ]
2025-07-29 22:08:43,965 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 22:08:44,137 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 81 s. ]
2025-07-29 22:08:44,345 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.16 s. ]
2025-07-29 22:08:44,456 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:44,457 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 22:08:44,493 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-29 22:08:44,494 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:44,494 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 22:08:44,503 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 22:08:44,503 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-29 22:08:44,503 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 22:08:44,503 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-29 22:08:44,504 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-29 22:08:44,503 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-29 22:08:44,522 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 22:08:44,682 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 22:08:44,793 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 22:08:45,273 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-07-29 22:08:45,289 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:45,289 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 22:08:45,576 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 22:08:45,591 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-29 22:08:45,622 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:45,622 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 22:08:45,628 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 22:08:45,696 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 22:08:45,764 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 22:08:45,793 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 22:08:45,826 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 22:08:45,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 22:08:45,891 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 22:08:45,927 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 22:08:45,960 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 22:08:45,960 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-07-29 22:08:45,961 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:45,961 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 22:08:45,974 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 22:08:45,975 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:45,975 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 22:08:46,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 22:08:46,115 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 22:08:46,291 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-07-29 22:08:46,292 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:46,292 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 22:08:46,308 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-29 22:08:46,308 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:08:46,308 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 22:09:27,781 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 41.46 s. ]
2025-07-29 22:09:27,781 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:09:27,781 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 48.6 s. ]
2025-07-29 22:09:27,781 [main] INFO  com.polarion.platform.startup - ****************************************************************
