2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:08,810 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:08:13,124 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:08:13,264 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-07-30 18:08:13,264 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:08:13,302 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0373 s. ]
2025-07-30 18:08:13,362 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:08:13,473 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 20 s. ]
2025-07-30 18:08:13,682 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.88 s. ]
2025-07-30 18:08:13,767 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:13,767 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:08:13,799 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 18:08:13,800 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:13,800 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 18:08:13,804 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 18:08:13,810 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:08:13,936 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:08:14,036 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:08:14,518 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-07-30 18:08:14,532 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:14,533 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:08:14,769 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:08:14,786 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-07-30 18:08:14,814 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:14,814 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:08:14,817 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:08:14,872 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:08:14,916 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:08:14,968 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:08:15,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:08:15,045 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:08:15,066 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:08:15,114 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:08:15,162 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:08:15,162 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.38 s. ]
2025-07-30 18:08:15,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:15,162 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:08:15,178 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 18:08:15,178 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:15,178 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:08:15,384 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:08:15,390 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:08:15,584 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.41 s. ]
2025-07-30 18:08:15,585 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:15,585 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:08:15,596 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:08:15,596 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:08:15,596 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:08:18,821 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.23 s. ]
2025-07-30 18:08:18,823 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:08:18,823 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10 s. ]
2025-07-30 18:08:18,823 [main] INFO  com.polarion.platform.startup - ****************************************************************
