2025-07-30 10:48:13,844 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0716 s [54% update (144x), 46% query (12x)] (221x), svn: 0.0185 s [61% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 10:48:13,954 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0304 s [68% getDir2 content (2x), 26% info (3x)] (6x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.208 s, system: 0.268 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.118 s, system: 0.201 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0776 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0409 s [51% log2 (5x), 33% getLatestRevision (1x)] (7x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.729 s, CPU [user: 0.0834 s, system: 0.123 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0743 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0645 s [76% log2 (10x), 18% getLatestRevision (2x)] (13x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.729 s, CPU [user: 0.28 s, system: 0.333 s], Allocated memory: 72.4 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (12x), svn: 0.0786 s [28% log (1x), 24% log2 (5x), 23% info (5x), 13% getLatestRevision (2x)] (18x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0547 s, system: 0.0884 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0556 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0408 s [73% log2 (5x), 18% testConnection (1x)] (7x)
2025-07-30 10:48:14,686 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.729 s, CPU [user: 0.0816 s, system: 0.101 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.086 s [83% log2 (10x)] (13x), ObjectMaps: 0.0571 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:48:14,689 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.526 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.326 s [59% log2 (36x), 18% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 10:48:14,955 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.231 s [100% getReadConfiguration (48x)] (48x), svn: 0.0735 s [83% info (18x)] (38x)
2025-07-30 10:48:15,298 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.267 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.207 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:48:15,533 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.218 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0433 s [100% refresh (1x)] (1x), commit: 0.0343 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0146 s [100% objectsToInv (1x)] (1x), DB: 0.0133 s [37% update (3x), 30% execute (1x), 17% commit (2x)] (8x)
2025-07-30 10:48:17,979 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.339 s [89% info (158x)] (168x)
2025-07-30 10:48:17,987 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 2, persistence listener: 0.0187 s [68% indexRefreshPersistenceListener (1x), 12% WorkItemActivityCreator (1x)] (7x), notification worker: 0.00396 s [67% WorkItemActivityCreator (1x), 25% TestRunActivityCreator (1x)] (3x)
2025-07-30 10:48:18,379 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66164ec250c44_0_66164ec250c44_0_: finished. Total: 0.392 s, CPU [user: 0.152 s, system: 0.0229 s], Allocated memory: 22.1 MB, resolve: 0.0956 s [72% User (2x), 25% Project (1x)] (5x), Lucene: 0.0659 s [100% search (1x)] (1x), GC: 0.033 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0296 s [67% getPrimaryObjectLocation (2x), 28% getPrimaryObjectProperty (2x)] (11x), svn: 0.0265 s [48% getLatestRevision (3x), 19% testConnection (1x), 14% log (1x)] (9x)
2025-07-30 10:48:18,898 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.00633 s, system: 0.0014 s], Allocated memory: 529.5 kB, transactions: 1
2025-07-30 10:48:18,898 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 26, notification worker: 0.405 s [99% RevisionActivityCreator (2x)] (3x), resolve: 0.11 s [72% User (3x), 22% Project (1x)] (7x), Lucene: 0.078 s [85% search (1x)] (3x), ObjectMaps: 0.0326 s [70% getPrimaryObjectLocation (3x), 26% getPrimaryObjectProperty (2x)] (12x), svn: 0.0321 s [46% getLatestRevision (4x), 27% testConnection (2x), 12% log (1x)] (11x), Incremental Baseline: 0.0261 s [100% WorkItem (22x)] (22x)
2025-07-30 10:48:18,899 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.921 s, CPU [user: 0.164 s, system: 0.0241 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.681 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0892 s [85% buildBaselineSnapshots (1x)] (24x)
2025-07-30 10:48:19,242 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ec252845_0_66164ec252845_0_: finished. Total: 1.25 s, CPU [user: 0.301 s, system: 0.0767 s], Allocated memory: 46.6 MB, svn: 0.761 s [44% getDir2 content (25x), 41% getDatedRevision (181x)] (307x), resolve: 0.328 s [100% Category (96x)] (96x), ObjectMaps: 0.114 s [42% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 10:48:19,422 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ec39dc48_0_66164ec39dc48_0_: finished. Total: 0.102 s, CPU [user: 0.0513 s, system: 0.00739 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0485 s [55% getReadConfiguration (162x), 45% getReadUserConfiguration (10x)] (172x), svn: 0.048 s [49% info (19x), 47% getFile content (15x)] (36x), resolve: 0.0295 s [100% User (9x)] (9x), ObjectMaps: 0.0103 s [56% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 10:48:19,669 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ec3c604a_0_66164ec3c604a_0_: finished. Total: 0.188 s, CPU [user: 0.0631 s, system: 0.00659 s], Allocated memory: 19.9 MB, svn: 0.151 s [80% getDir2 content (17x), 20% getFile content (44x)] (62x), RepositoryConfigService: 0.0509 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 10:48:20,346 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ec3f544b_0_66164ec3f544b_0_: finished. Total: 0.677 s, CPU [user: 0.343 s, system: 0.0142 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.517 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.328 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 10:48:20,695 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ec4bbc4e_0_66164ec4bbc4e_0_: finished. Total: 0.232 s, CPU [user: 0.0993 s, system: 0.0049 s], Allocated memory: 384.9 MB, svn: 0.148 s [52% getDir2 content (21x), 48% getFile content (185x)] (207x), RepositoryConfigService: 0.142 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 10:48:20,696 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.71 s, CPU [user: 0.943 s, system: 0.121 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.59 s [47% getDir2 content (115x), 30% getFile content (807x), 20% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.823 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.392 s [84% Category (96x)] (117x), ObjectMaps: 0.138 s [43% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 10:48:20,696 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.27 s [43% getDatedRevision (362x), 33% getDir2 content (115x), 21% getFile content (807x)] (1324x), RepositoryConfigService: 0.823 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.392 s [84% Category (96x)] (118x), ObjectMaps: 0.138 s [43% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x), Lucene: 0.125 s [61% buildBaselineSnapshots (2x), 26% search (5x)] (54x)
2025-07-30 10:48:28,001 [ajp-nio-127.0.0.1-8889-exec-2 | cID:593b2e95-0a465820-7bb00516-93750e6f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.458 s, CPU [user: 0.232 s, system: 0.0727 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.396 s [100% authenticate (1x)] (1x)
2025-07-30 10:48:28,404 [ajp-nio-127.0.0.1-8889-exec-5 | cID:593b3176-0a465820-7bb00516-c96a28f8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.125 s, CPU [user: 0.0136 s, system: 0.0054 s], Allocated memory: 1.3 MB, transactions: 0
2025-07-30 10:48:28,404 [ajp-nio-127.0.0.1-8889-exec-4 | cID:593b314c-0a465820-7bb00516-fb0988df] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.168 s, CPU [user: 0.0829 s, system: 0.0215 s], Allocated memory: 9.7 MB, transactions: 0
2025-07-30 10:48:28,427 [ajp-nio-127.0.0.1-8889-exec-6 | cID:593b3178-0a465820-7bb00516-bab27e89] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753843708189': Total: 0.146 s, CPU [user: 0.0367 s, system: 0.00819 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 10:48:28,486 [ajp-nio-127.0.0.1-8889-exec-8 | cID:593b317a-0a465820-7bb00516-6ea2a3b8 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843708190] INFO  TXLOGGER - Tx 66164ecc76451_0_66164ecc76451_0_: finished. Total: 0.109 s, CPU [user: 0.0539 s, system: 0.0142 s], Allocated memory: 7.9 MB, svn: 0.0153 s [69% testConnection (1x), 30% getFile content (2x)] (4x)
2025-07-30 10:48:28,490 [ajp-nio-127.0.0.1-8889-exec-8 | cID:593b317a-0a465820-7bb00516-6ea2a3b8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843708190': Total: 0.207 s, CPU [user: 0.0689 s, system: 0.0174 s], Allocated memory: 9.2 MB, transactions: 1, RepositoryConfigService: 0.11 s [100% getReadConfiguration (1x)] (1x), svn: 0.0153 s [69% testConnection (1x), 30% getFile content (2x)] (4x)
2025-07-30 10:48:28,532 [ajp-nio-127.0.0.1-8889-exec-7 | cID:593b317a-0a465820-7bb00516-0b7a200e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753843708191': Total: 0.248 s, CPU [user: 0.0434 s, system: 0.00748 s], Allocated memory: 4.6 MB, transactions: 1, RepositoryConfigService: 0.151 s [100% getReadConfiguration (1x)] (1x)
