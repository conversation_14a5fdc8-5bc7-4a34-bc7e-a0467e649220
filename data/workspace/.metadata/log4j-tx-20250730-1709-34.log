2025-07-30 17:09:39,181 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0642 s [66% update (144x), 33% query (12x)] (221x), svn: 0.0237 s [67% getLatestRevision (2x), 24% testConnection (1x)] (4x)
2025-07-30 17:09:39,312 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.043 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 17:09:40,238 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.918 s, CPU [user: 0.242 s, system: 0.27 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:09:40,238 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.917 s, CPU [user: 0.0617 s, system: 0.0772 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0672 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:09:40,238 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.917 s, CPU [user: 0.283 s, system: 0.325 s], Allocated memory: 70.7 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:09:40,238 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.918 s, CPU [user: 0.103 s, system: 0.129 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.107 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0801 s [80% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 17:09:40,239 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.916 s, CPU [user: 0.0961 s, system: 0.101 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.138 s [78% log2 (10x), 13% testConnection (1x)] (13x), ObjectMaps: 0.0484 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:09:40,239 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.917 s, CPU [user: 0.177 s, system: 0.201 s], Allocated memory: 26.3 MB, transactions: 0, svn: 0.121 s [38% log2 (5x), 21% info (5x), 16% log (1x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0943 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:09:40,239 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.564 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.425 s [64% log2 (36x), 12% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 17:09:40,398 [main | u:p] INFO  TXLOGGER - Tx 6616a60c49001_0_6616a60c49001_0_: finished. Total: 0.136 s, CPU [user: 0.102 s, system: 0.00499 s], Allocated memory: 21.8 MB
2025-07-30 17:09:40,564 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.281 s [100% getReadConfiguration (48x)] (48x), svn: 0.099 s [80% info (18x), 9% getLatestRevision (1x)] (38x)
2025-07-30 17:09:40,985 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.324 s [76% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.242 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:09:41,263 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.126 s, CPU [user: 0.0231 s, system: 0.00747 s], Allocated memory: 10.4 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:09:41,333 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.328 s [100% doFinishStartup (1x)] (1x), commit: 0.0721 s [100% Revision (1x)] (1x), Lucene: 0.0432 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0361 s [100% objectsToInv (1x)] (1x)
2025-07-30 17:09:44,507 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.352 s [87% info (158x)] (170x)
2025-07-30 17:09:44,858 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a61070845_0_6616a61070845_0_: finished. Total: 0.343 s, CPU [user: 0.146 s, system: 0.0175 s], Allocated memory: 21.6 MB, resolve: 0.088 s [59% User (2x), 39% Project (1x)] (5x), Lucene: 0.032 s [100% search (1x)] (1x), GC: 0.031 s [100% G1 Young Generation (1x)] (1x), svn: 0.0259 s [41% getLatestRevision (2x), 25% log (1x), 16% testConnection (1x)] (8x), ObjectMaps: 0.0251 s [46% getPrimaryObjectProperty (2x), 41% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 17:09:45,511 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.852 s, CPU [user: 0.00623 s, system: 0.00119 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 17:09:45,511 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.352 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.11 s [66% User (3x), 31% Project (1x)] (7x), Lucene: 0.0569 s [56% search (1x), 38% add (1x)] (3x), ObjectMaps: 0.0345 s [57% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.034 s [100% WorkItem (22x)] (22x), svn: 0.0259 s [41% getLatestRevision (2x), 25% log (1x), 16% testConnection (1x)] (8x)
2025-07-30 17:09:45,512 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.01 s, CPU [user: 0.184 s, system: 0.027 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.741 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0757 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 17:09:46,012 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a61070042_0_6616a61070042_0_: finished. Total: 1.5 s, CPU [user: 0.365 s, system: 0.0893 s], Allocated memory: 46.6 MB, svn: 0.928 s [51% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.373 s [100% Category (96x)] (96x), ObjectMaps: 0.139 s [42% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 17:09:46,220 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a611fb048_0_6616a611fb048_0_: finished. Total: 0.127 s, CPU [user: 0.0598 s, system: 0.0112 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0607 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0543 s [54% info (19x), 40% getFile content (15x)] (36x), resolve: 0.036 s [100% User (9x)] (9x), ObjectMaps: 0.0158 s [58% getPrimaryObjectProperty (8x), 23% getLastPromoted (8x)] (32x)
2025-07-30 17:09:46,520 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a61224c4a_0_6616a61224c4a_0_: finished. Total: 0.261 s, CPU [user: 0.0764 s, system: 0.0095 s], Allocated memory: 19.9 MB, svn: 0.207 s [73% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.0894 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:09:47,449 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6126644b_0_6616a6126644b_0_: finished. Total: 0.928 s, CPU [user: 0.408 s, system: 0.0304 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.74 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.492 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x)
2025-07-30 17:09:47,687 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6134e44c_0_6616a6134e44c_0_: finished. Total: 0.238 s, CPU [user: 0.0412 s, system: 0.00585 s], Allocated memory: 17.8 MB, svn: 0.205 s [69% getDir2 content (18x), 31% getFile content (29x)] (48x), RepositoryConfigService: 0.0895 s [99% getReadConfiguration (124x)] (148x)
2025-07-30 17:09:48,078 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a6139cc4e_0_6616a6139cc4e_0_: finished. Total: 0.315 s, CPU [user: 0.124 s, system: 0.00825 s], Allocated memory: 384.9 MB, svn: 0.207 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x), RepositoryConfigService: 0.197 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 17:09:48,078 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.57 s, CPU [user: 1.14 s, system: 0.166 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.21 s [42% getDir2 content (115x), 34% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.23 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.443 s [84% Category (96x)] (117x)
2025-07-30 17:09:48,078 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.96 s [41% getDatedRevision (362x), 31% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.23 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.443 s [84% Category (96x)] (118x), ObjectMaps: 0.167 s [45% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 17:09:50,834 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a985852-7f000001-31ea8b0e-6752d19e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.543 s, CPU [user: 0.268 s, system: 0.0627 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.462 s [100% authenticate (1x)] (1x)
2025-07-30 17:09:51,086 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a985aca-7f000001-31ea8b0e-e36c6573] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.162 s, CPU [user: 0.0253 s, system: 0.00664 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0908 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0598 s [100% readUserData (1x)] (1x)
2025-07-30 17:09:51,395 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a985c1a-7f000001-31ea8b0e-fffbeab8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.137 s, CPU [user: 0.0171 s, system: 0.00332 s], Allocated memory: 1.2 MB, transactions: 0, permissions: 0.0117 s [100% readInstance (1x)] (1x)
2025-07-30 17:09:51,395 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a985bf8-7f000001-31ea8b0e-045022fa] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.17 s, CPU [user: 0.0846 s, system: 0.012 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 17:09:51,421 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a985c1a-7f000001-31ea8b0e-dfccd521] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753866591176': Total: 0.163 s, CPU [user: 0.0396 s, system: 0.00545 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 17:09:51,468 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a985c1a-7f000001-31ea8b0e-fbacb8c8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866591178': Total: 0.21 s, CPU [user: 0.0721 s, system: 0.0135 s], Allocated memory: 8.1 MB, transactions: 1, RepositoryConfigService: 0.0918 s [100% getReadConfiguration (1x)] (1x), svn: 0.0124 s [59% testConnection (1x), 40% getFile content (2x)] (4x), permissions: 0.0124 s [100% readInstance (1x)] (1x)
2025-07-30 17:09:51,522 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a985c1a-7f000001-31ea8b0e-b380f961] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753866591177': Total: 0.264 s, CPU [user: 0.0519 s, system: 0.00716 s], Allocated memory: 5.1 MB, transactions: 1, RepositoryConfigService: 0.121 s [100% getReadConfiguration (1x)] (1x)
