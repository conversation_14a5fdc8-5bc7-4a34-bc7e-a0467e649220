2025-07-30 13:34:44,966 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:44,967 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:34:49,409 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:34:49,559 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-30 13:34:49,559 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:34:49,600 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0407 s. ]
2025-07-30 13:34:49,673 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:34:49,835 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 13:34:50,056 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.09 s. ]
2025-07-30 13:34:50,149 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:50,149 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:34:50,174 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 13:34:50,174 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:50,174 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 13:34:50,180 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 13:34:50,188 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:34:50,366 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:34:50,454 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:34:50,934 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-30 13:34:50,952 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:50,952 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:34:51,185 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:34:51,196 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 13:34:51,223 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:51,223 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:34:51,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:34:51,286 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:34:51,328 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:34:51,382 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:34:51,432 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:34:51,467 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:34:51,493 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:34:51,544 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:34:51,590 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:34:51,590 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-07-30 13:34:51,590 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:51,590 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:34:51,607 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 13:34:51,607 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:51,607 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:34:51,750 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:34:51,753 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:34:51,898 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 13:34:51,899 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:51,899 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:34:51,907 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:34:51,907 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:34:51,907 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:34:55,403 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.49 s. ]
2025-07-30 13:34:55,405 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:34:55,405 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.4 s. ]
2025-07-30 13:34:55,405 [main] INFO  com.polarion.platform.startup - ****************************************************************
