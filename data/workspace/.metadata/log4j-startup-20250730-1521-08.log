2025-07-30 15:21:08,657 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:08,658 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:21:13,298 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:21:13,438 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.139 s. ]
2025-07-30 15:21:13,438 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:21:13,483 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0456 s. ]
2025-07-30 15:21:13,533 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:21:13,632 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:21:13,848 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.19 s. ]
2025-07-30 15:21:13,931 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:13,931 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:21:13,958 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 15:21:13,958 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:13,958 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 15:21:13,962 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 15:21:13,968 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:21:14,088 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:21:14,202 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:21:14,674 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-07-30 15:21:14,686 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:14,686 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:21:14,894 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:21:14,906 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 15:21:14,929 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:14,929 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:21:14,932 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:21:14,980 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:21:15,027 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:21:15,062 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:21:15,103 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:21:15,128 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:21:15,149 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:21:15,186 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:21:15,216 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:21:15,216 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-07-30 15:21:15,216 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:15,216 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:21:15,232 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:21:15,232 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:15,232 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:21:15,328 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:21:15,330 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:21:15,441 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-07-30 15:21:15,442 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:15,442 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:21:15,448 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:21:15,448 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:21:15,448 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:21:17,811 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.36 s. ]
2025-07-30 15:21:17,812 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:21:17,812 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.15 s. ]
2025-07-30 15:21:17,812 [main] INFO  com.polarion.platform.startup - ****************************************************************
