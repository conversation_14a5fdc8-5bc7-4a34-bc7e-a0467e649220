2025-07-30 18:33:06,165 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.119 s [65% update (144x), 34% query (12x)] (221x), svn: 0.0196 s [55% getLatestRevision (2x), 27% testConnection (1x)] (4x)
2025-07-30 18:33:06,289 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0332 s [56% getDir2 content (2x), 36% info (3x)] (6x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.206 s, system: 0.265 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.119 s, system: 0.195 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0619 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.0856 s, system: 0.112 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.127 s [87% log2 (10x)] (13x), ObjectMaps: 0.0577 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.0839 s, system: 0.131 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.101 s [79% log2 (10x), 11% getLatestRevision (2x)] (13x), ObjectMaps: 0.1 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.0506 s, system: 0.0666 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0643 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0569 s [60% log2 (5x), 23% getLatestRevision (1x)] (7x)
2025-07-30 18:33:07,074 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.782 s, CPU [user: 0.264 s, system: 0.326 s], Allocated memory: 72.4 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0731 s [30% info (5x), 24% log2 (5x), 21% log (1x), 11% getLatestRevision (2x)] (18x)
2025-07-30 18:33:07,075 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.521 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.393 s [67% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 18:33:07,311 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.202 s [100% getReadConfiguration (48x)] (48x), svn: 0.0762 s [83% info (18x)] (38x)
2025-07-30 18:33:07,708 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.318 s [73% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.231 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:33:07,925 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.104 s, CPU [user: 0.0243 s, system: 0.00531 s], Allocated memory: 2.6 MB, GC: 0.029 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 18:33:07,929 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.108 s, CPU [user: 0.00357 s, system: 0.00203 s], Allocated memory: 826.6 kB, GC: 0.029 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 18:33:07,929 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.109 s, CPU [user: 0.0223 s, system: 0.00487 s], Allocated memory: 2.5 MB, GC: 0.029 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 18:33:07,959 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.139 s, CPU [user: 0.0245 s, system: 0.00808 s], Allocated memory: 10.5 MB, GC: 0.029 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 18:33:08,014 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.291 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0755 s [100% refresh (1x)] (1x), commit: 0.0574 s [100% Revision (1x)] (1x), DB: 0.0168 s [45% update (3x), 33% query (1x), 15% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0156 s [100% objectsToInv (1x)] (1x)
2025-07-30 18:33:17,103 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.432 s [86% info (158x)] (172x), PullingJob: 0.0281 s [100% collectChanges (3x)] (3x)
2025-07-30 18:33:17,503 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616b92f8f445_0_6616b92f8f445_0_: finished. Total: 0.386 s, CPU [user: 0.13 s, system: 0.0224 s], Allocated memory: 20.3 MB, resolve: 0.0949 s [53% User (2x), 45% Project (1x)] (5x), Lucene: 0.0377 s [100% search (1x)] (1x), ObjectMaps: 0.0322 s [54% getPrimaryObjectLocation (2x), 36% getPrimaryObjectProperty (2x)] (11x), svn: 0.0303 s [42% getLatestRevision (2x), 23% log (1x), 14% testConnection (1x), 12% getFile content (2x)] (8x)
2025-07-30 18:33:18,171 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.98 s, CPU [user: 0.00613 s, system: 0.00243 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 18:33:18,172 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.394 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.107 s [57% User (3x), 40% Project (1x)] (7x), Lucene: 0.0568 s [66% search (1x), 27% add (1x)] (3x), ObjectMaps: 0.0347 s [57% getPrimaryObjectLocation (3x), 33% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0328 s [100% WorkItem (22x)] (22x), svn: 0.0303 s [42% getLatestRevision (2x), 23% log (1x), 14% testConnection (1x), 12% getFile content (2x)] (8x), persistence listener: 0.0233 s [78% indexRefreshPersistenceListener (1x), 16% WorkItemActivityCreator (1x)] (7x)
2025-07-30 18:33:18,173 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.07 s, CPU [user: 0.171 s, system: 0.0304 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.761 s [99% getDatedRevision (181x)] (183x), Lucene: 0.175 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 18:33:18,650 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b92f8e840_0_6616b92f8e840_0_: finished. Total: 1.54 s, CPU [user: 0.366 s, system: 0.101 s], Allocated memory: 47.1 MB, svn: 0.945 s [48% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.478 s [100% Category (96x)] (96x), ObjectMaps: 0.171 s [39% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 18:33:18,839 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b9311ec48_0_6616b9311ec48_0_: finished. Total: 0.124 s, CPU [user: 0.0574 s, system: 0.0106 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0648 s [53% getReadConfiguration (162x), 47% getReadUserConfiguration (10x)] (172x), svn: 0.0593 s [63% info (19x), 32% getFile content (15x)] (36x), resolve: 0.0314 s [100% User (9x)] (9x), ObjectMaps: 0.0138 s [51% getPrimaryObjectProperty (8x), 26% getPrimaryObjectLocation (8x), 22% getLastPromoted (8x)] (32x)
2025-07-30 18:33:19,049 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b9314784a_0_6616b9314784a_0_: finished. Total: 0.17 s, CPU [user: 0.0638 s, system: 0.00793 s], Allocated memory: 19.9 MB, svn: 0.12 s [64% getDir2 content (17x), 36% getFile content (44x)] (62x), RepositoryConfigService: 0.075 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 18:33:20,200 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b9317244b_0_6616b9317244b_0_: finished. Total: 1.15 s, CPU [user: 0.424 s, system: 0.0629 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.964 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.505 s [77% getFile content (412x), 23% getDir2 content (21x)] (434x)
2025-07-30 18:33:20,835 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b932b184e_0_6616b932b184e_0_: finished. Total: 0.506 s, CPU [user: 0.186 s, system: 0.017 s], Allocated memory: 384.8 MB, RepositoryConfigService: 0.374 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.288 s [67% getFile content (185x), 32% getDir2 content (21x)] (207x)
2025-07-30 18:33:20,839 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.73 s, CPU [user: 1.18 s, system: 0.211 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.07 s [39% getFile content (807x), 35% getDir2 content (115x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.54 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.532 s [90% Category (96x)] (117x), ObjectMaps: 0.193 s [40% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 18:33:20,843 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.84 s [43% getDatedRevision (362x), 29% getFile content (807x), 25% getDir2 content (115x)] (1325x), RepositoryConfigService: 1.54 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.532 s [90% Category (96x)] (118x), Lucene: 0.212 s [66% buildBaselineSnapshots (2x), 20% buildBaseline (46x)] (54x), ObjectMaps: 0.193 s [40% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 18:33:29,358 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.224 s, CPU [user: 0.00224 s, system: 0.00394 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.202 s [100% collectChanges (1x)] (1x), svn: 0.18 s [100% getLatestRevision (1x)] (1x)
2025-07-30 18:33:36,194 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5ae4cc37-7f000001-0e44d951-c0e53223] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 15.5 s, CPU [user: 0.3 s, system: 0.0887 s], Allocated memory: 51.5 MB, transactions: 2, PolarionAuthenticator: 15.4 s [100% authenticate (1x)] (1x), interceptor: 15 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 18:35:14,269 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.103 s, CPU [user: 0.00224 s, system: 0.00286 s], Allocated memory: 129.8 kB, transactions: 0, PullingJob: 0.102 s [100% collectChanges (1x)] (1x), svn: 0.102 s [100% getLatestRevision (1x)] (1x)
