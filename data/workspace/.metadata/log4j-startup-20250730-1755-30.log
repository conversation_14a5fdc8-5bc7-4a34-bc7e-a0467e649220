2025-07-30 17:55:30,490 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:55:30,490 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:55:30,490 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:55:30,490 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:55:30,490 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:55:30,491 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:30,491 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:55:34,964 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:55:35,102 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.137 s. ]
2025-07-30 17:55:35,102 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:55:35,154 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0521 s. ]
2025-07-30 17:55:35,209 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:55:35,367 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 13 s. ]
2025-07-30 17:55:35,620 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.14 s. ]
2025-07-30 17:55:35,719 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:35,719 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:55:35,748 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 17:55:35,749 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:35,749 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:55:35,755 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 17:55:35,755 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 17:55:35,755 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 17:55:35,756 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 17:55:35,755 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 17:55:35,755 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 17:55:35,770 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:55:35,946 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:55:36,046 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:55:36,785 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.04 s. ]
2025-07-30 17:55:36,806 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:36,806 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:55:37,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:55:37,113 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 17:55:37,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:37,154 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:55:37,163 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:55:37,239 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:55:37,295 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:55:37,347 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:55:37,404 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:55:37,433 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:55:37,459 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:55:37,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:55:37,565 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:55:37,565 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-07-30 17:55:37,565 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:37,565 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:55:37,580 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:55:37,580 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:37,580 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:55:37,712 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:55:37,719 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:55:37,954 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.37 s. ]
2025-07-30 17:55:37,954 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:37,954 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:55:37,963 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:55:37,963 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:55:37,963 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:56:02,376 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 24.41 s. ]
2025-07-30 17:56:02,376 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:56:02,376 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 31.9 s. ]
2025-07-30 17:56:02,376 [main] INFO  com.polarion.platform.startup - ****************************************************************
