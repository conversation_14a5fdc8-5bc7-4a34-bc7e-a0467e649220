2025-07-30 16:59:49,320 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 16:59:50,022 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 16:59:50,025 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 16:59:50,026 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 16:59:50,028 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-30 16:59:50,194 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 16:59:50,195 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 16:59:50,195 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-30 16:59:50,879 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 16:59:50,992 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a8f3339-7f000001-1475ce17-a7d39c0b] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 16:59:51,011 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 16:59:51,012 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
