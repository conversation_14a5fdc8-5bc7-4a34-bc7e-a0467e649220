2025-07-29 22:14:41,385 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0747 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0152 s [47% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-07-29 22:14:41,548 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0446 s [65% getDir2 content (2x), 27% info (3x)] (6x)
2025-07-29 22:14:42,303 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.219 s, system: 0.243 s], Allocated memory: 52.7 MB, transactions: 0, ObjectMaps: 0.113 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 22:14:42,303 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.117 s, system: 0.113 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.144 s [47% log2 (10x), 17% getLatestRevision (3x), 14% log (1x), 13% info (5x)] (24x), ObjectMaps: 0.0572 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 22:14:42,304 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.747 s, CPU [user: 0.139 s, system: 0.176 s], Allocated memory: 24.3 MB, transactions: 0, ObjectMaps: 0.0665 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0396 s [75% log2 (5x), 16% testConnection (1x)] (7x)
2025-07-29 22:14:42,304 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.0858 s, system: 0.0858 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.119 s [85% log2 (10x)] (13x), ObjectMaps: 0.054 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 22:14:42,304 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.0615 s, system: 0.0734 s], Allocated memory: 6.8 MB, transactions: 0, svn: 0.0602 s [77% log2 (5x), 12% testConnection (1x)] (7x), ObjectMaps: 0.0567 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 22:14:42,304 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.746 s, CPU [user: 0.245 s, system: 0.307 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0376 s [69% log2 (5x), 20% testConnection (1x)] (7x)
2025-07-29 22:14:42,305 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.471 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.424 s [66% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 22:14:42,596 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.245 s [100% getReadConfiguration (48x)] (48x), svn: 0.0968 s [84% info (18x)] (38x)
2025-07-29 22:14:42,916 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.243 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.191 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 22:14:43,144 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.111 s, CPU [user: 0.0239 s, system: 0.00701 s], Allocated memory: 10.5 MB, GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 22:14:43,185 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.254 s [100% doFinishStartup (1x)] (1x), commit: 0.0437 s [100% Revision (1x)] (1x), Lucene: 0.0408 s [100% refresh (1x)] (1x), DB: 0.0317 s [53% update (3x), 30% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.0161 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0128 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-29 22:26:44,329 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 8, svn: 2.84 s [87% getLatestRevision (241x)] (415x), PullingJob: 2.51 s [100% collectChanges (240x)] (240x)
2025-07-29 22:26:45,518 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.07 s, CPU [user: 0.0085 s, system: 0.00381 s], Allocated memory: 531.1 kB, transactions: 1
2025-07-29 22:26:45,520 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, resolve: 0.0508 s [72% User (2x), 28% Revision (2x)] (4x), Incremental Baseline: 0.0449 s [100% WorkItem (21x)] (21x), persistence listener: 0.0377 s [89% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.0358 s [73% add (1x), 27% refresh (1x)] (2x), notification worker: 0.0323 s [77% RevisionActivityCreator (2x), 9% TestRunActivityCreator (1x)] (6x), ObjectMaps: 0.015 s [100% getPrimaryObjectLocation (2x)] (2x), DB: 0.0122 s [92% update (1x)] (2x), EHCache: 0.00871 s [100% GET (16x)] (37x)
2025-07-29 22:26:45,521 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.08 s, CPU [user: 0.203 s, system: 0.0429 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.948 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0689 s [68% buildBaselineSnapshots (1x), 32% buildBaseline (22x)] (23x)
2025-07-29 22:26:46,355 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a5077cc40_0_6615a5077cc40_0_: finished. Total: 2.02 s, CPU [user: 0.551 s, system: 0.144 s], Allocated memory: 54.6 MB, svn: 1.27 s [62% getDatedRevision (181x), 23% getDir2 content (25x)] (307x), resolve: 0.654 s [100% Category (96x)] (96x), ObjectMaps: 0.218 s [39% getPrimaryObjectProperty (96x), 38% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 22:26:46,656 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a50990448_0_6615a50990448_0_: finished. Total: 0.19 s, CPU [user: 0.0762 s, system: 0.0199 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0857 s [51% getReadConfiguration (162x), 49% getReadUserConfiguration (10x)] (172x), svn: 0.0828 s [52% info (19x), 42% getFile content (16x)] (37x), resolve: 0.0615 s [100% User (9x)] (9x), ObjectMaps: 0.0273 s [48% getPrimaryObjectProperty (9x), 36% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 22:26:46,906 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a509d904a_0_6615a509d904a_0_: finished. Total: 0.149 s, CPU [user: 0.0409 s, system: 0.00558 s], Allocated memory: 19.9 MB, svn: 0.112 s [81% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0371 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 22:26:47,900 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a509fe84b_0_6615a509fe84b_0_: finished. Total: 0.994 s, CPU [user: 0.44 s, system: 0.0492 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.767 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.521 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-29 22:26:48,436 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a50b18c4e_0_6615a50b18c4e_0_: finished. Total: 0.399 s, CPU [user: 0.148 s, system: 0.00961 s], Allocated memory: 384.7 MB, RepositoryConfigService: 0.257 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.23 s [51% getFile content (185x), 41% getDir2 content (20x)] (206x)
2025-07-29 22:26:48,439 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.1 s, CPU [user: 1.37 s, system: 0.247 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.4 s [33% getDatedRevision (181x), 31% getFile content (809x), 31% getDir2 content (114x)] (1144x), RepositoryConfigService: 1.29 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.77 s [85% Category (96x)] (117x), ObjectMaps: 0.264 s [42% getPrimaryObjectProperty (110x), 37% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 22:26:48,439 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 3.35 s [51% getDatedRevision (362x), 23% getFile content (809x), 22% getDir2 content (114x)] (1328x), RepositoryConfigService: 1.29 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.77 s [85% Category (96x)] (118x), ObjectMaps: 0.264 s [42% getPrimaryObjectProperty (110x), 37% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 22:26:58,446 [ajp-nio-127.0.0.1-8889-exec-2 | cID:569452db-0a465820-6f6b7d08-d8005f31] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.561 s, CPU [user: 0.263 s, system: 0.0815 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.473 s [100% authenticate (1x)] (1x)
2025-07-29 22:26:58,964 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5694563a-0a465820-6f6b7d08-fd0a6a17] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.217 s, CPU [user: 0.104 s, system: 0.0243 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-29 22:26:58,964 [ajp-nio-127.0.0.1-8889-exec-5 | cID:56945664-0a465820-6f6b7d08-4aa6e1a6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.176 s, CPU [user: 0.019 s, system: 0.00613 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-29 22:26:59,002 [ajp-nio-127.0.0.1-8889-exec-6 | cID:56945663-0a465820-6f6b7d08-1ea14182] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753799218696': Total: 0.213 s, CPU [user: 0.0488 s, system: 0.0104 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-29 22:26:59,045 [ajp-nio-127.0.0.1-8889-exec-7 | cID:56945664-0a465820-6f6b7d08-3030cb38 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753799218697] INFO  TXLOGGER - Tx 6615a515bf451_0_6615a515bf451_0_: finished. Total: 0.103 s, CPU [user: 0.0421 s, system: 0.0133 s], Allocated memory: 6.5 MB, svn: 0.0102 s [66% testConnection (1x), 33% getFile content (1x)] (3x)
2025-07-29 22:26:59,051 [ajp-nio-127.0.0.1-8889-exec-7 | cID:56945664-0a465820-6f6b7d08-3030cb38] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753799218697': Total: 0.263 s, CPU [user: 0.06 s, system: 0.0184 s], Allocated memory: 7.9 MB, transactions: 1, RepositoryConfigService: 0.105 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 22:26:59,080 [ajp-nio-127.0.0.1-8889-exec-8 | cID:56945665-0a465820-6f6b7d08-ed0e7539] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753799218698': Total: 0.29 s, CPU [user: 0.0311 s, system: 0.00708 s], Allocated memory: 3.5 MB, transactions: 1, RepositoryConfigService: 0.137 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 01:00:44,369 [Monitoring] INFO  TXLOGGER - Periodic summary: transactions: 97, PullingJob: 24.6 s [100% collectChanges (3072x)] (3072x), svn: 24.2 s [100% getLatestRevision (3072x)] (3080x)
2025-07-30 10:18:23,514 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.167 s, CPU [user: 0.00254 s, system: 0.0161 s], Allocated memory: 130.2 kB, transactions: 0, PullingJob: 0.118 s [100% collectChanges (1x)] (1x), svn: 0.116 s [100% getLatestRevision (1x)] (1x)
2025-07-30 10:24:54,628 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59259e15-0a465820-6f6b7d08-637cd82e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753799218699': Total: 0.332 s, CPU [user: 0.0139 s, system: 0.068 s], Allocated memory: 1.2 MB, transactions: 1, PolarionAuthenticator: 0.275 s [100% authenticate (1x)] (1x), interceptor: 0.0811 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
