2025-07-30 13:43:31,226 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:43:31,226 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:43:31,226 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:43:31,227 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:43:31,227 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:43:31,227 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:31,227 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:43:35,956 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:43:36,114 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.158 s. ]
2025-07-30 13:43:36,114 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:43:36,169 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0549 s. ]
2025-07-30 13:43:36,230 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:43:36,363 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 13:43:36,570 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.35 s. ]
2025-07-30 13:43:36,657 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:36,657 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:43:36,686 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 13:43:36,686 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:36,686 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:43:36,691 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 13:43:36,692 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 13:43:36,691 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 13:43:36,691 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 13:43:36,691 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 13:43:36,692 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 13:43:36,698 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:43:36,836 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:43:36,935 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:43:37,423 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 13:43:37,435 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:37,435 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:43:37,635 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:43:37,649 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 13:43:37,674 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:37,674 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:43:37,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:43:37,732 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:43:37,783 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:43:37,836 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:43:37,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:43:37,890 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:43:37,918 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:43:37,968 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:43:38,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:43:38,000 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-07-30 13:43:38,000 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:38,000 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:43:38,013 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 13:43:38,013 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:38,013 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:43:38,123 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:43:38,126 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:43:38,362 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.35 s. ]
2025-07-30 13:43:38,362 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:38,362 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:43:38,374 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:43:38,374 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:43:38,374 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:43:41,459 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.09 s. ]
2025-07-30 13:43:41,459 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:43:41,459 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.2 s. ]
2025-07-30 13:43:41,459 [main] INFO  com.polarion.platform.startup - ****************************************************************
