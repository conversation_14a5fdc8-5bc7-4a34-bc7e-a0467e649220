2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:31,566 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:10:36,171 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:10:36,413 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.241 s. ]
2025-07-30 17:10:36,413 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:10:36,495 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0819 s. ]
2025-07-30 17:10:36,553 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:10:36,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:10:36,924 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.36 s. ]
2025-07-30 17:10:37,022 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:37,022 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:10:37,049 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 17:10:37,049 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:37,049 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 17:10:37,055 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 17:10:37,065 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:10:37,218 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:10:37,336 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:10:38,099 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.05 s. ]
2025-07-30 17:10:38,111 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:38,111 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:10:38,359 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:10:38,376 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-30 17:10:38,420 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:38,420 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:10:38,427 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:10:38,485 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:10:38,534 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:10:38,577 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:10:38,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:10:38,658 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:10:38,684 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:10:38,738 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:10:38,788 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:10:38,788 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.41 s. ]
2025-07-30 17:10:38,788 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:38,788 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:10:38,803 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:10:38,803 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:38,803 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:10:38,939 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:10:38,945 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:10:39,170 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.37 s. ]
2025-07-30 17:10:39,171 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:39,171 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:10:39,185 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 17:10:39,185 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:10:39,185 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:10:43,514 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.33 s. ]
2025-07-30 17:10:43,515 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:10:43,515 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.9 s. ]
2025-07-30 17:10:43,515 [main] INFO  com.polarion.platform.startup - ****************************************************************
