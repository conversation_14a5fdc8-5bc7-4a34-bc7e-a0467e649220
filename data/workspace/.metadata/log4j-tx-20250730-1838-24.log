2025-07-30 18:38:29,109 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0496 s [56% update (144x), 44% query (12x)] (221x), svn: 0.0153 s [54% getLatestRevision (2x), 37% testConnection (1x)] (4x)
2025-07-30 18:38:29,223 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.031 s [60% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.267 s, system: 0.3 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.227 s, system: 0.244 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.0546 s, system: 0.077 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0606 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0535 s [82% log2 (5x)] (7x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.147 s, system: 0.174 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.103 s [28% log2 (5x), 22% info (5x), 20% log (1x), 14% getLatestRevision (2x)] (18x), ObjectMaps: 0.08 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.084 s, system: 0.0848 s], Allocated memory: 11.2 MB, transactions: 0, svn: 0.0918 s [83% log2 (10x)] (13x), ObjectMaps: 0.0478 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:38:29,952 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.0899 s, system: 0.11 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0758 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0726 s [80% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 18:38:29,952 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.502 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.354 s [64% log2 (36x), 13% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 18:38:30,188 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.202 s [100% getReadConfiguration (48x)] (48x), svn: 0.0687 s [85% info (18x)] (38x)
2025-07-30 18:38:30,526 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.265 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.199 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:38:30,758 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.029 s, system: 0.00705 s], Allocated memory: 10.5 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 18:38:30,787 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.246 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0538 s [100% refresh (1x)] (1x), commit: 0.0474 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0161 s [100% objectsToInv (1x)] (1x), DB: 0.0126 s [33% update (3x), 29% query (1x), 25% commit (2x)] (8x)
2025-07-30 18:38:33,460 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.315 s [89% info (158x)] (168x)
2025-07-30 18:38:33,710 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616ba6481844_0_6616ba6481844_0_: finished. Total: 0.231 s, CPU [user: 0.125 s, system: 0.0176 s], Allocated memory: 20.3 MB, resolve: 0.0789 s [69% User (2x), 30% Project (1x)] (5x), Lucene: 0.0244 s [100% search (1x)] (1x), svn: 0.0212 s [40% getLatestRevision (2x), 22% testConnection (1x), 16% log (1x), 12% getFile content (2x)] (8x), ObjectMaps: 0.0199 s [47% getPrimaryObjectProperty (2x), 43% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 18:38:34,722 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.18 s, CPU [user: 0.00529 s, system: 0.00133 s], Allocated memory: 315.4 kB, transactions: 1
2025-07-30 18:38:34,723 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.238 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.092 s [72% User (3x), 26% Project (1x)] (7x), svn: 0.0459 s [46% testConnection (2x), 37% getLatestRevision (3x)] (10x), Lucene: 0.0444 s [55% search (1x), 31% add (1x)] (3x), Incremental Baseline: 0.0325 s [100% WorkItem (22x)] (22x), persistence listener: 0.0271 s [81% indexRefreshPersistenceListener (1x)] (7x), PullingJob: 0.0252 s [100% collectChanges (1x)] (1x), ObjectMaps: 0.0232 s [51% getPrimaryObjectLocation (3x), 41% getPrimaryObjectProperty (2x)] (12x)
2025-07-30 18:38:34,723 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.27 s, CPU [user: 0.171 s, system: 0.0313 s], Allocated memory: 18.4 MB, transactions: 24, svn: 1.08 s [99% getDatedRevision (181x)] (183x)
2025-07-30 18:38:35,337 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba6480040_0_6616ba6480040_0_: finished. Total: 1.86 s, CPU [user: 0.407 s, system: 0.111 s], Allocated memory: 46.6 MB, svn: 1.02 s [57% getDatedRevision (181x), 23% getDir2 content (25x)] (307x), resolve: 0.64 s [100% Category (96x)] (96x), ObjectMaps: 0.194 s [40% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 18:38:35,716 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba666b048_0_6616ba666b048_0_: finished. Total: 0.279 s, CPU [user: 0.0771 s, system: 0.0175 s], Allocated memory: 8.0 MB, resolve: 0.138 s [100% User (9x)] (9x), svn: 0.109 s [50% info (19x), 32% getFile content (15x)] (36x), RepositoryConfigService: 0.0948 s [59% getReadUserConfiguration (10x), 41% getReadConfiguration (162x)] (172x), ObjectMaps: 0.0727 s [80% getPrimaryObjectProperty (8x)] (32x)
2025-07-30 18:38:36,212 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba66b1049_0_6616ba66b1049_0_: finished. Total: 0.491 s, CPU [user: 0.0523 s, system: 0.0106 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.36 s [88% getReadConfiguration (54x)] (77x), svn: 0.0746 s [100% getFile content (12x)] (13x)
2025-07-30 18:38:37,858 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba672d04a_0_6616ba672d04a_0_: finished. Total: 1.64 s, CPU [user: 0.147 s, system: 0.0375 s], Allocated memory: 19.9 MB, svn: 1.4 s [77% getDir2 content (17x), 23% getFile content (44x)] (62x), RepositoryConfigService: 0.496 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 18:38:39,665 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba68c984b_0_6616ba68c984b_0_: finished. Total: 1.8 s, CPU [user: 0.537 s, system: 0.133 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.18 s [98% getReadConfiguration (8682x)] (9021x), svn: 1.07 s [51% getFile content (412x), 49% getDir2 content (21x)] (434x)
2025-07-30 18:38:39,773 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba6a8c44c_0_6616ba6a8c44c_0_: finished. Total: 0.107 s, CPU [user: 0.021 s, system: 0.00306 s], Allocated memory: 17.8 MB, svn: 0.0944 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0248 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 18:38:40,142 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ba6aaf04e_0_6616ba6aaf04e_0_: finished. Total: 0.339 s, CPU [user: 0.129 s, system: 0.00971 s], Allocated memory: 385.4 MB, RepositoryConfigService: 0.228 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.217 s [59% getFile content (185x), 41% getDir2 content (21x)] (207x)
2025-07-30 18:38:40,143 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 6.67 s, CPU [user: 1.42 s, system: 0.335 s], Allocated memory: 1.6 GB, transactions: 10, svn: 4.06 s [50% getDir2 content (115x), 33% getFile content (807x)] (1141x), RepositoryConfigService: 2.41 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.83 s [77% Category (96x), 17% User (9x)] (117x)
2025-07-30 18:38:40,143 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 5.17 s [39% getDir2 content (115x), 32% getDatedRevision (362x), 26% getFile content (807x)] (1326x), RepositoryConfigService: 2.41 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.83 s [77% Category (96x), 17% User (10x)] (118x), ObjectMaps: 0.284 s [51% getPrimaryObjectProperty (108x), 29% getPrimaryObjectLocation (114x), 20% getLastPromoted (108x)] (442x)
2025-07-30 18:38:42,534 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5ae99a31-7f000001-0eea87c4-e97373d9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 6.96 s, CPU [user: 0.448 s, system: 0.0446 s], Allocated memory: 51.9 MB, transactions: 2, PolarionAuthenticator: 6.31 s [100% authenticate (1x)] (1x), interceptor: 5.92 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 18:40:11,479 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5aea13d6-7f000001-0eea87c4-f10018be] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 64.8 s, CPU [user: 0.399 s, system: 0.035 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:44:47,534 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5aeb26d0-7f000001-0eea87c4-8dd1651a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 270 s, CPU [user: 0.15 s, system: 0.0437 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:45:45,995 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5af02a54-7f000001-0eea87c4-40f95fd7 | u:admin] INFO  TXLOGGER - Tx 6616bc0aafc51_0_6616bc0aafc51_0_: finished. Total: 0.202 s, CPU [user: 0.0685 s, system: 0.0372 s], Allocated memory: 4.9 MB, svn: 0.0577 s [36% testConnection (1x), 21% info (2x), 18% getDir2 content (1x), 12% log (1x)] (8x), RepositoryConfigService: 0.0546 s [100% getReadConfiguration (10x)] (15x), resolve: 0.039 s [98% Project (4x)] (5x), ObjectMaps: 0.0261 s [51% getPrimaryObjectProperty (1x), 24% getPrimaryObjectLocations (1x), 19% getPrimaryObjectLocation (1x)] (6x), permissions: 0.0158 s [100% readInstance (8x)] (10x)
2025-07-30 18:45:46,098 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5af02a54-7f000001-0eea87c4-40f95fd7] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.414 s, CPU [user: 0.21 s, system: 0.0707 s], Allocated memory: 66.9 MB, transactions: 1, PortalDataService: 0.206 s [100% getInitData (1x)] (1x), RPC: 0.184 s [44% decodeRequest (1x), 43% encodeResponse (1x)] (4x), svn: 0.0577 s [36% testConnection (1x), 21% info (2x), 18% getDir2 content (1x), 12% log (1x)] (8x), RepositoryConfigService: 0.0546 s [100% getReadConfiguration (10x)] (15x), resolve: 0.039 s [98% Project (4x)] (5x), ObjectMaps: 0.0261 s [51% getPrimaryObjectProperty (1x), 24% getPrimaryObjectLocations (1x), 19% getPrimaryObjectLocation (1x)] (6x)
2025-07-30 18:45:46,329 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5af02c26-7f000001-0eea87c4-b22db98a | u:admin] INFO  TXLOGGER - Tx 6616bc0b0bc52_0_6616bc0b0bc52_0_: finished. Total: 0.17 s, CPU [user: 0.0879 s, system: 0.0202 s], Allocated memory: 13.2 MB, RepositoryConfigService: 0.0485 s [93% getReadConfiguration (13x)] (18x), svn: 0.0336 s [36% getFile content (6x), 29% info (4x), 21% getDir2 content (1x)] (13x)
2025-07-30 18:45:46,350 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5af02c26-7f000001-0eea87c4-b22db98a] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.2 s, CPU [user: 0.106 s, system: 0.0273 s], Allocated memory: 16.4 MB, transactions: 1, PortalDataService: 0.171 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0485 s [93% getReadConfiguration (13x)] (18x), svn: 0.0336 s [36% getFile content (6x), 29% info (4x), 21% getDir2 content (1x)] (13x), RPC: 0.0264 s [71% encodeResponse (1x), 24% decodeRequest (1x)] (4x)
2025-07-30 18:45:47,060 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5af02f3b-7f000001-0eea87c4-90938788] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753872346857': Total: 0.121 s, CPU [user: 0.0269 s, system: 0.00383 s], Allocated memory: 3.7 MB, transactions: 1, RepositoryConfigService: 0.114 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 18:47:11,942 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5af14a42-7f000001-0eea87c4-c87e2526] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 12.5 s, CPU [user: 0.431 s, system: 0.0867 s], Allocated memory: 1.1 MB, transactions: 0
2025-07-30 18:48:19,300 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5af1f71a-7f000001-0eea87c4-45fcbfe9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 35.7 s, CPU [user: 0.409 s, system: 0.0416 s], Allocated memory: 1.1 MB, transactions: 0
2025-07-30 18:49:04,931 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5af2b075-7f000001-0eea87c4-4c326427] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 33.8 s, CPU [user: 0.451 s, system: 0.0802 s], Allocated memory: 983.3 kB, transactions: 0
2025-07-30 18:49:37,818 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5af39580-7f000001-0eea87c4-441f0686] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 8.09 s, CPU [user: 0.417 s, system: 0.0241 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:49:57,933 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5af3e971-7f000001-0eea87c4-82046820] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 6.71 s, CPU [user: 0.385 s, system: 0.0377 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:51:13,279 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5af4973a-7f000001-0eea87c4-1b56b814] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 37.6 s, CPU [user: 0.104 s, system: 0.0403 s], Allocated memory: 951.1 kB, transactions: 0
2025-07-30 18:51:36,103 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5af533b2-7f000001-0eea87c4-716ebca4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 20.3 s, CPU [user: 0.387 s, system: 0.0156 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:52:18,543 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5af5e286-7f000001-0eea87c4-cfafe355] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 18 s, CPU [user: 0.132 s, system: 0.0348 s], Allocated memory: 978.6 kB, transactions: 0
2025-07-30 18:52:59,945 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5af64339-7f000001-0eea87c4-1a0d1280] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 34.7 s, CPU [user: 0.4 s, system: 0.0412 s], Allocated memory: 1.0 MB, transactions: 0
2025-07-30 18:55:28,860 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5af8e7db-7f000001-0eea87c4-2b63ff39] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections': Total: 10.4 s, CPU [user: 0.428 s, system: 0.0659 s], Allocated memory: 5.1 MB, transactions: 0
2025-07-30 18:56:15,154 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5af9c486-7f000001-0eea87c4-af2b0046] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.166 s, CPU [user: 0.0518 s, system: 0.0254 s], Allocated memory: 52.3 MB, transactions: 1, RPC: 0.146 s [75% encodeResponse (1x), 24% writeResponse (1x)] (4x), GC: 0.063 s [100% G1 Young Generation (1x)] (1x), PortalDataService: 0.0177 s [100% getInitData (1x)] (1x)
2025-07-30 18:56:24,657 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5af9e8f8-7f000001-0eea87c4-de27a3bc] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileContent.jsp?url=WBSdev': Total: 0.342 s, CPU [user: 0.0313 s, system: 0.0556 s], Allocated memory: 1.6 MB, transactions: 0
2025-07-30 18:56:25,567 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5af9ea77-7f000001-0eea87c4-b131fb1d] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/invalidResource.jsp': Total: 0.871 s, CPU [user: 0.454 s, system: 0.0917 s], Allocated memory: 35.3 MB, transactions: 0
2025-07-30 18:56:28,393 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5af9f7ea-7f000001-0eea87c4-6eab4d57] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/directoryContent.jsp': Total: 0.254 s, CPU [user: 0.0542 s, system: 0.054 s], Allocated memory: 6.6 MB, transactions: 17, resolve: 0.0383 s [69% Revision (6x), 31% User (17x)] (23x), svn: 0.0222 s [100% log (3x)] (3x)
2025-07-30 18:56:28,780 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5af9f9d5-7f000001-0eea87c4-3375a651 | u:admin] INFO  TXLOGGER - Tx 6616be7e7a885_0_6616be7e7a885_0_: finished. Total: 0.13 s, CPU [user: 0.0376 s, system: 0.0219 s], Allocated memory: 3.3 MB, resolve: 0.0616 s [100% RichPage (1x)] (1x), svn: 0.0434 s [43% info (4x), 24% getDir2 content (1x), 21% getFile content (3x)] (10x), RepositoryConfigService: 0.0181 s [99% getReadConfiguration (13x)] (22x), ObjectMaps: 0.0139 s [27% getLastPromoted (1x), 26% getPrimaryObjectLocation (1x), 25% getLocalIdsForLocation (1x), 22% getPrimaryObjectProperty (1x)] (5x)
2025-07-30 18:56:28,783 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5af9f9d5-7f000001-0eea87c4-3375a651] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.153 s, CPU [user: 0.0443 s, system: 0.0272 s], Allocated memory: 7.2 MB, transactions: 1, PortalDataService: 0.13 s [100% requestPortalSite (1x)] (1x), resolve: 0.0616 s [100% RichPage (1x)] (1x), svn: 0.0434 s [43% info (4x), 24% getDir2 content (1x), 21% getFile content (3x)] (10x), RPC: 0.0216 s [90% decodeRequest (1x)] (4x), RepositoryConfigService: 0.0181 s [99% getReadConfiguration (13x)] (22x), ObjectMaps: 0.0139 s [27% getLastPromoted (1x), 26% getPrimaryObjectLocation (1x), 25% getLocalIdsForLocation (1x), 22% getPrimaryObjectProperty (1x)] (5x)
2025-07-30 18:56:33,917 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5afa0bd5-7f000001-0eea87c4-e540640c | u:admin] INFO  TXLOGGER - Tx 6616be837608f_0_6616be837608f_0_: finished. Total: 0.164 s, CPU [user: 0.0732 s, system: 0.0141 s], Allocated memory: 9.2 MB, svn: 0.028 s [53% info (4x), 16% getLatestRevision (1x), 15% getFile content (3x)] (10x), resolve: 0.0278 s [93% Plan (3x)] (11x), Lucene: 0.0277 s [100% search (2x)] (2x), WeakPObjectList: [secondGetHits: 1 ]
2025-07-30 18:56:33,919 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5afa0bd5-7f000001-0eea87c4-e540640c] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.682 s, CPU [user: 0.344 s, system: 0.0619 s], Allocated memory: 36.3 MB, transactions: 1, RPC: 0.504 s [100% decodeRequest (1x)] (4x), PlansData: 0.165 s [100% initialize (1x)] (1x), WeakPObjectList: [secondGetHits: 1 ]
2025-07-30 18:56:35,436 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5afa0bd0-7f000001-0eea87c4-6fd36ba4 | u:admin] INFO  TXLOGGER - Tx 6616be82f488e_0_6616be82f488e_0_: finished. Total: 2.2 s, CPU [user: 0.617 s, system: 0.146 s], Allocated memory: 183.0 MB, resolve: 1.44 s [100% RichPage (179x)] (232x), svn: 1.2 s [66% info (303x), 21% getDir2 content (25x)] (456x), DB: 0.183 s [45% query (161x), 34% update (161x), 21% commit (161x)] (846x), ObjectMaps: 0.17 s [29% getLastPromoted (125x), 27% getPrimaryObjectProperty (125x), 22% getPrimaryObjectLocation (125x), 22% getLocalIdsForLocation (125x)] (625x)
2025-07-30 18:56:35,447 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5afa0bd0-7f000001-0eea87c4-6fd36ba4] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 2.21 s, CPU [user: 0.623 s, system: 0.149 s], Allocated memory: 188.1 MB, transactions: 1, PortalDataService: 2.2 s [100% getWikiSpacesAndPages (1x)] (1x), resolve: 1.44 s [100% RichPage (179x)] (232x), svn: 1.2 s [66% info (303x), 21% getDir2 content (25x)] (456x), DB: 0.183 s [45% query (161x), 34% update (161x), 21% commit (161x)] (846x), ObjectMaps: 0.17 s [29% getLastPromoted (125x), 27% getPrimaryObjectProperty (125x), 22% getPrimaryObjectLocation (125x), 22% getLocalIdsForLocation (125x)] (625x)
