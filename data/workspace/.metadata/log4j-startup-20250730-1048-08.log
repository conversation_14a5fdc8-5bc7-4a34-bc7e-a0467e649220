2025-07-30 10:48:09,017 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:09,017 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:48:09,017 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:09,017 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:48:09,017 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:48:09,018 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:09,018 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:48:13,223 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:48:13,396 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.172 s. ]
2025-07-30 10:48:13,396 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:48:13,475 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0793 s. ]
2025-07-30 10:48:13,523 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:48:13,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 88 s. ]
2025-07-30 10:48:13,844 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.83 s. ]
2025-07-30 10:48:13,928 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:13,928 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:48:13,954 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 10:48:13,954 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:13,954 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:48:13,958 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 10:48:13,959 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 10:48:13,958 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 10:48:13,959 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 10:48:13,959 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 10:48:13,959 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 10:48:13,964 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:48:14,099 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:48:14,170 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:48:14,688 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-30 10:48:14,699 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:14,700 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:48:14,942 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:48:14,955 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-07-30 10:48:14,981 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:14,981 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:48:14,988 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:48:15,051 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:48:15,099 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:48:15,124 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:48:15,145 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:48:15,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:48:15,197 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:48:15,249 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:48:15,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:48:15,298 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 10:48:15,298 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:15,298 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:48:15,315 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:48:15,315 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:15,315 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:48:15,415 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:48:15,421 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:48:15,533 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-30 10:48:15,534 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:15,534 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:48:15,541 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:48:15,541 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:48:15,541 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:48:17,979 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.44 s. ]
2025-07-30 10:48:17,980 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:48:17,980 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.96 s. ]
2025-07-30 10:48:17,980 [main] INFO  com.polarion.platform.startup - ****************************************************************
