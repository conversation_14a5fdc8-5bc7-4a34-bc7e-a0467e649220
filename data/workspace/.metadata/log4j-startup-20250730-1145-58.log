2025-07-30 11:45:58,864 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:45:58,864 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:45:58,864 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:45:58,865 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:45:58,865 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:45:58,865 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:45:58,865 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:46:03,226 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:46:03,373 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.146 s. ]
2025-07-30 11:46:03,373 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:46:03,451 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0775 s. ]
2025-07-30 11:46:03,510 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:46:03,630 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 51 s. ]
2025-07-30 11:46:03,841 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.98 s. ]
2025-07-30 11:46:03,928 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:03,928 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:46:03,953 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:46:03,953 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:03,953 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 11:46:03,961 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 11:46:03,968 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:46:04,104 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:46:04,188 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:46:04,644 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-07-30 11:46:04,656 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:04,656 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:46:04,893 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:46:04,905 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 11:46:04,936 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:04,936 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:46:04,940 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:46:04,997 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:46:05,047 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:46:05,093 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:46:05,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:46:05,161 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:46:05,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:46:05,242 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:46:05,287 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:46:05,287 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.38 s. ]
2025-07-30 11:46:05,288 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:05,288 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:46:05,304 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:46:05,306 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:05,306 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:46:05,430 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:46:05,434 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:46:05,584 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.28 s. ]
2025-07-30 11:46:05,585 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:05,585 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:46:05,595 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:46:05,595 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:46:05,595 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:46:08,206 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.61 s. ]
2025-07-30 11:46:08,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:46:08,206 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.34 s. ]
2025-07-30 11:46:08,206 [main] INFO  com.polarion.platform.startup - ****************************************************************
