2025-07-29 19:57:00,537 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0589 s [58% update (144x), 41% query (12x)] (221x), svn: 0.0128 s [56% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-29 19:57:00,656 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0308 s [59% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-29 19:57:01,385 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.259 s, system: 0.312 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:57:01,386 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.0966 s, system: 0.135 s], Allocated memory: 12.2 MB, transactions: 0, ObjectMaps: 0.0887 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0618 s [68% log2 (10x), 25% getLatestRevision (2x)] (13x)
2025-07-29 19:57:01,386 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.0555 s, system: 0.0789 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0687 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:57:01,386 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.726 s, CPU [user: 0.0847 s, system: 0.105 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.118 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-29 19:57:01,386 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.224 s, system: 0.255 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.115 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:57:01,386 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.725 s, CPU [user: 0.153 s, system: 0.188 s], Allocated memory: 26.5 MB, transactions: 0, ObjectMaps: 0.0795 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0765 s [28% log2 (5x), 28% info (5x), 20% log (1x), 9% getLatestRevision (2x)] (18x)
2025-07-29 19:57:01,386 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.332 s [60% log2 (36x), 16% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-29 19:57:01,630 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.207 s [100% getReadConfiguration (48x)] (48x), svn: 0.0726 s [80% info (18x)] (38x)
2025-07-29 19:57:02,000 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.292 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.212 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:57:02,236 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.217 s [100% doFinishStartup (1x)] (1x), commit: 0.0384 s [100% Revision (1x)] (1x), Lucene: 0.0314 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0166 s [100% objectsToInv (1x)] (1x), DB: 0.0126 s [39% update (3x), 34% query (1x), 20% execute (1x)] (8x)
