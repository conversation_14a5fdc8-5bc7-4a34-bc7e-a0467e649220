2025-07-29 19:18:28,460 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:18:28,460 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:18:28,460 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:18:28,461 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:18:28,461 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:18:28,461 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:28,461 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:18:32,790 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:18:32,939 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-29 19:18:32,939 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:18:32,983 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0431 s. ]
2025-07-29 19:18:33,038 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:18:33,178 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 90 s. ]
2025-07-29 19:18:33,387 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.93 s. ]
2025-07-29 19:18:33,470 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:33,470 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:18:33,493 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 19:18:33,493 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:33,494 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:18:33,498 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 19:18:33,499 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 19:18:33,499 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-29 19:18:33,499 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 19:18:33,499 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 19:18:33,499 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 19:18:33,505 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:18:33,663 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:18:33,784 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:18:34,304 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.81 s. ]
2025-07-29 19:18:34,315 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:34,315 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:18:34,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:18:34,540 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 19:18:34,570 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:34,570 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:18:34,573 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:18:34,627 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:18:34,675 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:18:34,698 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:18:34,721 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:18:34,768 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:18:34,808 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:18:34,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:18:34,875 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:18:34,875 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.33 s. ]
2025-07-29 19:18:34,875 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:34,875 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:18:34,889 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:18:34,889 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:34,889 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:18:34,994 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:18:34,996 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:18:35,135 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-29 19:18:35,136 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:35,136 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:18:35,143 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:18:35,143 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:18:35,143 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:18:38,763 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.62 s. ]
2025-07-29 19:18:38,763 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:18:38,763 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-07-29 19:18:38,763 [main] INFO  com.polarion.platform.startup - ****************************************************************
