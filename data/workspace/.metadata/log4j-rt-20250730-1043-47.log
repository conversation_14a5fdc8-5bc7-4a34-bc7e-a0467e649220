2025-07-30 10:43:56,741 [Catalina-utility-1] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 10:43:57,625 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 10:43:57,627 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 10:43:57,628 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 10:43:57,631 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[91]')
2025-07-30 10:43:57,637 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 10:43:57,637 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 10:43:57,638 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[87]')
2025-07-30 10:43:58,549 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 10:43:58,661 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59371431-0a465820-2afd1646-40079e42] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 10:43:58,682 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 10:43:58,682 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
