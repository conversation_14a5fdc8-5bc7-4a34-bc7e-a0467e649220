2025-07-30 15:54:36,237 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.116 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0157 s [55% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-30 15:54:36,443 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0404 s [57% getDir2 content (2x), 36% info (3x)] (6x)
2025-07-30 15:54:37,318 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.872 s, CPU [user: 0.118 s, system: 0.205 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.101 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0499 s [78% log2 (5x), 13% testConnection (1x)] (7x)
2025-07-30 15:54:37,319 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.871 s, CPU [user: 0.0828 s, system: 0.095 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.142 s [71% log2 (10x), 14% testConnection (1x)] (13x)
2025-07-30 15:54:37,318 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.871 s, CPU [user: 0.0743 s, system: 0.089 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.122 s [29% log2 (5x), 22% log (1x), 22% info (5x), 17% getLatestRevision (2x)] (18x), ObjectMaps: 0.0715 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:54:37,319 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.871 s, CPU [user: 0.217 s, system: 0.28 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.136 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:54:37,318 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.872 s, CPU [user: 0.263 s, system: 0.34 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:54:37,319 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.872 s, CPU [user: 0.0876 s, system: 0.141 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.109 s [73% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.1 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:54:37,319 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.58 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.477 s [59% log2 (36x), 15% getLatestRevision (9x), 13% testConnection (6x)] (61x)
2025-07-30 15:54:37,465 [main | u:p] INFO  TXLOGGER - Tx 661694dee9401_0_661694dee9401_0_: finished. Total: 0.114 s, CPU [user: 0.0875 s, system: 0.00443 s], Allocated memory: 21.8 MB
2025-07-30 15:54:37,770 [main | u:p] INFO  TXLOGGER - Tx 661694df32410_0_661694df32410_0_: finished. Total: 0.128 s, CPU [user: 0.0105 s, system: 0.00169 s], Allocated memory: 555.3 kB, svn: 0.118 s [98% info (2x)] (4x)
2025-07-30 15:54:37,794 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.419 s [100% getReadConfiguration (48x)] (48x), svn: 0.236 s [92% info (18x)] (38x)
2025-07-30 15:54:38,298 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.374 s [76% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.289 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:54:38,554 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.107 s, CPU [user: 0.031 s, system: 0.00571 s], Allocated memory: 2.8 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:38,556 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.109 s, CPU [user: 0.0264 s, system: 0.00471 s], Allocated memory: 2.5 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:38,575 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.128 s, CPU [user: 0.00592 s, system: 0.00306 s], Allocated memory: 839.4 kB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:38,614 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.119 s, CPU [user: 0.0255 s, system: 0.00973 s], Allocated memory: 12.7 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:38,646 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.199 s, CPU [user: 0.035 s, system: 0.0113 s], Allocated memory: 10.6 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:38,716 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [Plan]: finished. Total: 0.103 s, CPU [user: 0.0114 s, system: 0.00594 s], Allocated memory: 6.0 MB
2025-07-30 15:54:38,737 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.124 s, CPU [user: 0.0408 s, system: 0.00911 s], Allocated memory: 10.6 MB, commit: 0.123 s [100% Revision (1x)] (1x)
2025-07-30 15:54:38,750 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.432 s [100% doFinishStartup (1x)] (1x), commit: 0.123 s [100% Revision (1x)] (1x), Lucene: 0.0906 s [100% refresh (1x)] (1x), DB: 0.0359 s [51% update (3x), 22% query (1x), 14% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0234 s [100% objectsToInv (1x)] (1x)
2025-07-30 15:54:41,761 [main | u:p | u:p] INFO  TXLOGGER - Tx 661694e31c83f_0_661694e31c83f_0_: finished. Total: 0.11 s, CPU [user: 0.0668 s, system: 0.00781 s], Allocated memory: 7.5 MB
2025-07-30 15:54:42,532 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.553 s [90% info (158x)] (170x), RepositoryConfigService: 0.0354 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 15:54:43,041 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661694e3fe044_0_661694e3fe044_0_: finished. Total: 0.486 s, CPU [user: 0.166 s, system: 0.026 s], Allocated memory: 20.3 MB, resolve: 0.157 s [57% User (2x), 41% Project (1x)] (5x), svn: 0.0447 s [32% getLatestRevision (2x), 27% info (1x), 20% log (1x), 14% testConnection (1x)] (8x), ObjectMaps: 0.0366 s [45% getPrimaryObjectProperty (2x), 36% getPrimaryObjectLocation (2x)] (11x), Lucene: 0.0358 s [100% search (1x)] (1x), GC: 0.035 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:54:43,690 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.00679 s, system: 0.00161 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 15:54:43,692 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.5 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.203 s [65% User (4x), 31% Project (1x)] (8x), Lucene: 0.0546 s [66% search (1x), 28% add (1x)] (3x), ObjectMaps: 0.0537 s [56% getPrimaryObjectLocation (4x), 31% getPrimaryObjectProperty (2x)] (13x), svn: 0.0447 s [32% getLatestRevision (2x), 27% info (1x), 20% log (1x), 14% testConnection (1x)] (8x), persistence listener: 0.0309 s [79% indexRefreshPersistenceListener (1x), 6% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0281 s [100% WorkItem (22x)] (22x)
2025-07-30 15:54:43,693 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.05 s, CPU [user: 0.182 s, system: 0.0321 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.927 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0681 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 15:54:44,235 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e3fbc40_0_661694e3fbc40_0_: finished. Total: 1.69 s, CPU [user: 0.394 s, system: 0.098 s], Allocated memory: 47.1 MB, svn: 1.04 s [48% getDatedRevision (181x), 37% getDir2 content (25x)] (307x), resolve: 0.47 s [100% Category (96x)] (96x), ObjectMaps: 0.171 s [40% getPrimaryObjectProperty (96x), 40% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (387x)
2025-07-30 15:54:44,496 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e5bc448_0_661694e5bc448_0_: finished. Total: 0.159 s, CPU [user: 0.0769 s, system: 0.0123 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0783 s [51% getReadConfiguration (162x), 49% getReadUserConfiguration (10x)] (172x), svn: 0.0697 s [57% info (19x), 38% getFile content (15x)] (36x), resolve: 0.0407 s [100% User (9x)] (9x), ObjectMaps: 0.0173 s [57% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 15:54:44,740 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e5f144a_0_661694e5f144a_0_: finished. Total: 0.191 s, CPU [user: 0.0515 s, system: 0.00566 s], Allocated memory: 19.9 MB, svn: 0.16 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0422 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 15:54:45,796 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e62104b_0_661694e62104b_0_: finished. Total: 1.06 s, CPU [user: 0.445 s, system: 0.0512 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.849 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.524 s [73% getFile content (412x), 27% getDir2 content (21x)] (434x)
2025-07-30 15:54:45,938 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e72904c_0_661694e72904c_0_: finished. Total: 0.141 s, CPU [user: 0.0298 s, system: 0.00385 s], Allocated memory: 17.8 MB, svn: 0.128 s [89% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0231 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 15:54:46,580 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661694e76344e_0_661694e76344e_0_: finished. Total: 0.551 s, CPU [user: 0.191 s, system: 0.0194 s], Allocated memory: 384.8 MB, RepositoryConfigService: 0.393 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.305 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 15:54:46,581 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.04 s, CPU [user: 1.28 s, system: 0.204 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.39 s [42% getDir2 content (115x), 34% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.45 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.56 s [84% Category (96x)] (117x), ObjectMaps: 0.204 s [43% getPrimaryObjectProperty (108x), 38% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 15:54:46,581 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.32 s [43% getDatedRevision (362x), 30% getDir2 content (115x), 24% getFile content (807x)] (1325x), RepositoryConfigService: 1.45 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.56 s [84% Category (96x)] (118x), ObjectMaps: 0.204 s [43% getPrimaryObjectProperty (108x), 38% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 15:55:12,456 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a540289-7f000001-6d654064-09d76ba0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.574 s, CPU [user: 0.277 s, system: 0.108 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.515 s [100% authenticate (1x)] (1x)
2025-07-30 15:55:12,680 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a540517-7f000001-6d654064-2d705e43] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.144 s, CPU [user: 0.0174 s, system: 0.00569 s], Allocated memory: 4.3 MB, transactions: 0, UICustomizationDataProvider: 0.0152 s [100% getUserDataValue (1x)] (1x)
2025-07-30 15:55:13,284 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a540755-7f000001-6d654064-9799a1ae] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.174 s, CPU [user: 0.0167 s, system: 0.00481 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 15:55:13,284 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a540711-7f000001-6d654064-9eab53f3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.243 s, CPU [user: 0.0938 s, system: 0.0214 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 15:55:13,371 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a54076a-7f000001-6d654064-c29eb062] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753862112881': Total: 0.241 s, CPU [user: 0.06 s, system: 0.0112 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 15:55:13,482 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a54076f-7f000001-6d654064-b02ea495 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753862112883] INFO  TXLOGGER - Tx 66169501f1051_0_66169501f1051_0_: finished. Total: 0.261 s, CPU [user: 0.0878 s, system: 0.0176 s], Allocated memory: 5.4 MB, svn: 0.0223 s [100% getFile content (2x)] (3x)
2025-07-30 15:55:13,504 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a54076f-7f000001-6d654064-b02ea495] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753862112883': Total: 0.368 s, CPU [user: 0.111 s, system: 0.0228 s], Allocated memory: 7.5 MB, transactions: 1, RepositoryConfigService: 0.263 s [100% getReadConfiguration (1x)] (1x), svn: 0.0223 s [100% getFile content (2x)] (3x)
2025-07-30 15:55:13,592 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a54076f-7f000001-6d654064-b5275d00] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753862112882': Total: 0.456 s, CPU [user: 0.0726 s, system: 0.0124 s], Allocated memory: 5.7 MB, transactions: 1, RepositoryConfigService: 0.318 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 15:59:39,690 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0845703125
2025-07-30 15:59:49,691 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0173828125
2025-07-30 15:59:59,687 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.00537109375
2025-07-30 16:00:29,687 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.14482421875
2025-07-30 16:00:39,691 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.030908203125
