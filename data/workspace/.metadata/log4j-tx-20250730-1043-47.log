2025-07-30 10:43:53,581 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0942 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0178 s [57% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-30 10:43:53,762 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0326 s [59% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.291 s, system: 0.334 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.164 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.0825 s, system: 0.0745 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.123 s [81% log2 (10x)] (13x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.234 s, system: 0.259 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.154 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.059 s, system: 0.0798 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0717 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0526 s [70% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.092 s, system: 0.107 s], Allocated memory: 12.2 MB, transactions: 0, svn: 0.0932 s [67% log2 (10x), 27% getLatestRevision (2x)] (13x), ObjectMaps: 0.0848 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:43:54,650 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.883 s, CPU [user: 0.153 s, system: 0.183 s], Allocated memory: 26.4 MB, transactions: 0, svn: 0.107 s [28% log2 (5x), 25% info (5x), 19% log (1x), 12% getLatestRevision (2x)] (18x), ObjectMaps: 0.0889 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:43:54,651 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.604 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.433 s [61% log2 (36x), 17% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 10:43:54,822 [main | u:p] INFO  TXLOGGER - Tx 66164dc12d401_0_66164dc12d401_0_: finished. Total: 0.144 s, CPU [user: 0.107 s, system: 0.00949 s], Allocated memory: 21.8 MB
2025-07-30 10:43:54,941 [main | u:p] INFO  TXLOGGER - Tx 66164dc152002_0_66164dc152002_0_: finished. Total: 0.117 s, CPU [user: 0.0611 s, system: 0.00732 s], Allocated memory: 27.0 MB, GC: 0.028 s [100% G1 Young Generation (1x)] (1x), svn: 0.0126 s [85% info (2x)] (4x)
2025-07-30 10:43:55,056 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.356 s [100% getReadConfiguration (48x)] (48x), svn: 0.0964 s [81% info (18x)] (38x)
2025-07-30 10:43:55,526 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.37 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.279 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:43:55,802 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.256 s [100% doFinishStartup (1x)] (1x), commit: 0.0558 s [100% Revision (1x)] (1x), Lucene: 0.0362 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0215 s [100% objectsToInv (1x)] (1x)
2025-07-30 10:43:58,729 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.527 s [89% info (158x)] (168x)
2025-07-30 10:43:59,035 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66164dc524845_0_66164dc524845_0_: finished. Total: 0.297 s, CPU [user: 0.145 s, system: 0.021 s], Allocated memory: 21.0 MB, resolve: 0.0855 s [60% User (2x), 37% Project (1x)] (5x), Lucene: 0.0412 s [100% search (1x)] (1x), svn: 0.027 s [48% getLatestRevision (3x), 23% log (1x), 14% testConnection (1x)] (9x), ObjectMaps: 0.0269 s [55% getPrimaryObjectLocation (2x), 39% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 10:43:59,616 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.788 s, CPU [user: 0.00594 s, system: 0.00138 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 10:43:59,617 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.31 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.111 s [68% User (3x), 29% Project (1x)] (7x), Lucene: 0.0474 s [87% search (1x)] (2x), svn: 0.0415 s [53% getLatestRevision (4x), 22% testConnection (2x), 15% log (1x)] (11x), ObjectMaps: 0.0324 s [62% getPrimaryObjectLocation (3x), 32% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0251 s [100% WorkItem (22x)] (22x), persistence listener: 0.0165 s [69% indexRefreshPersistenceListener (1x), 14% PlanActivityCreator (1x)] (7x)
2025-07-30 10:43:59,618 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.896 s, CPU [user: 0.167 s, system: 0.0274 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.699 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0539 s [81% buildBaselineSnapshots (1x)] (24x)
2025-07-30 10:44:00,238 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164dc523c41_0_66164dc523c41_0_: finished. Total: 1.5 s, CPU [user: 0.38 s, system: 0.093 s], Allocated memory: 47.1 MB, svn: 0.979 s [59% getDatedRevision (181x), 27% getDir2 content (25x)] (307x), resolve: 0.38 s [100% Category (96x)] (96x), ObjectMaps: 0.134 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 10:44:00,444 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164dc6ae048_0_66164dc6ae048_0_: finished. Total: 0.131 s, CPU [user: 0.064 s, system: 0.0109 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0648 s [57% getReadConfiguration (162x), 43% getReadUserConfiguration (10x)] (172x), svn: 0.0562 s [58% info (19x), 37% getFile content (15x)] (36x), resolve: 0.0315 s [100% User (9x)] (9x), ObjectMaps: 0.0157 s [56% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 10:44:00,736 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164dc6dc84a_0_66164dc6dc84a_0_: finished. Total: 0.237 s, CPU [user: 0.077 s, system: 0.00876 s], Allocated memory: 19.9 MB, svn: 0.185 s [69% getDir2 content (17x), 31% getFile content (44x)] (62x), RepositoryConfigService: 0.0886 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 10:44:01,684 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164dc71804b_0_66164dc71804b_0_: finished. Total: 0.948 s, CPU [user: 0.396 s, system: 0.0354 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.709 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.536 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 10:44:03,178 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164dc82344e_0_66164dc82344e_0_: finished. Total: 1.37 s, CPU [user: 0.345 s, system: 0.102 s], Allocated memory: 385.4 MB, RepositoryConfigService: 0.946 s [98% getReadConfiguration (2787x)] (3025x), svn: 0.762 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x)
2025-07-30 10:44:03,178 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.44 s, CPU [user: 1.35 s, system: 0.263 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.68 s [38% getDir2 content (115x), 38% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.88 s [96% getReadConfiguration (12019x)] (12691x), resolve: 0.443 s [86% Category (96x)] (117x)
2025-07-30 10:44:03,178 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.38 s [38% getDatedRevision (362x), 30% getDir2 content (115x), 30% getFile content (807x)] (1325x), RepositoryConfigService: 1.88 s [96% getReadConfiguration (12019x)] (12691x), resolve: 0.445 s [85% Category (96x)] (118x)
2025-07-30 10:44:05,391 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59372b35-0a465820-2afd1646-a6159f7a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.856 s, CPU [user: 0.37 s, system: 0.103 s], Allocated memory: 42.0 MB, transactions: 2, PolarionAuthenticator: 0.75 s [100% authenticate (1x)] (1x)
2025-07-30 10:44:05,917 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59372fc9-0a465820-2afd1646-0ed026a6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.211 s, CPU [user: 0.0829 s, system: 0.0202 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 10:44:05,917 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59372ffc-0a465820-2afd1646-50ccbd00] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.161 s, CPU [user: 0.0216 s, system: 0.00472 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 10:44:05,957 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59372ffe-0a465820-2afd1646-a12f2c7a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753843445657': Total: 0.198 s, CPU [user: 0.0408 s, system: 0.00611 s], Allocated memory: 1.8 MB, transactions: 0
2025-07-30 10:44:06,044 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59373005-0a465820-2afd1646-76371241 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843445658] INFO  TXLOGGER - Tx 66164dcc17c51_0_66164dcc17c51_0_: finished. Total: 0.188 s, CPU [user: 0.0727 s, system: 0.0163 s], Allocated memory: 7.9 MB, svn: 0.0137 s [59% testConnection (1x), 41% getFile content (2x)] (4x)
2025-07-30 10:44:06,050 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59373005-0a465820-2afd1646-76371241] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843445658': Total: 0.284 s, CPU [user: 0.0854 s, system: 0.0191 s], Allocated memory: 9.1 MB, transactions: 1, RepositoryConfigService: 0.192 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 10:44:06,089 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59373006-0a465820-2afd1646-f601c672] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753843445659': Total: 0.322 s, CPU [user: 0.0443 s, system: 0.00583 s], Allocated memory: 4.6 MB, transactions: 1, RepositoryConfigService: 0.232 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 10:45:17,734 [ajp-nio-127.0.0.1-8889-exec-10 | cID:59384881-0a465820-2afd1646-dfab2fed] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileContent.jsp?url=WBSdev%2F.polarion%2Fsynchronizer%2Fconfiguration.xml': Total: 0.163 s, CPU [user: 0.0364 s, system: 0.0524 s], Allocated memory: 3.0 MB, transactions: 1, PolarionAuthenticator: 0.0118 s [100% authenticate (1x)] (1x)
