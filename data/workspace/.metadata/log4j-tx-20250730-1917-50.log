2025-07-30 19:17:55,336 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0565 s [67% update (144x), 33% query (12x)] (221x), svn: 0.0133 s [59% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 19:17:55,454 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0334 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-30 19:17:56,189 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.218 s, system: 0.266 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.148 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:17:56,189 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0567 s, system: 0.0848 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0486 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:17:56,190 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0738 s, system: 0.086 s], Allocated memory: 8.8 MB, transactions: 0, svn: 0.0708 s [81% log2 (10x)] (13x), ObjectMaps: 0.0557 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 19:17:56,190 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.139 s, system: 0.196 s], Allocated memory: 26.1 MB, transactions: 0, ObjectMaps: 0.0773 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.068 s [29% log2 (5x), 26% info (5x), 22% log (1x), 10% getLatestRevision (2x)] (18x)
2025-07-30 19:17:56,190 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0918 s, system: 0.145 s], Allocated memory: 14.3 MB, transactions: 0, ObjectMaps: 0.0785 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.059 s [68% log2 (10x), 22% getLatestRevision (2x)] (13x)
2025-07-30 19:17:56,190 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.271 s, system: 0.33 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.127 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 19:17:56,192 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.535 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.27 s [61% log2 (36x), 15% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 19:17:56,542 [main | u:p] INFO  TXLOGGER - Tx 6616c367df401_0_6616c367df401_0_: finished. Total: 0.319 s, CPU [user: 0.136 s, system: 0.0154 s], Allocated memory: 21.8 MB
2025-07-30 19:17:56,871 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.601 s [100% getReadConfiguration (48x)] (48x), svn: 0.189 s [86% info (18x)] (38x)
2025-07-30 19:17:57,432 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.417 s [73% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.345 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 19:17:58,036 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.163 s, CPU [user: 0.0352 s, system: 0.00584 s], Allocated memory: 2.8 MB
2025-07-30 19:17:58,037 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.166 s, CPU [user: 0.031 s, system: 0.00526 s], Allocated memory: 2.3 MB
2025-07-30 19:17:58,039 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.166 s, CPU [user: 0.00333 s, system: 0.00171 s], Allocated memory: 757.1 kB
2025-07-30 19:17:58,081 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.14 s, CPU [user: 0.0308 s, system: 0.0104 s], Allocated memory: 12.7 MB
2025-07-30 19:17:58,129 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.256 s, CPU [user: 0.0293 s, system: 0.0076 s], Allocated memory: 10.4 MB
2025-07-30 19:17:58,223 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.734 s [100% doFinishStartup (1x)] (1x), Lucene: 0.14 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0954 s [100% objectsToInv (1x)] (1x), commit: 0.0784 s [100% Revision (1x)] (1x), SubterraURITable: 0.0459 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-30 19:18:02,859 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.566 s [88% info (158x)] (170x)
2025-07-30 19:18:03,238 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616c36e5f043_0_6616c36e5f043_0_: finished. Total: 0.362 s, CPU [user: 0.151 s, system: 0.02 s], Allocated memory: 20.2 MB, resolve: 0.0924 s [59% User (2x), 38% Project (1x)] (5x), Lucene: 0.0368 s [100% search (1x)] (1x), ObjectMaps: 0.0299 s [45% getPrimaryObjectLocation (2x), 43% getPrimaryObjectProperty (2x)] (11x), svn: 0.0286 s [30% getLatestRevision (2x), 22% log (1x), 21% testConnection (1x), 17% getFile content (2x)] (8x), linkedWorkItemsQueryExpander: 0.019 s [100% expand (1x)] (2x)
2025-07-30 19:18:04,081 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.06 s, CPU [user: 0.00718 s, system: 0.00222 s], Allocated memory: 324.0 kB, transactions: 1
2025-07-30 19:18:04,083 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.384 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.191 s [78% User (4x), 18% Project (1x)] (8x), Lucene: 0.0811 s [45% search (1x), 43% add (1x)] (3x), ObjectMaps: 0.0618 s [73% getPrimaryObjectLocation (4x), 21% getPrimaryObjectProperty (2x)] (13x), Incremental Baseline: 0.0297 s [100% WorkItem (22x)] (22x), svn: 0.0286 s [30% getLatestRevision (2x), 22% log (1x), 21% testConnection (1x), 17% getFile content (2x)] (8x), persistence listener: 0.0261 s [83% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 19:18:04,084 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.06 s, CPU [user: 0.187 s, system: 0.0315 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.919 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0768 s [81% buildBaselineSnapshots (1x)] (24x)
2025-07-30 19:18:04,774 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c36e5d040_0_6616c36e5d040_0_: finished. Total: 1.9 s, CPU [user: 0.409 s, system: 0.105 s], Allocated memory: 46.6 MB, svn: 1.16 s [55% getDatedRevision (181x), 27% getDir2 content (25x)] (307x), resolve: 0.563 s [100% Category (96x)] (96x), ObjectMaps: 0.211 s [37% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 19:18:05,088 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c3705a848_0_6616c3705a848_0_: finished. Total: 0.18 s, CPU [user: 0.0789 s, system: 0.0152 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0888 s [57% getReadConfiguration (162x), 43% getReadUserConfiguration (10x)] (172x), svn: 0.0787 s [60% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0394 s [100% User (9x)] (9x), ObjectMaps: 0.0168 s [60% getPrimaryObjectProperty (8x), 21% getPrimaryObjectLocation (8x)] (32x), Lucene: 0.0142 s [100% search (1x)] (1x)
2025-07-30 19:18:05,353 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c3709984a_0_6616c3709984a_0_: finished. Total: 0.195 s, CPU [user: 0.0559 s, system: 0.00573 s], Allocated memory: 19.9 MB, svn: 0.154 s [80% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0526 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 19:18:06,510 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c370ca44b_0_6616c370ca44b_0_: finished. Total: 1.16 s, CPU [user: 0.496 s, system: 0.0422 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.908 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.594 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x), GC: 0.063 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 19:18:06,657 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c371eb84c_0_6616c371eb84c_0_: finished. Total: 0.147 s, CPU [user: 0.0335 s, system: 0.00414 s], Allocated memory: 17.8 MB, svn: 0.127 s [75% getDir2 content (18x), 25% getFile content (29x)] (48x), RepositoryConfigService: 0.0464 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 19:18:06,764 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c3721084d_0_6616c3721084d_0_: finished. Total: 0.105 s, CPU [user: 0.0274 s, system: 0.0034 s], Allocated memory: 5.3 MB, svn: 0.0945 s [89% getDir2 content (11x)] (20x), RepositoryConfigService: 0.018 s [96% getReadConfiguration (38x)] (54x)
2025-07-30 19:18:07,245 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c3722b04e_0_6616c3722b04e_0_: finished. Total: 0.481 s, CPU [user: 0.168 s, system: 0.0146 s], Allocated memory: 384.3 MB, svn: 0.337 s [61% getDir2 content (21x), 39% getFile content (185x)] (207x), RepositoryConfigService: 0.251 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 19:18:07,245 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.38 s, CPU [user: 1.35 s, system: 0.207 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.63 s [39% getDir2 content (115x), 34% getFile content (807x), 24% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.43 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.66 s [85% Category (96x)] (117x), ObjectMaps: 0.251 s [39% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 19:18:07,245 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.56 s [44% getDatedRevision (362x), 29% getDir2 content (115x), 25% getFile content (807x)] (1326x), RepositoryConfigService: 1.43 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.66 s [85% Category (96x)] (118x), ObjectMaps: 0.251 s [39% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
2025-07-30 19:24:12,568 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5b11c4b8-0a465820-0533ad9b-43205082] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 105 s, CPU [user: 0.753 s, system: 0.156 s], Allocated memory: 76.8 MB, transactions: 2, PolarionAuthenticator: 104 s [100% authenticate (1x)] (1x), interceptor: 104 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 19:24:13,195 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5b11c4cb-0a465820-0533ad9b-87105935] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 105 s, CPU [user: 0.025 s, system: 0.0119 s], Allocated memory: 2.4 MB, transactions: 1, PolarionAuthenticator: 105 s [100% authenticate (1x)] (1x), interceptor: 105 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 19:24:13,599 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5b136052-0a465820-0533ad9b-5945dc31 | u:admin | i:WBSdev/07_Ruan Jian Jia Gou/Home] INFO  TXLOGGER - Tx 6616c4d817052_0_6616c4d817052_0_: finished. Total: 0.323 s, CPU [user: 0.179 s, system: 0.0346 s], Allocated memory: 14.5 MB, svn: 0.0367 s [64% info (4x), 22% testConnection (1x)] (8x), resolve: 0.0284 s [99% RichPage (1x)] (3x)
2025-07-30 19:24:13,645 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5b136052-0a465820-0533ad9b-5945dc31] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.379 s, CPU [user: 0.207 s, system: 0.0428 s], Allocated memory: 18.3 MB, transactions: 1, RpeModel: 0.325 s [100% initialize (1x)] (1x), RPC: 0.0481 s [92% encodeResponse (1x)] (4x), svn: 0.0367 s [64% info (4x), 22% testConnection (1x)] (8x), resolve: 0.0284 s [99% RichPage (1x)] (3x)
2025-07-30 19:24:13,991 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5b13627f-0a465820-0533ad9b-7f2a2d15] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.168 s, CPU [user: 0.0211 s, system: 0.0063 s], Allocated memory: 4.7 MB, transactions: 1, RPC: 0.0975 s [98% decodeRequest (1x)] (4x), RenderingRequest: 0.0654 s [100% initialize (1x)] (1x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x), Velocity: 0.016 s [100% evaluate (1x)] (1x)
2025-07-30 19:24:13,991 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5b136279-0a465820-0533ad9b-f788038e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.174 s, CPU [user: 0.0448 s, system: 0.0127 s], Allocated memory: 7.5 MB, transactions: 1, RPC: 0.1 s [98% decodeRequest (1x)] (4x), RenderingRequest: 0.0683 s [100% initialize (1x)] (1x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 19:24:14,108 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5b136284-0a465820-0533ad9b-c4729ced | u:admin | i:WBSdev/07_Ruan Jian Jia Gou/Home e:polarion_client10_iw_1] INFO  TXLOGGER - Tx 6616c4d8b9459_0_6616c4d8b9459_0_: finished. Total: 0.183 s, CPU [user: 0.0332 s, system: 0.0133 s], Allocated memory: 8.3 MB, Velocity: 0.136 s [100% evaluate (1x)] (1x), DB: 0.0265 s [57% query (2x), 22% commit (2x), 20% update (2x)] (61x)
2025-07-30 19:24:14,109 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5b136280-0a465820-0533ad9b-59f7e150 | u:admin | i:WBSdev/07_Ruan Jian Jia Gou/Home e:polarion_client16_iw_1] INFO  TXLOGGER - Tx 6616c4d8b8c57_0_6616c4d8b8c57_0_: finished. Total: 0.186 s, CPU [user: 0.0124 s, system: 0.00661 s], Allocated memory: 3.7 MB, Velocity: 0.126 s [100% evaluate (1x)] (1x), DB: 0.0416 s [85% query (2x)] (61x)
2025-07-30 19:24:14,110 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5b136284-0a465820-0533ad9b-c4729ced] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.282 s, CPU [user: 0.0427 s, system: 0.0155 s], Allocated memory: 9.7 MB, transactions: 1, RenderingRequest: 0.184 s [100% initialize (1x)] (1x), Velocity: 0.136 s [100% evaluate (1x)] (1x), RPC: 0.0956 s [99% decodeRequest (1x)] (4x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x), DB: 0.0265 s [57% query (2x), 22% commit (2x), 20% update (2x)] (61x)
2025-07-30 19:24:14,113 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5b136280-0a465820-0533ad9b-59f7e150] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.286 s, CPU [user: 0.0225 s, system: 0.00891 s], Allocated memory: 5.2 MB, transactions: 1, RenderingRequest: 0.186 s [100% initialize (1x)] (1x), Velocity: 0.126 s [100% evaluate (1x)] (1x), RPC: 0.0972 s [98% decodeRequest (1x)] (4x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x), DB: 0.0416 s [85% query (2x)] (61x)
2025-07-30 19:24:14,221 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5b13627f-0a465820-0533ad9b-a7c86c9f | u:admin | i:WBSdev/07_Ruan Jian Jia Gou/Home e:polarion_client9] INFO  TXLOGGER - Tx 6616c4d8b8856_0_6616c4d8b8856_0_: finished. Total: 0.299 s, CPU [user: 0.0742 s, system: 0.0239 s], Allocated memory: 18.3 MB, resolve: 0.102 s [100% RichPage (6x)] (13x), Lucene: 0.0973 s [99% getCount (3x)] (6x), svn: 0.0835 s [70% info (11x), 21% getDir2 content (1x)] (18x)
2025-07-30 19:24:14,223 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5b13627f-0a465820-0533ad9b-a7c86c9f] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.399 s, CPU [user: 0.0853 s, system: 0.0258 s], Allocated memory: 20.1 MB, transactions: 1, RenderingRequest: 0.3 s [100% initialize (1x)] (1x), resolve: 0.102 s [100% RichPage (6x)] (13x), Lucene: 0.0973 s [99% getCount (3x)] (6x), RPC: 0.0973 s [97% decodeRequest (1x)] (4x), svn: 0.0835 s [70% info (11x), 21% getDir2 content (1x)] (18x), GC: 0.047 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 19:26:05,382 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.301 s, CPU [user: 0.00187 s, system: 0.00542 s], Allocated memory: 130.7 kB, transactions: 0, PullingJob: 0.279 s [100% collectChanges (1x)] (1x), svn: 0.276 s [100% getLatestRevision (1x)] (1x)
2025-07-30 19:26:10,431 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.00498046875
2025-07-30 19:26:40,434 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0123046875
2025-07-30 19:41:52,178 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.412 s, CPU [user: 0.0023 s, system: 0.0123 s], Allocated memory: 129.6 kB, transactions: 0, PullingJob: 0.318 s [100% collectChanges (1x)] (1x), svn: 0.285 s [100% getLatestRevision (1x)] (1x)
2025-07-30 19:42:00,414 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 2.10751953125
2025-07-30 19:42:10,406 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.91123046875
2025-07-30 19:42:20,402 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.66953125
2025-07-30 19:42:30,401 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.435546875
2025-07-30 19:42:40,405 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.244873046875
2025-07-30 19:42:50,401 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.114306640625
