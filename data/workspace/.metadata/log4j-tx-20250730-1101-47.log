2025-07-30 11:01:52,839 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0562 s [58% update (144x), 41% query (12x)] (221x), svn: 0.00992 s [51% getLatestRevision (2x), 36% testConnection (1x)] (4x)
2025-07-30 11:01:52,970 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0319 s [52% getDir2 content (2x), 41% info (3x)] (6x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.0712 s, system: 0.0944 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0985 s [80% log2 (10x)] (13x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.185 s, system: 0.263 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.104 s, system: 0.196 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0773 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.074 s, system: 0.137 s], Allocated memory: 12.2 MB, transactions: 0, svn: 0.0848 s [74% log2 (10x), 18% getLatestRevision (2x)] (13x), ObjectMaps: 0.0735 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.0466 s, system: 0.0927 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0654 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.048 s [69% log2 (5x), 20% getLatestRevision (1x)] (7x)
2025-07-30 11:01:53,671 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.698 s, CPU [user: 0.25 s, system: 0.325 s], Allocated memory: 72.4 MB, transactions: 0, ObjectMaps: 0.137 s [100% getAllPrimaryObjects (1x)] (12x), svn: 0.073 s [26% log (1x), 25% log2 (5x), 22% info (5x), 14% getLatestRevision (2x)] (18x)
2025-07-30 11:01:53,672 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.498 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.348 s [62% log2 (36x), 16% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 11:01:53,800 [main | u:p] INFO  TXLOGGER - Tx 661651dee7c01_0_661651dee7c01_0_: finished. Total: 0.103 s, CPU [user: 0.0788 s, system: 0.00394 s], Allocated memory: 21.8 MB
2025-07-30 11:01:53,950 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.24 s [100% getReadConfiguration (48x)] (48x), svn: 0.0805 s [86% info (18x)] (38x)
2025-07-30 11:01:54,323 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.287 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.222 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:01:54,570 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.231 s [100% doFinishStartup (1x)] (1x), commit: 0.0439 s [100% Revision (1x)] (1x), Lucene: 0.0395 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.017 s [100% objectsToInv (1x)] (1x), DB: 0.0134 s [46% update (3x), 24% execute (1x), 21% query (1x)] (8x)
2025-07-30 11:01:57,425 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.328 s [89% info (158x)] (168x)
2025-07-30 11:01:57,827 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661651e294844_0_661651e294844_0_: finished. Total: 0.368 s, CPU [user: 0.166 s, system: 0.0208 s], Allocated memory: 20.8 MB, resolve: 0.0796 s [55% User (2x), 38% Project (1x)] (5x), Lucene: 0.0416 s [100% search (1x)] (1x), svn: 0.0265 s [52% getLatestRevision (3x), 23% log (1x), 11% testConnection (1x)] (9x), ObjectMaps: 0.0192 s [54% getPrimaryObjectProperty (2x), 38% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 11:01:58,330 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.776 s, CPU [user: 0.00675 s, system: 0.00156 s], Allocated memory: 529.6 kB, transactions: 1
2025-07-30 11:01:58,331 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.385 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.152 s [74% User (4x), 20% Project (1x)] (8x), Lucene: 0.0615 s [68% search (1x), 27% add (1x)] (3x), svn: 0.0462 s [41% getLatestRevision (4x), 37% testConnection (2x), 13% log (1x)] (11x), ObjectMaps: 0.0451 s [74% getPrimaryObjectLocation (4x), 23% getPrimaryObjectProperty (2x)] (13x), persistence listener: 0.0438 s [77% indexRefreshPersistenceListener (1x), 18% WorkItemActivityCreator (1x)] (7x), PullingJob: 0.0201 s [100% collectChanges (1x)] (1x), Incremental Baseline: 0.0197 s [100% WorkItem (22x)] (22x)
2025-07-30 11:01:58,332 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.784 s, CPU [user: 0.171 s, system: 0.0242 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.642 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0789 s [91% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:01:58,703 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e28f040_0_661651e28f040_0_: finished. Total: 1.27 s, CPU [user: 0.322 s, system: 0.0817 s], Allocated memory: 46.6 MB, svn: 0.772 s [43% getDatedRevision (181x), 42% getDir2 content (25x)] (307x), resolve: 0.35 s [100% Category (96x)] (96x), ObjectMaps: 0.129 s [41% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 11:01:58,869 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e3d8847_0_661651e3d8847_0_: finished. Total: 0.114 s, CPU [user: 0.0254 s, system: 0.00814 s], Allocated memory: 3.1 MB, resolve: 0.102 s [100% Project (5x)] (5x), svn: 0.0703 s [67% info (4x), 18% getFile content (4x)] (10x), ObjectMaps: 0.0284 s [77% getPrimaryObjectProperty (4x), 15% getPrimaryObjectLocation (4x)] (17x)
2025-07-30 11:01:58,997 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e3f5448_0_661651e3f5448_0_: finished. Total: 0.128 s, CPU [user: 0.06 s, system: 0.0101 s], Allocated memory: 8.0 MB, svn: 0.0599 s [53% info (19x), 35% getFile content (15x)] (36x), RepositoryConfigService: 0.0547 s [50% getReadConfiguration (162x), 50% getReadUserConfiguration (10x)] (172x), resolve: 0.0439 s [100% User (9x)] (9x), ObjectMaps: 0.0205 s [63% getPrimaryObjectProperty (8x), 19% getLastPromoted (8x)] (32x)
2025-07-30 11:01:59,273 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e420c4a_0_661651e420c4a_0_: finished. Total: 0.23 s, CPU [user: 0.0807 s, system: 0.00918 s], Allocated memory: 19.9 MB, svn: 0.176 s [65% getDir2 content (17x), 35% getFile content (44x)] (62x), RepositoryConfigService: 0.0973 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 11:02:00,099 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e45a44b_0_661651e45a44b_0_: finished. Total: 0.826 s, CPU [user: 0.384 s, system: 0.0265 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.65 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.41 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-30 11:02:00,468 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661651e54344e_0_661651e54344e_0_: finished. Total: 0.263 s, CPU [user: 0.114 s, system: 0.00654 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.175 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.161 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-07-30 11:02:00,468 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.03 s, CPU [user: 1.06 s, system: 0.151 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.79 s [41% getDir2 content (115x), 34% getFile content (807x), 18% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.04 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.497 s [70% Category (96x), 21% Project (6x)] (117x), ObjectMaps: 0.178 s [50% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 11:02:00,469 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.43 s [39% getDatedRevision (362x), 30% getDir2 content (115x), 25% getFile content (807x)] (1324x), RepositoryConfigService: 1.04 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.497 s [70% Category (96x), 21% Project (6x)] (118x), ObjectMaps: 0.178 s [50% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x), Lucene: 0.129 s [55% buildBaselineSnapshots (2x), 36% search (5x)] (54x)
2025-07-30 11:02:16,393 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5947d277-0a465820-75eb3fad-cc4c6ac2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.464 s, CPU [user: 0.235 s, system: 0.0735 s], Allocated memory: 42.1 MB, transactions: 2, PolarionAuthenticator: 0.395 s [100% authenticate (1x)] (1x)
2025-07-30 11:02:16,803 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5947d537-0a465820-75eb3fad-ffd81ef5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.171 s, CPU [user: 0.0813 s, system: 0.0193 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:02:16,803 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5947d555-0a465820-75eb3fad-aa1bc734] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.141 s, CPU [user: 0.0193 s, system: 0.00562 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 11:02:16,828 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5947d557-0a465820-75eb3fad-c2bdd21e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753844536596': Total: 0.164 s, CPU [user: 0.0369 s, system: 0.00756 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 11:02:16,885 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5947d557-0a465820-75eb3fad-b30c0118 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753844536597] INFO  TXLOGGER - Tx 661651f572c51_0_661651f572c51_0_: finished. Total: 0.105 s, CPU [user: 0.0504 s, system: 0.0172 s], Allocated memory: 7.9 MB, svn: 0.0114 s [53% getFile content (2x), 46% testConnection (1x)] (4x)
2025-07-30 11:02:16,887 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5947d557-0a465820-75eb3fad-b30c0118] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753844536597': Total: 0.224 s, CPU [user: 0.0639 s, system: 0.0207 s], Allocated memory: 9.2 MB, transactions: 1, RepositoryConfigService: 0.107 s [100% getReadConfiguration (1x)] (1x), svn: 0.0114 s [53% getFile content (2x), 46% testConnection (1x)] (4x)
2025-07-30 11:02:16,903 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5947d558-0a465820-75eb3fad-42fdedbf] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753844536598': Total: 0.237 s, CPU [user: 0.0294 s, system: 0.00584 s], Allocated memory: 4.6 MB, transactions: 1, RepositoryConfigService: 0.122 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:07:13,149 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.16 s, CPU [user: 0.00237 s, system: 0.00881 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0623 s [100% collectChanges (1x)] (1x), svn: 0.0499 s [100% getLatestRevision (1x)] (1x)
