2025-07-30 15:09:50,918 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 15:09:51,545 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 15:09:51,547 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 15:09:51,548 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 15:09:51,549 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[91]')
2025-07-30 15:09:51,707 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 15:09:51,708 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[85]')
2025-07-30 15:09:51,708 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[87]')
2025-07-30 15:09:52,351 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 15:09:52,462 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a2a83ba-7f000001-0cb06460-b0ccee0a] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 15:09:52,483 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 15:09:52,483 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
