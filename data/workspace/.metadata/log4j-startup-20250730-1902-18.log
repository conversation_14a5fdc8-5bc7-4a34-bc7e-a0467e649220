2025-07-30 19:02:18,445 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:02:18,445 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 19:02:18,445 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:02:18,445 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 19:02:18,446 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 19:02:18,446 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:18,446 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 19:02:22,722 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 19:02:22,883 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.161 s. ]
2025-07-30 19:02:22,883 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 19:02:22,948 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0647 s. ]
2025-07-30 19:02:23,017 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 19:02:23,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-07-30 19:02:23,385 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.95 s. ]
2025-07-30 19:02:23,483 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:23,483 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 19:02:23,509 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 19:02:23,510 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:23,510 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 19:02:23,516 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 19:02:23,525 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 19:02:23,651 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 19:02:23,771 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 19:02:24,273 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-30 19:02:24,284 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:24,284 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 19:02:24,510 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 19:02:24,527 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 19:02:24,553 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:24,553 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 19:02:24,557 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 19:02:24,619 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 19:02:24,659 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 19:02:24,698 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 19:02:24,743 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 19:02:24,767 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 19:02:24,789 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 19:02:24,831 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 19:02:24,867 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 19:02:24,867 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 19:02:24,868 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:24,868 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 19:02:24,884 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 19:02:24,884 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:24,884 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 19:02:25,003 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 19:02:25,007 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 19:02:25,146 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 19:02:25,147 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:25,147 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 19:02:25,154 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 19:02:25,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:02:25,154 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 19:02:28,168 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.01 s. ]
2025-07-30 19:02:28,169 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:02:28,169 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.72 s. ]
2025-07-30 19:02:28,169 [main] INFO  com.polarion.platform.startup - ****************************************************************
