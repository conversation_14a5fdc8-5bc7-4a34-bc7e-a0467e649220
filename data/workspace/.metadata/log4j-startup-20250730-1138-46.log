2025-07-30 11:38:46,221 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:38:46,221 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:38:46,221 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:38:46,222 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:38:46,222 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:38:46,222 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:46,222 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:38:51,207 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:38:51,360 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.153 s. ]
2025-07-30 11:38:51,360 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:38:51,443 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0821 s. ]
2025-07-30 11:38:51,521 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:38:51,639 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 54 s. ]
2025-07-30 11:38:51,856 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.64 s. ]
2025-07-30 11:38:51,947 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:51,947 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:38:51,982 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 11:38:51,983 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:51,983 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:38:51,991 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:38:51,991 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 11:38:51,992 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:38:51,991 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 11:38:51,991 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 11:38:51,991 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-30 11:38:52,008 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:38:52,208 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:38:52,301 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:38:52,857 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.88 s. ]
2025-07-30 11:38:52,880 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:52,880 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:38:53,129 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:38:53,142 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-07-30 11:38:53,172 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:53,172 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:38:53,179 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:38:53,242 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:38:53,292 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:38:53,322 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:38:53,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:38:53,377 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:38:53,401 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:38:53,430 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:38:53,457 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:38:53,458 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-30 11:38:53,458 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:53,458 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:38:53,472 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:38:53,473 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:53,473 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:38:53,596 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:38:53,601 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:38:53,710 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-30 11:38:53,710 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:53,711 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:38:53,722 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:38:53,722 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:38:53,722 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:38:56,779 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.06 s. ]
2025-07-30 11:38:56,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:38:56,780 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.6 s. ]
2025-07-30 11:38:56,780 [main] INFO  com.polarion.platform.startup - ****************************************************************
