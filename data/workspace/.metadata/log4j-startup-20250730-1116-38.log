2025-07-30 11:16:38,545 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:16:38,545 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:16:38,545 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:16:38,546 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:16:38,546 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:16:38,546 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:38,546 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:16:42,827 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:16:42,979 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.152 s. ]
2025-07-30 11:16:42,979 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:16:43,026 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0462 s. ]
2025-07-30 11:16:43,080 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:16:43,196 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 67 s. ]
2025-07-30 11:16:43,407 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.87 s. ]
2025-07-30 11:16:43,490 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:43,490 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:16:43,517 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:16:43,517 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:43,517 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 11:16:43,525 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 11:16:43,534 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:16:43,675 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:16:43,746 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:16:44,262 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 11:16:44,273 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:44,273 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:16:44,470 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:16:44,482 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 11:16:44,506 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:44,506 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:16:44,510 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:16:44,556 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:16:44,601 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:16:44,618 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:16:44,632 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:16:44,668 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:16:44,707 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:16:44,735 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:16:44,760 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:16:44,760 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.28 s. ]
2025-07-30 11:16:44,760 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:44,760 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:16:44,772 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:16:44,773 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:44,773 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:16:44,869 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:16:44,871 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:16:45,001 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 11:16:45,001 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:45,001 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:16:45,009 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:16:45,009 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:16:45,009 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:17:04,550 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 19.54 s. ]
2025-07-30 11:17:04,551 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:17:04,551 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 26 s. ]
2025-07-30 11:17:04,551 [main] INFO  com.polarion.platform.startup - ****************************************************************
