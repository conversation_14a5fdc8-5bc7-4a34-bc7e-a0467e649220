2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:22,392 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 14:01:27,180 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 14:01:27,331 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.151 s. ]
2025-07-30 14:01:27,331 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 14:01:27,386 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0554 s. ]
2025-07-30 14:01:27,440 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 14:01:27,556 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 14:01:27,950 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.56 s. ]
2025-07-30 14:01:28,196 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:28,196 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 14:01:28,235 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.29 s. ]
2025-07-30 14:01:28,236 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:28,236 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 14:01:28,242 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 14:01:28,253 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 14:01:28,417 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 14:01:28,545 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 14:01:29,087 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.85 s. ]
2025-07-30 14:01:29,101 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:29,101 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 14:01:29,376 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 14:01:29,394 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.31 s. ]
2025-07-30 14:01:29,441 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:29,441 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 14:01:29,449 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 14:01:29,525 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 14:01:29,588 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 14:01:29,660 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 14:01:29,716 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 14:01:29,767 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 14:01:29,818 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 14:01:29,918 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 14:01:30,021 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 14:01:30,022 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.63 s. ]
2025-07-30 14:01:30,022 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:30,022 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 14:01:30,058 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.04 s. ]
2025-07-30 14:01:30,059 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:30,059 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 14:01:30,246 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 14:01:30,252 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 14:01:30,450 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.39 s. ]
2025-07-30 14:01:30,450 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:30,450 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 14:01:30,457 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 14:01:30,457 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 14:01:30,457 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 14:01:35,205 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.75 s. ]
2025-07-30 14:01:35,205 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 14:01:35,205 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.8 s. ]
2025-07-30 14:01:35,205 [main] INFO  com.polarion.platform.startup - ****************************************************************
