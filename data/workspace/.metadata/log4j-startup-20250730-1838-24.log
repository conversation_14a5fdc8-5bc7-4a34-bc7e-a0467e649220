2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:24,317 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:38:28,477 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:38:28,626 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.15 s. ]
2025-07-30 18:38:28,627 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:38:28,683 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.056 s. ]
2025-07-30 18:38:28,746 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:38:28,892 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 21 s. ]
2025-07-30 18:38:29,109 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.8 s. ]
2025-07-30 18:38:29,198 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:29,198 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:38:29,223 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 18:38:29,223 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:29,223 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 18:38:29,229 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 18:38:29,238 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:38:29,379 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:38:29,455 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:38:29,952 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-30 18:38:29,964 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:29,964 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:38:30,178 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:38:30,188 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 18:38:30,217 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:30,217 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:38:30,223 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:38:30,276 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:38:30,313 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:38:30,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:38:30,387 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:38:30,407 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:38:30,436 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:38:30,481 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:38:30,525 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:38:30,525 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 18:38:30,526 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:30,526 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:38:30,540 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:38:30,541 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:30,541 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:38:30,652 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:38:30,657 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:38:30,787 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 18:38:30,788 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:30,788 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:38:30,796 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:38:30,796 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:38:30,796 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:38:33,460 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.66 s. ]
2025-07-30 18:38:33,460 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:38:33,460 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.14 s. ]
2025-07-30 18:38:33,460 [main] INFO  com.polarion.platform.startup - ****************************************************************
