2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:05,700 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:42:10,100 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:42:10,260 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-07-30 17:42:10,260 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:42:10,320 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0606 s. ]
2025-07-30 17:42:10,394 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:42:10,500 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:42:10,727 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.03 s. ]
2025-07-30 17:42:10,824 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:10,824 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:42:10,857 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 17:42:10,857 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:10,857 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:42:10,863 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 17:42:10,863 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 17:42:10,863 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 17:42:10,863 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 17:42:10,864 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 17:42:10,864 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 17:42:10,873 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:42:11,058 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:42:11,124 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:42:11,599 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 17:42:11,611 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:11,611 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:42:11,864 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:42:11,877 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-30 17:42:11,907 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:11,907 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:42:11,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:42:11,960 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:42:12,015 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:42:12,058 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:42:12,101 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:42:12,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:42:12,144 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:42:12,212 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:42:12,259 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:42:12,259 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.38 s. ]
2025-07-30 17:42:12,259 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:12,259 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:42:12,273 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:42:12,273 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:12,273 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:42:12,381 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:42:12,384 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:42:12,508 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 17:42:12,508 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:12,508 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:42:12,515 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:42:12,515 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:42:12,515 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:42:29,087 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 16.57 s. ]
2025-07-30 17:42:29,088 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:42:29,088 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 23.4 s. ]
2025-07-30 17:42:29,088 [main] INFO  com.polarion.platform.startup - ****************************************************************
