2025-07-29 19:26:56,077 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.085 s [76% update (144x), 24% query (12x)] (221x), svn: 0.016 s [58% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-29 19:26:56,207 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0397 s [51% getDir2 content (2x), 43% info (3x)] (6x)
2025-07-29 19:26:57,028 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.813 s, CPU [user: 0.252 s, system: 0.339 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:26:57,028 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.814 s, CPU [user: 0.217 s, system: 0.283 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.144 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:26:57,028 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.814 s, CPU [user: 0.0825 s, system: 0.129 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.0927 s [84% log2 (10x)] (13x), ObjectMaps: 0.078 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:26:57,028 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.814 s, CPU [user: 0.0819 s, system: 0.107 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.104 s [84% log2 (10x)] (13x), ObjectMaps: 0.0588 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:26:57,029 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.813 s, CPU [user: 0.155 s, system: 0.212 s], Allocated memory: 26.5 MB, transactions: 0, svn: 0.129 s [42% log2 (5x), 23% info (5x), 18% log (1x)] (18x), ObjectMaps: 0.0871 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 19:26:57,029 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.814 s, CPU [user: 0.0524 s, system: 0.0838 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0645 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0576 s [80% log2 (5x), 10% testConnection (1x)] (7x)
2025-07-29 19:26:57,030 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.561 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.428 s [68% log2 (36x), 11% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-29 19:26:57,281 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.212 s [100% getReadConfiguration (48x)] (48x), svn: 0.0851 s [84% info (18x)] (38x)
2025-07-29 19:26:57,655 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.304 s [75% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.227 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:26:57,908 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.137 s, CPU [user: 0.0311 s, system: 0.00979 s], Allocated memory: 10.4 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 19:26:57,941 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.269 s [100% doFinishStartup (1x)] (1x), commit: 0.0711 s [100% Revision (1x)] (1x), Lucene: 0.0529 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.016 s [100% objectsToInv (1x)] (1x), DB: 0.0146 s [43% update (3x), 23% query (1x), 22% commit (2x)] (8x)
2025-07-29 19:27:00,688 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.347 s [91% info (158x)] (168x)
2025-07-29 19:27:01,768 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.943 s, CPU [user: 0.00936 s, system: 0.00237 s], Allocated memory: 529.8 kB, transactions: 1
2025-07-29 19:27:01,769 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, notification worker: 0.0326 s [63% RevisionActivityCreator (2x), 17% WorkItemActivityCreator (1x), 14% TestRunActivityCreator (1x)] (6x), Incremental Baseline: 0.0316 s [100% WorkItem (21x)] (21x), Lucene: 0.0231 s [64% add (1x), 36% refresh (1x)] (2x), persistence listener: 0.0217 s [68% indexRefreshPersistenceListener (1x), 20% RevisionActivityCreator (2x)] (7x), resolve: 0.0202 s [76% User (1x), 24% Revision (2x)] (3x), PullingJob: 0.00682 s [100% collectChanges (1x)] (1x), ObjectMaps: 0.00681 s [100% getPrimaryObjectLocation (1x)] (1x), svn: 0.00664 s [57% getLatestRevision (1x), 43% testConnection (1x)] (2x), EHCache: 0.00177 s [98% GET (15x)] (36x), DB: 0.0017 s [57% update (1x), 43% commit (1x)] (2x)
2025-07-29 19:27:01,769 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.09 s, CPU [user: 0.186 s, system: 0.0302 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.845 s [98% getDatedRevision (181x)] (183x)
2025-07-29 19:27:02,219 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be496c42_0_66157be496c42_0_: finished. Total: 1.52 s, CPU [user: 0.479 s, system: 0.104 s], Allocated memory: 54.2 MB, svn: 0.88 s [47% getDatedRevision (181x), 32% getDir2 content (25x), 19% getFile content (98x)] (307x), resolve: 0.538 s [100% Category (96x)] (96x), ObjectMaps: 0.171 s [43% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (388x)
2025-07-29 19:27:02,485 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be62e048_0_66157be62e048_0_: finished. Total: 0.156 s, CPU [user: 0.072 s, system: 0.0135 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0671 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0613 s [53% info (19x), 41% getFile content (16x)] (37x), resolve: 0.0469 s [100% User (9x)] (9x), ObjectMaps: 0.0244 s [46% getPrimaryObjectProperty (9x), 39% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 19:27:02,828 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be66784a_0_66157be66784a_0_: finished. Total: 0.27 s, CPU [user: 0.0722 s, system: 0.0155 s], Allocated memory: 19.9 MB, svn: 0.227 s [76% getDir2 content (17x), 24% getFile content (44x)] (62x), RepositoryConfigService: 0.079 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 19:27:04,466 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be6ab44b_0_66157be6ab44b_0_: finished. Total: 1.64 s, CPU [user: 0.547 s, system: 0.097 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.35 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.834 s [81% getFile content (412x)] (434x)
2025-07-29 19:27:04,596 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be84484c_0_66157be84484c_0_: finished. Total: 0.13 s, CPU [user: 0.0245 s, system: 0.00271 s], Allocated memory: 17.8 MB, svn: 0.116 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0272 s [98% getReadConfiguration (124x)] (148x)
2025-07-29 19:27:05,332 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66157be86d04e_0_66157be86d04e_0_: finished. Total: 0.703 s, CPU [user: 0.255 s, system: 0.0239 s], Allocated memory: 387.4 MB, RepositoryConfigService: 0.582 s [71% getReadConfiguration (2787x), 29% getExistingPrefixes (89x)] (3025x), svn: 0.463 s [48% getFile content (185x), 33% info (60x)] (266x)
2025-07-29 19:27:05,333 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.64 s, CPU [user: 1.54 s, system: 0.273 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.68 s [45% getFile content (809x), 31% getDir2 content (114x), 16% getDatedRevision (181x)] (1204x), RepositoryConfigService: 2.16 s [89% getReadConfiguration (12019x)] (12691x), resolve: 0.647 s [83% Category (96x)] (117x)
2025-07-29 19:27:05,333 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 3.53 s [35% getDatedRevision (362x), 34% getFile content (809x), 24% getDir2 content (114x)] (1388x), RepositoryConfigService: 2.16 s [89% getReadConfiguration (12019x)] (12691x), resolve: 0.648 s [83% Category (96x)] (118x), ObjectMaps: 0.22 s [45% getPrimaryObjectProperty (110x), 34% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-29 19:27:05,525 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55efa202-0a465820-6330987e-f2d1236c] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.819 s, CPU [user: 0.51 s, system: 0.0634 s], Allocated memory: 71.8 MB, transactions: 2, RPC: 0.42 s [100% decodeRequest (1x)] (3x), PolarionAuthenticator: 0.366 s [100% authenticate (1x)] (1x)
2025-07-29 19:27:06,256 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55efa768-0a465820-6330987e-9f92287a | u:admin] INFO  TXLOGGER - Tx 66157be9dcc54_0_66157be9dcc54_0_: finished. Total: 0.157 s, CPU [user: 0.0761 s, system: 0.012 s], Allocated memory: 8.6 MB, resolve: 0.0814 s [100% BaselineCollection (8x)] (15x), svn: 0.0617 s [60% log (7x), 24% getFile content (7x)] (17x), ObjectMaps: 0.0458 s [92% getPrimaryObjectProperty (7x)] (36x), Lucene: 0.0118 s [100% search (1x)] (1x)
2025-07-29 19:27:06,260 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55efa768-0a465820-6330987e-9f92287a] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.171 s, CPU [user: 0.0871 s, system: 0.0146 s], Allocated memory: 10.9 MB, transactions: 1, ModelObjectListRequest: 0.157 s [100% initialize (1x)] (1x), resolve: 0.0814 s [100% BaselineCollection (8x)] (15x), svn: 0.0617 s [60% log (7x), 24% getFile content (7x)] (17x), ObjectMaps: 0.0458 s [92% getPrimaryObjectProperty (7x)] (36x), RPC: 0.0124 s [76% decodeRequest (1x), 18% encodeResponse (1x)] (4x), Lucene: 0.0118 s [100% search (1x)] (1x)
2025-07-29 19:27:06,427 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55efa823-0a465820-6330987e-09f2a19b] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.151 s, CPU [user: 0.0985 s, system: 0.0152 s], Allocated memory: 12.4 MB, transactions: 1, FormRequest: 0.0887 s [100% initialize (1x)] (1x), RPC: 0.0612 s [54% encodeResponse (1x), 46% decodeRequest (1x)] (4x), svn: 0.0143 s [39% testConnection (1x), 31% getFile content (2x), 30% getLatestRevision (1x)] (5x)
2025-07-29 19:27:19,628 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55efdbcf-0a465820-6330987e-edc9f314 | u:admin] INFO  TXLOGGER - Tx 66157bf6f545c_0_66157bf6f545c_0_: finished. Total: 0.117 s, CPU [user: 0.0207 s, system: 0.00988 s], Allocated memory: 761.9 kB
2025-07-29 19:27:19,671 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55efdbcf-0a465820-6330987e-edc9f314] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/BubbleQueryService': Total: 0.168 s, CPU [user: 0.0435 s, system: 0.0207 s], Allocated memory: 3.4 MB, transactions: 1, BubbleQueryService: 0.121 s [100% getSelectorContent (1x)] (1x), RPC: 0.0441 s [95% encodeResponse (1x)] (4x)
2025-07-29 19:27:51,968 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55f05a01-0a465820-6330987e-36ea41b2] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.156 s, CPU [user: 0.0112 s, system: 0.00544 s], Allocated memory: 951.6 kB, transactions: 1, RPC: 0.0921 s [90% encodeResponse (1x)] (4x), GC: 0.082 s [100% G1 Young Generation (1x)] (1x), FormRequest: 0.0582 s [100% initialize (1x)] (1x), svn: 0.0262 s [100% getLatestRevision (1x)] (2x), ObjectMaps: 0.0182 s [100% getPrimaryObjectLocations (3x)] (3x)
2025-07-29 19:30:25,733 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55f2b2b8-0a465820-6330987e-7945f6f5 | u:admin] INFO  TXLOGGER - Tx 66157cacb20b1_0_66157cacb20b1_0_: finished. Total: 0.123 s, CPU [user: 0.0129 s, system: 0.0271 s], Allocated memory: 685.5 kB, svn: 0.0752 s [100% getLatestRevision (1x)] (2x)
2025-07-29 19:30:25,737 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55f2b2b8-0a465820-6330987e-7945f6f5] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.143 s, CPU [user: 0.0166 s, system: 0.0351 s], Allocated memory: 952.2 kB, transactions: 1, FormRequest: 0.127 s [100% initialize (1x)] (1x), svn: 0.0752 s [100% getLatestRevision (1x)] (2x)
2025-07-29 19:30:29,296 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55f2c0a7-0a465820-6330987e-20e6614e | u:admin] INFO  TXLOGGER - Tx 66157cb02b4bc_0_66157cb02b4bc_0_: finished. Total: 0.13 s, CPU [user: 0.029 s, system: 0.0158 s], Allocated memory: 1.3 MB, RepositoryConfigService: 0.109 s [68% getReadUserConfiguration (1x), 16% getExistingPrefixes (1x)] (9x), svn: 0.0385 s [68% info (2x), 32% getFile content (1x)] (4x)
2025-07-29 19:30:29,296 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55f2c0a7-0a465820-6330987e-360da884] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/TestingDataService': Total: 0.137 s, CPU [user: 0.0429 s, system: 0.0196 s], Allocated memory: 2.8 MB, transactions: 0, RPC: 0.0729 s [98% decodeRequest (1x)] (4x)
2025-07-29 19:30:29,302 [ajp-nio-127.0.0.1-8889-exec-1 | cID:55f2c0a7-0a465820-6330987e-20e6614e] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.142 s, CPU [user: 0.0353 s, system: 0.0187 s], Allocated memory: 1.8 MB, transactions: 1, WorkItemDataService: 0.133 s [100% getAvailableColumnsAndConfigurations (1x)] (1x), RepositoryConfigService: 0.109 s [68% getReadUserConfiguration (1x), 16% getExistingPrefixes (1x)] (9x), svn: 0.0385 s [68% info (2x), 32% getFile content (1x)] (4x)
2025-07-29 19:30:31,153 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55f2c80b-0a465820-6330987e-143c0b6d] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/BubbleQueryService': Total: 0.101 s, CPU [user: 0.0382 s, system: 0.0193 s], Allocated memory: 3.4 MB, transactions: 1, BubbleQueryService: 0.0705 s [100% parse (1x)] (1x), RPC: 0.0265 s [44% encodeResponse (1x), 42% decodeRequest (1x)] (4x)
2025-07-29 19:30:31,154 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55f2c80b-0a465820-6330987e-33155f82] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.103 s, CPU [user: 0.0214 s, system: 0.028 s], Allocated memory: 3.0 MB, transactions: 1, WorkItemDataService: 0.0854 s [100% getCreateWorkItemData (1x)] (1x), RPC: 0.0128 s [60% encodeResponse (1x), 23% readRequest (1x)] (4x)
2025-07-29 19:30:31,782 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55f2c96d-0a465820-6330987e-637e2c02 | u:admin] INFO  TXLOGGER - Tx 66157cb25ccc9_0_66157cb25ccc9_0_: finished. Total: 0.371 s, CPU [user: 0.111 s, system: 0.0219 s], Allocated memory: 20.4 MB, resolve: 0.298 s [97% WorkItem (12x)] (15x), svn: 0.233 s [44% info (26x), 22% log (12x), 18% getDir2 content (12x)] (67x), ObjectMaps: 0.0743 s [87% getPrimaryObjectProperty (13x)] (66x), RepositoryConfigService: 0.0263 s [75% getReadConfiguration (425x), 25% getExistingPrefixes (24x)] (449x)
2025-07-29 19:30:31,797 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55f2c96d-0a465820-6330987e-637e2c02] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/WorkItemDataService': Total: 0.392 s, CPU [user: 0.124 s, system: 0.0261 s], Allocated memory: 23.7 MB, transactions: 1, WorkItemDataService: 0.373 s [100% loadInstances (1x)] (1x), resolve: 0.298 s [97% WorkItem (12x)] (15x), svn: 0.233 s [44% info (26x), 22% log (12x), 18% getDir2 content (12x)] (67x), ObjectMaps: 0.0743 s [87% getPrimaryObjectProperty (13x)] (66x), RepositoryConfigService: 0.0263 s [75% getReadConfiguration (425x), 25% getExistingPrefixes (24x)] (449x)
2025-07-29 19:30:31,829 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55f2c96d-0a465820-6330987e-d357d512 | u:admin] INFO  TXLOGGER - Tx 66157cb25d4ca_0_66157cb25d4ca_0_: finished. Total: 0.416 s, CPU [user: 0.157 s, system: 0.0362 s], Allocated memory: 30.5 MB, RepositoryConfigService: 0.144 s [87% getReadConfiguration (270x)] (301x), svn: 0.102 s [33% info (10x), 26% getFile content (12x), 17% getDir2 content (4x), 15% log (2x)] (30x), resolve: 0.101 s [100% WorkItem (4x)] (9x), DocumentsDataProvider: 0.0411 s [100% getStructureLinkRoles (3x)] (3x), Lucene: 0.0374 s [100% search (6x)] (6x), ObjectMaps: 0.0281 s [82% getPrimaryObjectProperty (2x)] (13x)
2025-07-29 19:30:31,852 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55f2c96d-0a465820-6330987e-d357d512] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/UniversalService': Total: 0.447 s, CPU [user: 0.178 s, system: 0.0431 s], Allocated memory: 35.0 MB, transactions: 1, LoadWorkItemRequest: 0.416 s [100% initialize (1x)] (1x), RepositoryConfigService: 0.144 s [87% getReadConfiguration (270x)] (301x), svn: 0.102 s [33% info (10x), 26% getFile content (12x), 17% getDir2 content (4x), 15% log (2x)] (30x), resolve: 0.101 s [100% WorkItem (4x)] (9x), DocumentsDataProvider: 0.0411 s [100% getStructureLinkRoles (3x)] (3x), Lucene: 0.0374 s [100% search (6x)] (6x), RPC: 0.0286 s [74% encodeResponse (1x), 20% decodeRequest (1x)] (4x), ObjectMaps: 0.0281 s [82% getPrimaryObjectProperty (2x)] (13x)
