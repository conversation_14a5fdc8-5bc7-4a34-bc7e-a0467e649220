2025-07-29 20:03:37,589 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0483 s [61% update (144x), 39% query (12x)] (221x), svn: 0.013 s [60% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-29 20:03:37,696 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.034 s [60% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.183 s, system: 0.279 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.683 s, CPU [user: 0.219 s, system: 0.343 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0489 s, system: 0.098 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0614 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0382 s [76% log2 (5x), 15% testConnection (1x)] (7x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.108 s, system: 0.217 s], Allocated memory: 24.4 MB, transactions: 0, ObjectMaps: 0.0643 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.684 s, CPU [user: 0.0732 s, system: 0.115 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.08 s [76% log2 (10x), 19% getLatestRevision (2x)] (13x), ObjectMaps: 0.0721 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 20:03:38,383 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.683 s, CPU [user: 0.0909 s, system: 0.153 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.107 s [41% log2 (10x), 19% info (5x), 15% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0743 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 20:03:38,383 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.507 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.29 s [60% log2 (36x), 14% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-29 20:03:38,642 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.228 s [100% getReadConfiguration (48x)] (48x), svn: 0.0942 s [88% info (18x)] (38x)
2025-07-29 20:03:38,937 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.219 s [78% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.172 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 20:03:39,152 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.2 s [100% doFinishStartup (1x)] (1x), commit: 0.0487 s [100% Revision (1x)] (1x), Lucene: 0.0292 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0133 s [100% objectsToInv (1x)] (1x), DB: 0.0107 s [46% update (3x), 22% execute (1x), 21% query (1x)] (8x)
2025-07-29 20:06:09,402 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.102 s, CPU [user: 0.00293 s, system: 0.00189 s], Allocated memory: 131.0 kB, transactions: 0, PullingJob: 0.101 s [100% collectChanges (1x)] (1x), svn: 0.101 s [100% getLatestRevision (1x)] (1x), GC: 0.089 s [100% G1 Young Generation (1x)] (1x)
