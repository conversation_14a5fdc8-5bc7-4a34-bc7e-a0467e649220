2025-07-30 19:12:48,476 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.06 s [68% update (144x), 32% query (12x)] (221x), svn: 0.016 s [58% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-30 19:12:48,592 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0334 s [54% getDir2 content (2x), 37% info (3x)] (6x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.204 s, system: 0.286 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.263 s, system: 0.348 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.12 s, system: 0.223 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0675 s, system: 0.0904 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.0901 s [80% log2 (10x), 12% getLatestRevision (2x)] (13x), ObjectMaps: 0.0543 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.101 s, system: 0.168 s], Allocated memory: 14.5 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0617 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 19:12:49,365 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0695 s, system: 0.0875 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0874 s [39% log2 (5x), 23% log (1x), 21% info (5x)] (18x), ObjectMaps: 0.0687 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:12:49,366 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.598 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.314 s [65% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 19:12:49,632 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.231 s [100% getReadConfiguration (48x)] (48x), svn: 0.0814 s [86% info (18x)] (38x)
2025-07-30 19:12:49,910 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.211 s [74% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.162 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 19:12:50,162 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.234 s [100% doFinishStartup (1x)] (1x), commit: 0.0606 s [100% Revision (1x)] (1x), Lucene: 0.0404 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0142 s [100% objectsToInv (1x)] (1x)
2025-07-30 19:12:59,722 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.432 s [85% info (158x)] (172x), PullingJob: 0.0278 s [100% collectChanges (3x)] (3x)
2025-07-30 19:12:59,983 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616c24654444_0_6616c24654444_0_: finished. Total: 0.253 s, CPU [user: 0.125 s, system: 0.015 s], Allocated memory: 20.2 MB, resolve: 0.0923 s [56% User (2x), 42% Project (1x)] (5x), ObjectMaps: 0.0336 s [53% getPrimaryObjectLocation (2x), 33% getPrimaryObjectProperty (2x)] (11x), svn: 0.0254 s [34% getLatestRevision (2x), 21% log (1x), 16% info (1x), 16% testConnection (1x)] (8x), Lucene: 0.0151 s [100% search (1x)] (1x)
2025-07-30 19:13:00,498 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.697 s, CPU [user: 0.00597 s, system: 0.00157 s], Allocated memory: 324.8 kB, transactions: 1
2025-07-30 19:13:00,498 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.267 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.111 s [62% User (3x), 35% Project (1x)] (7x), ObjectMaps: 0.0372 s [58% getPrimaryObjectLocation (3x), 30% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0278 s [100% WorkItem (22x)] (22x), svn: 0.0254 s [34% getLatestRevision (2x), 21% log (1x), 16% info (1x), 16% testConnection (1x)] (8x), persistence listener: 0.0202 s [82% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.019 s [80% search (1x), 20% refresh (1x)] (2x)
2025-07-30 19:13:00,499 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.776 s, CPU [user: 0.151 s, system: 0.0225 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.623 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0427 s [80% buildBaselineSnapshots (1x), 20% buildBaseline (23x)] (24x)
2025-07-30 19:13:00,905 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c24654845_0_6616c24654845_0_: finished. Total: 1.17 s, CPU [user: 0.298 s, system: 0.0781 s], Allocated memory: 47.0 MB, svn: 0.734 s [52% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.342 s [100% Category (96x)] (96x), ObjectMaps: 0.121 s [43% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 19:13:01,116 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c24790c48_0_6616c24790c48_0_: finished. Total: 0.12 s, CPU [user: 0.0568 s, system: 0.00979 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0606 s [53% getReadConfiguration (162x), 47% getReadUserConfiguration (10x)] (172x), svn: 0.0518 s [56% info (19x), 40% getFile content (15x)] (36x), resolve: 0.0265 s [100% User (9x)] (9x), ObjectMaps: 0.0114 s [50% getPrimaryObjectProperty (8x), 28% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 19:13:01,236 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c247af049_0_6616c247af049_0_: finished. Total: 0.119 s, CPU [user: 0.0372 s, system: 0.00556 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0695 s [97% getReadConfiguration (54x)] (77x), svn: 0.0354 s [100% getFile content (12x)] (13x)
2025-07-30 19:13:01,738 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c247cd04a_0_6616c247cd04a_0_: finished. Total: 0.502 s, CPU [user: 0.0788 s, system: 0.0161 s], Allocated memory: 19.9 MB, svn: 0.401 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0994 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 19:13:03,120 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c2484a84b_0_6616c2484a84b_0_: finished. Total: 1.38 s, CPU [user: 0.505 s, system: 0.0573 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.09 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.763 s [73% getFile content (412x), 27% getDir2 content (21x)] (434x)
2025-07-30 19:13:03,254 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c249a404c_0_6616c249a404c_0_: finished. Total: 0.134 s, CPU [user: 0.0277 s, system: 0.00332 s], Allocated memory: 17.8 MB, svn: 0.119 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.031 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 19:13:03,597 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616c249cfc4e_0_6616c249cfc4e_0_: finished. Total: 0.301 s, CPU [user: 0.116 s, system: 0.00722 s], Allocated memory: 384.3 MB, svn: 0.202 s [53% getDir2 content (21x), 47% getFile content (185x)] (207x), RepositoryConfigService: 0.176 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 19:13:03,597 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.87 s, CPU [user: 1.17 s, system: 0.187 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.38 s [43% getDir2 content (115x), 38% getFile content (807x)] (1141x), RepositoryConfigService: 1.55 s [96% getReadConfiguration (12019x)] (12691x), resolve: 0.409 s [84% Category (96x)] (117x)
2025-07-30 19:13:03,597 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.01 s [34% getDir2 content (115x), 33% getDatedRevision (362x), 30% getFile content (807x)] (1325x), RepositoryConfigService: 1.55 s [96% getReadConfiguration (12019x)] (12691x), resolve: 0.41 s [84% Category (96x)] (118x)
2025-07-30 19:13:23,354 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.115 s, CPU [user: 0.00295 s, system: 0.02 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0664 s [100% collectChanges (1x)] (1x), svn: 0.0619 s [100% getLatestRevision (1x)] (1x)
