2025-07-30 17:35:26,221 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0543 s [71% update (144x), 29% query (12x)] (221x), svn: 0.0135 s [47% getLatestRevision (2x), 27% checkPath (1x), 26% testConnection (1x)] (4x)
2025-07-30 17:35:26,326 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0277 s [53% getDir2 content (2x), 37% info (3x)] (6x)
2025-07-30 17:35:27,025 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.0541 s, system: 0.1 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0756 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:35:27,025 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.249 s, system: 0.313 s], Allocated memory: 70.9 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:35:27,025 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.117 s, system: 0.191 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.064 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:35:27,025 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.205 s, system: 0.255 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.111 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:35:27,025 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.0847 s, system: 0.131 s], Allocated memory: 12.2 MB, transactions: 0, ObjectMaps: 0.0707 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0646 s [79% log2 (10x), 12% getLatestRevision (2x)] (13x)
2025-07-30 17:35:27,026 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.696 s, CPU [user: 0.1 s, system: 0.112 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.147 s [54% log2 (10x), 16% info (5x), 14% log (1x)] (24x)
2025-07-30 17:35:27,026 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.475 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.314 s [63% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 17:35:27,279 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.222 s [100% getReadConfiguration (48x)] (48x), svn: 0.078 s [85% info (18x)] (38x)
2025-07-30 17:35:27,598 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.253 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.188 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:35:27,833 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.122 s, CPU [user: 0.0342 s, system: 0.00983 s], Allocated memory: 11.0 MB, GC: 0.014 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:35:27,885 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.273 s [100% doFinishStartup (1x)] (1x), commit: 0.0744 s [100% Revision (1x)] (1x), Lucene: 0.041 s [100% refresh (1x)] (1x), DB: 0.018 s [33% update (3x), 28% query (1x), 21% commit (2x)] (8x)
2025-07-30 17:35:30,243 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.312 s [89% info (158x)] (168x)
2025-07-30 17:35:30,550 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616abf5fd043_0_6616abf5fd043_0_: finished. Total: 0.258 s, CPU [user: 0.126 s, system: 0.015 s], Allocated memory: 20.3 MB, resolve: 0.0673 s [61% User (2x), 31% Project (1x)] (5x), Lucene: 0.0216 s [100% search (1x)] (1x), svn: 0.0213 s [39% getLatestRevision (2x), 20% getFile content (2x), 17% log (1x), 16% testConnection (1x)] (8x), ObjectMaps: 0.0197 s [58% getPrimaryObjectLocation (2x), 35% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 17:35:31,097 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.709 s, CPU [user: 0.00723 s, system: 0.00134 s], Allocated memory: 316.1 kB, transactions: 1
2025-07-30 17:35:31,098 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.268 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.126 s [76% User (4x), 16% Project (1x)] (8x), persistence listener: 0.0536 s [93% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.0433 s [50% search (1x), 30% add (1x), 20% refresh (1x)] (3x), ObjectMaps: 0.0378 s [78% getPrimaryObjectLocation (4x), 18% getPrimaryObjectProperty (2x)] (13x), svn: 0.0276 s [41% getLatestRevision (3x), 25% testConnection (2x), 16% getFile content (2x)] (10x), Incremental Baseline: 0.0245 s [100% WorkItem (22x)] (22x)
2025-07-30 17:35:31,098 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.714 s, CPU [user: 0.154 s, system: 0.024 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.619 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0503 s [85% buildBaselineSnapshots (1x)] (24x)
2025-07-30 17:35:31,461 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abf5fb040_0_6616abf5fb040_0_: finished. Total: 1.18 s, CPU [user: 0.3 s, system: 0.0778 s], Allocated memory: 47.1 MB, svn: 0.719 s [47% getDatedRevision (181x), 37% getDir2 content (25x)] (307x), resolve: 0.33 s [100% Category (96x)] (96x), ObjectMaps: 0.113 s [44% getPrimaryObjectProperty (96x), 28% getPrimaryObjectLocation (96x), 28% getLastPromoted (96x)] (387x)
2025-07-30 17:35:31,793 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abf74e84a_0_6616abf74e84a_0_: finished. Total: 0.15 s, CPU [user: 0.054 s, system: 0.00447 s], Allocated memory: 19.9 MB, svn: 0.109 s [63% getDir2 content (17x), 37% getFile content (44x)] (62x), RepositoryConfigService: 0.0633 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 17:35:32,624 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abf77444b_0_6616abf77444b_0_: finished. Total: 0.83 s, CPU [user: 0.392 s, system: 0.0197 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.631 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.426 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 17:35:33,078 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616abf861c4e_0_6616abf861c4e_0_: finished. Total: 0.334 s, CPU [user: 0.14 s, system: 0.0103 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.232 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.197 s [61% getFile content (185x), 39% getDir2 content (21x)] (207x)
2025-07-30 17:35:33,078 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.8 s, CPU [user: 1 s, system: 0.129 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.64 s [40% getDir2 content (115x), 37% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.02 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.382 s [86% Category (96x)] (117x)
2025-07-30 17:35:33,078 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.25 s [42% getDatedRevision (362x), 29% getDir2 content (115x), 27% getFile content (807x)] (1324x), RepositoryConfigService: 1.02 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.383 s [86% Category (96x)] (118x), ObjectMaps: 0.134 s [46% getPrimaryObjectProperty (108x), 28% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
