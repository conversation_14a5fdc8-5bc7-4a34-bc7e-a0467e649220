2025-07-30 17:09:34,144 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:09:34,144 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:09:34,144 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:09:34,144 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:09:34,144 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:09:34,145 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:34,145 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:09:38,572 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:09:38,719 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-07-30 17:09:38,719 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:09:38,780 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0602 s. ]
2025-07-30 17:09:38,832 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:09:38,967 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:09:39,181 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.04 s. ]
2025-07-30 17:09:39,274 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:39,274 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:09:39,312 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 17:09:39,313 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:39,313 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 17:09:39,327 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 17:09:39,342 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:09:39,609 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:09:39,697 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:09:40,239 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.93 s. ]
2025-07-30 17:09:40,252 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:40,252 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:09:40,544 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:09:40,564 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 17:09:40,601 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:40,601 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:09:40,612 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:09:40,672 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:09:40,722 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:09:40,766 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:09:40,809 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:09:40,838 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:09:40,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:09:40,927 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:09:40,985 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:09:40,985 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-30 17:09:40,986 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:40,986 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:09:41,005 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:09:41,005 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:41,005 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:09:41,154 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:09:41,161 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:09:41,333 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.33 s. ]
2025-07-30 17:09:41,339 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:41,339 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:09:41,377 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.04 s. ]
2025-07-30 17:09:41,377 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:09:41,377 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:09:44,506 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.13 s. ]
2025-07-30 17:09:44,507 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:09:44,507 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.4 s. ]
2025-07-30 17:09:44,507 [main] INFO  com.polarion.platform.startup - ****************************************************************
