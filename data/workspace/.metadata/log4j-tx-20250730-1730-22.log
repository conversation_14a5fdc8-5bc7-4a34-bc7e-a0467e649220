2025-07-30 17:30:28,735 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0634 s [64% update (144x), 36% query (12x)] (221x), svn: 0.0167 s [49% testConnection (1x), 39% getLatestRevision (2x)] (4x)
2025-07-30 17:30:28,909 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0415 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.238 s, system: 0.294 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.0808 s, system: 0.109 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.101 s [83% log2 (10x)] (13x), ObjectMaps: 0.0518 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.0577 s, system: 0.0918 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.054 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.053 s [75% log2 (5x), 13% getLatestRevision (1x)] (7x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.123 s, system: 0.138 s], Allocated memory: 14.6 MB, transactions: 0, svn: 0.185 s [29% log2 (10x), 20% log (1x), 18% info (5x), 16% testConnection (1x)] (24x), ObjectMaps: 0.0759 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.126 s, system: 0.22 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0854 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:30:29,948 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.275 s, system: 0.361 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:30:29,949 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.553 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.427 s [55% log2 (36x), 13% getLatestRevision (9x), 13% testConnection (6x)] (61x)
2025-07-30 17:30:30,120 [main | u:p] INFO  TXLOGGER - Tx 6616aad0b6401_0_6616aad0b6401_0_: finished. Total: 0.141 s, CPU [user: 0.0934 s, system: 0.00699 s], Allocated memory: 21.8 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:30:30,286 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.289 s [100% getReadConfiguration (48x)] (48x), svn: 0.0971 s [83% info (18x)] (38x)
2025-07-30 17:30:30,822 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.4 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.315 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:30:31,145 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.103 s, CPU [user: 0.0433 s, system: 0.00969 s], Allocated memory: 3.2 MB
2025-07-30 17:30:31,156 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.112 s, CPU [user: 0.0049 s, system: 0.00211 s], Allocated memory: 811.2 kB
2025-07-30 17:30:31,157 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.115 s, CPU [user: 0.0193 s, system: 0.00475 s], Allocated memory: 2.1 MB
2025-07-30 17:30:31,209 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.164 s, CPU [user: 0.0294 s, system: 0.0114 s], Allocated memory: 10.5 MB
2025-07-30 17:30:31,276 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.431 s [100% doFinishStartup (1x)] (1x), commit: 0.0944 s [100% Revision (1x)] (1x), Lucene: 0.0615 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0402 s [100% objectsToInv (1x)] (1x)
2025-07-30 17:30:34,660 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.513 s [91% info (158x)] (170x)
2025-07-30 17:30:35,097 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616aad552445_0_6616aad552445_0_: finished. Total: 0.399 s, CPU [user: 0.165 s, system: 0.0237 s], Allocated memory: 21.6 MB, resolve: 0.121 s [67% User (2x), 30% Project (1x)] (5x), Lucene: 0.0668 s [100% search (1x)] (1x), svn: 0.0371 s [51% getLatestRevision (2x), 17% log (1x), 15% testConnection (1x)] (8x), ObjectMaps: 0.0338 s [49% getPrimaryObjectLocation (2x), 41% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 17:30:35,865 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.1 s, CPU [user: 0.00677 s, system: 0.0017 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 17:30:35,867 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.418 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.138 s [69% User (3x), 26% Project (1x)] (7x), Lucene: 0.111 s [60% search (1x), 35% add (2x)] (4x), persistence listener: 0.0448 s [46% BuildActivityCreator (1x), 32% indexRefreshPersistenceListener (1x), 13% TestRunActivityCreator (1x)] (7x), ObjectMaps: 0.0387 s [55% getPrimaryObjectLocation (3x), 36% getPrimaryObjectProperty (2x)] (12x), svn: 0.0371 s [51% getLatestRevision (2x), 17% log (1x), 15% testConnection (1x)] (8x), Incremental Baseline: 0.0219 s [100% WorkItem (22x)] (22x)
2025-07-30 17:30:35,868 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.21 s, CPU [user: 0.198 s, system: 0.0293 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.952 s [97% getDatedRevision (181x)] (183x), Lucene: 0.0862 s [87% buildBaselineSnapshots (1x)] (24x)
2025-07-30 17:30:36,350 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad551c40_0_6616aad551c40_0_: finished. Total: 1.65 s, CPU [user: 0.414 s, system: 0.103 s], Allocated memory: 46.6 MB, svn: 0.98 s [46% getDatedRevision (181x), 33% getDir2 content (25x), 20% getFile content (98x)] (307x), resolve: 0.596 s [100% Category (96x)] (96x), ObjectMaps: 0.2 s [42% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 17:30:36,573 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad703848_0_6616aad703848_0_: finished. Total: 0.142 s, CPU [user: 0.066 s, system: 0.0104 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0723 s [61% getReadUserConfiguration (10x), 39% getReadConfiguration (162x)] (172x), svn: 0.0656 s [56% info (19x), 37% getFile content (15x)] (36x), resolve: 0.037 s [100% User (9x)] (9x), ObjectMaps: 0.0157 s [62% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x)] (32x)
2025-07-30 17:30:36,870 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad73404a_0_6616aad73404a_0_: finished. Total: 0.245 s, CPU [user: 0.0859 s, system: 0.00799 s], Allocated memory: 19.9 MB, svn: 0.178 s [63% getDir2 content (17x), 37% getFile content (44x)] (62x), RepositoryConfigService: 0.109 s [97% getReadConfiguration (170x)] (192x)
2025-07-30 17:30:37,853 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad77184b_0_6616aad77184b_0_: finished. Total: 0.983 s, CPU [user: 0.43 s, system: 0.0437 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.772 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.505 s [71% getFile content (412x), 29% getDir2 content (21x)] (434x)
2025-07-30 17:30:37,981 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad86784c_0_6616aad86784c_0_: finished. Total: 0.127 s, CPU [user: 0.0292 s, system: 0.00346 s], Allocated memory: 17.8 MB, svn: 0.11 s [77% getDir2 content (18x), 23% getFile content (29x)] (48x), RepositoryConfigService: 0.0378 s [99% getReadConfiguration (124x)] (148x)
2025-07-30 17:30:38,361 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616aad89644e_0_6616aad89644e_0_: finished. Total: 0.319 s, CPU [user: 0.124 s, system: 0.00931 s], Allocated memory: 384.9 MB, svn: 0.208 s [51% getFile content (185x), 49% getDir2 content (21x)] (207x), RepositoryConfigService: 0.193 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 17:30:38,361 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.7 s, CPU [user: 1.23 s, system: 0.19 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.15 s [38% getDir2 content (115x), 37% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.23 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.671 s [89% Category (96x)] (117x), ObjectMaps: 0.23 s [45% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 17:30:38,361 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.11 s [44% getDatedRevision (362x), 26% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.23 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.672 s [89% Category (96x)] (118x), ObjectMaps: 0.23 s [45% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 17:30:43,882 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5aab76e6-7f000001-03702c19-bd40aadb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.579 s, CPU [user: 0.257 s, system: 0.0965 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.519 s [100% authenticate (1x)] (1x)
2025-07-30 17:30:44,097 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5aab7967-7f000001-03702c19-cdae001d] INFO  TXLOGGER - Summary for 'servlet /polarion/wiki/skins/sidecar/screenlayout.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.148 s, CPU [user: 0.00299 s, system: 0.00111 s], Allocated memory: 120.8 kB, transactions: 0, PolarionAuthenticator: 0.118 s [100% authenticate (1x)] (1x), interceptor: 0.0703 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 17:30:44,209 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5aab79bb-7f000001-03702c19-7d255bbe] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/lib/ember/radiobutton.js': Total: 0.153 s, CPU [user: 0.00235 s, system: 0.00113 s], Allocated memory: 78.2 kB, transactions: 0, PolarionAuthenticator: 0.151 s [100% authenticate (1x)] (1x), interceptor: 0.131 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 17:30:44,236 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5aab7968-7f000001-03702c19-9a4bb958] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.291 s, CPU [user: 0.0258 s, system: 0.00776 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0786 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.062 s [100% readUserData (1x)] (1x), PolarionAuthenticator: 0.0589 s [100% authenticate (1x)] (1x)
2025-07-30 17:30:44,547 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5aab7b08-7f000001-03702c19-dd579cfa] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.186 s, CPU [user: 0.0884 s, system: 0.0218 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 17:30:44,547 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5aab7b49-7f000001-03702c19-b93466fb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.122 s, CPU [user: 0.0216 s, system: 0.00749 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 17:30:44,573 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5aab7b49-7f000001-03702c19-a601503b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753867844313': Total: 0.148 s, CPU [user: 0.0461 s, system: 0.00805 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 17:30:44,628 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5aab7b55-7f000001-03702c19-66f5c6f9 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753867844315] INFO  TXLOGGER - Tx 6616aadeebc51_0_6616aadeebc51_0_: finished. Total: 0.101 s, CPU [user: 0.0533 s, system: 0.0137 s], Allocated memory: 5.6 MB, svn: 0.0146 s [65% testConnection (1x), 35% getFile content (2x)] (4x)
2025-07-30 17:30:44,638 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5aab7b55-7f000001-03702c19-66f5c6f9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753867844315': Total: 0.201 s, CPU [user: 0.0724 s, system: 0.0189 s], Allocated memory: 7.7 MB, transactions: 1, RepositoryConfigService: 0.102 s [100% getReadConfiguration (1x)] (1x), svn: 0.0146 s [65% testConnection (1x), 35% getFile content (2x)] (4x)
2025-07-30 17:30:44,677 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5aab7b4e-7f000001-03702c19-19d59b6b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753867844314': Total: 0.246 s, CPU [user: 0.0533 s, system: 0.009 s], Allocated memory: 5.2 MB, transactions: 1, RepositoryConfigService: 0.125 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 17:31:10,990 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5aabe0a4-7f000001-03702c19-d2f5cb5c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 0.617 s, CPU [user: 0.145 s, system: 0.0632 s], Allocated memory: 6.9 MB, transactions: 0
