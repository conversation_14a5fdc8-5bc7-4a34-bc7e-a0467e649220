2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:01,962 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:10:06,070 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:10:06,189 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.118 s. ]
2025-07-30 15:10:06,189 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:10:06,226 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0371 s. ]
2025-07-30 15:10:06,273 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:10:06,370 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:10:06,588 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.63 s. ]
2025-07-30 15:10:06,669 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:06,669 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:10:06,690 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-30 15:10:06,690 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:06,690 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 15:10:06,696 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 15:10:06,705 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:10:06,901 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:10:07,121 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:10:07,699 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.01 s. ]
2025-07-30 15:10:07,711 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:07,711 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:10:07,984 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:10:08,001 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.3 s. ]
2025-07-30 15:10:08,028 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:08,028 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:10:08,033 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:10:08,089 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:10:08,140 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:10:08,199 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:10:08,256 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:10:08,292 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:10:08,323 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:10:08,396 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:10:08,448 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:10:08,448 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.45 s. ]
2025-07-30 15:10:08,448 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:08,448 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:10:08,462 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 15:10:08,462 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:08,462 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:10:08,643 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:10:08,646 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:10:08,845 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.38 s. ]
2025-07-30 15:10:08,846 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:08,846 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:10:08,854 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:10:08,854 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:10:08,854 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:10:11,365 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.51 s. ]
2025-07-30 15:10:11,365 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:10:11,365 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.4 s. ]
2025-07-30 15:10:11,365 [main] INFO  com.polarion.platform.startup - ****************************************************************
