2025-07-29 19:41:35,981 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:41:35,981 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:41:35,981 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:41:35,981 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:41:35,982 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:41:35,982 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:35,982 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:41:40,154 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:41:40,319 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.164 s. ]
2025-07-29 19:41:40,319 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:41:40,353 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0344 s. ]
2025-07-29 19:41:40,404 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:41:40,513 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 86 s. ]
2025-07-29 19:41:40,710 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.74 s. ]
2025-07-29 19:41:40,788 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:40,788 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:41:40,814 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-29 19:41:40,814 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:40,814 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-29 19:41:40,820 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 19:41:40,826 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:41:40,955 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:41:41,033 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:41:41,511 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-07-29 19:41:41,522 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:41,522 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:41:41,733 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:41:41,748 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 19:41:41,774 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:41,774 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:41:41,778 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:41:41,831 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:41:41,880 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:41:41,902 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:41:41,925 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:41:41,961 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:41:41,998 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:41:42,037 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:41:42,065 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:41:42,066 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 19:41:42,066 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:42,066 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:41:42,079 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:41:42,079 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:42,079 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:41:42,177 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:41:42,180 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:41:42,295 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 19:41:42,296 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:42,296 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:41:42,302 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:41:42,302 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:41:42,302 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:43:37,292 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 114.99 s. ]
2025-07-29 19:43:37,293 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:43:37,293 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 121 s. ]
2025-07-29 19:43:37,293 [main] INFO  com.polarion.platform.startup - ****************************************************************
