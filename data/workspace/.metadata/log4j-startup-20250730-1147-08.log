2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:08,574 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:47:12,725 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:47:12,906 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.181 s. ]
2025-07-30 11:47:12,906 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:47:12,952 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0453 s. ]
2025-07-30 11:47:13,013 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:47:13,171 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 49 s. ]
2025-07-30 11:47:13,466 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-07-30 11:47:13,540 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:13,540 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:47:13,568 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-30 11:47:13,568 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:13,568 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:47:13,573 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 11:47:13,573 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:47:13,573 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 11:47:13,573 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 11:47:13,573 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 11:47:13,574 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-30 11:47:13,581 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:47:13,716 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:47:13,808 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:47:14,258 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-07-30 11:47:14,268 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,268 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:47:14,474 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:47:14,484 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 11:47:14,523 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,523 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:47:14,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:47:14,578 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:47:14,617 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:47:14,638 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:47:14,651 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:47:14,673 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:47:14,694 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:47:14,717 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:47:14,741 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:47:14,741 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.26 s. ]
2025-07-30 11:47:14,741 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,741 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:47:14,755 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:47:14,755 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,755 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:47:14,855 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:47:14,860 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:47:14,978 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-30 11:47:14,978 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,978 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:47:14,985 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:47:14,985 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:47:14,985 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:47:17,345 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.36 s. ]
2025-07-30 11:47:17,345 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:47:17,345 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.77 s. ]
2025-07-30 11:47:17,345 [main] INFO  com.polarion.platform.startup - ****************************************************************
