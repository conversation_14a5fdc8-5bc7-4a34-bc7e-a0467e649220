2025-07-30 11:17:35,495 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0831 s [59% update (144x), 40% query (12x)] (221x), svn: 0.0173 s [50% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-07-30 11:17:35,663 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0687 s [67% info (3x), 27% getDir2 content (2x)] (6x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.829 s, CPU [user: 0.283 s, system: 0.311 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.148 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.225 s, system: 0.251 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.0785 s, system: 0.0811 s], Allocated memory: 9.6 MB, transactions: 0, svn: 0.108 s [28% log (1x), 25% log2 (5x), 24% info (5x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0676 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.0859 s, system: 0.101 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.1 s [75% log2 (10x), 14% getLatestRevision (2x)] (13x), ObjectMaps: 0.0443 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.83 s, CPU [user: 0.0904 s, system: 0.118 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.106 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.079 s [78% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 11:17:36,505 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.831 s, CPU [user: 0.129 s, system: 0.18 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0828 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0565 s [77% log2 (5x), 13% getLatestRevision (1x)] (7x)
2025-07-30 11:17:36,506 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.573 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.39 s [61% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 11:17:36,644 [main | u:p] INFO  TXLOGGER - Tx 66165577a4801_0_66165577a4801_0_: finished. Total: 0.113 s, CPU [user: 0.0859 s, system: 0.00427 s], Allocated memory: 21.8 MB
2025-07-30 11:17:36,832 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.279 s [100% getReadConfiguration (48x)] (48x), svn: 0.114 s [81% info (18x)] (38x)
2025-07-30 11:17:37,275 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.357 s [73% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.262 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:17:37,513 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.12 s, CPU [user: 0.0313 s, system: 0.00881 s], Allocated memory: 10.6 MB
2025-07-30 11:17:37,555 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.264 s [100% doFinishStartup (1x)] (1x), commit: 0.0585 s [100% Revision (1x)] (1x), Lucene: 0.0537 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0155 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:17:44,632 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.509 s [83% info (158x)] (171x), PullingJob: 0.0389 s [100% collectChanges (2x)] (2x)
2025-07-30 11:17:44,946 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616557f91443_0_6616557f91443_0_: finished. Total: 0.3 s, CPU [user: 0.145 s, system: 0.0193 s], Allocated memory: 21.0 MB, resolve: 0.0933 s [57% User (2x), 40% Project (1x)] (5x), svn: 0.0356 s [43% getLatestRevision (3x), 16% getFile content (2x), 16% log (1x), 13% testConnection (1x)] (9x), ObjectMaps: 0.0287 s [49% getPrimaryObjectLocation (2x), 42% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0227 s [100% search (1x)] (1x)
2025-07-30 11:17:45,613 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.887 s, CPU [user: 0.00588 s, system: 0.00277 s], Allocated memory: 315.5 kB, transactions: 1
2025-07-30 11:17:45,614 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.316 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.115 s [63% User (3x), 33% Project (1x)] (7x), Lucene: 0.0453 s [50% search (1x), 40% add (1x)] (3x), svn: 0.0356 s [43% getLatestRevision (3x), 16% getFile content (2x), 16% log (1x), 13% testConnection (1x)] (9x), ObjectMaps: 0.032 s [54% getPrimaryObjectLocation (3x), 38% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0305 s [77% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0291 s [100% WorkItem (22x)] (22x)
2025-07-30 11:17:45,614 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.994 s, CPU [user: 0.172 s, system: 0.0312 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.794 s [99% getDatedRevision (181x)] (183x)
2025-07-30 11:17:46,238 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616557f90040_0_6616557f90040_0_: finished. Total: 1.6 s, CPU [user: 0.405 s, system: 0.105 s], Allocated memory: 46.6 MB, svn: 0.993 s [59% getDatedRevision (181x), 26% getDir2 content (25x)] (307x), resolve: 0.462 s [100% Category (96x)] (96x), ObjectMaps: 0.154 s [45% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 11:17:46,503 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616558136048_0_6616558136048_0_: finished. Total: 0.174 s, CPU [user: 0.0759 s, system: 0.0179 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0864 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (162x)] (172x), svn: 0.078 s [62% info (19x), 32% getFile content (15x)] (36x), resolve: 0.0434 s [100% User (9x)] (9x), ObjectMaps: 0.02 s [54% getPrimaryObjectProperty (8x), 31% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:17:46,748 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661655817044a_0_661655817044a_0_: finished. Total: 0.187 s, CPU [user: 0.0508 s, system: 0.00991 s], Allocated memory: 19.9 MB, svn: 0.153 s [77% getDir2 content (17x), 23% getFile content (44x)] (62x), RepositoryConfigService: 0.0545 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:17:48,381 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661655819f04b_0_661655819f04b_0_: finished. Total: 1.63 s, CPU [user: 0.555 s, system: 0.0868 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.39 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.604 s [79% getFile content (412x), 21% getDir2 content (21x)] (434x)
2025-07-30 11:17:48,532 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661655833744c_0_661655833744c_0_: finished. Total: 0.15 s, CPU [user: 0.029 s, system: 0.00492 s], Allocated memory: 17.8 MB, svn: 0.137 s [89% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0238 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 11:17:49,140 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661655836c04e_0_661655836c04e_0_: finished. Total: 0.548 s, CPU [user: 0.196 s, system: 0.024 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.397 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.323 s [65% getFile content (185x), 35% getDir2 content (21x)] (207x)
2025-07-30 11:17:49,141 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.5 s, CPU [user: 1.4 s, system: 0.264 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.4 s [39% getFile content (807x), 33% getDir2 content (115x), 24% getDatedRevision (181x)] (1141x), RepositoryConfigService: 2.02 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.544 s [85% Category (96x)] (117x)
2025-07-30 11:17:49,141 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.2 s [43% getDatedRevision (362x), 29% getFile content (807x), 25% getDir2 content (115x)] (1325x), RepositoryConfigService: 2.02 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.544 s [85% Category (96x)] (118x), ObjectMaps: 0.186 s [47% getPrimaryObjectProperty (108x), 33% getPrimaryObjectLocation (114x), 20% getLastPromoted (108x)] (442x)
2025-07-30 11:17:50,645 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59561427-0a465820-522e238c-2d771068] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.397 s, CPU [user: 0.226 s, system: 0.057 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.335 s [100% authenticate (1x)] (1x)
2025-07-30 11:17:51,037 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5956169b-0a465820-522e238c-4a8a878a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.161 s, CPU [user: 0.0161 s, system: 0.00389 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 11:17:51,040 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59561684-0a465820-522e238c-33f16abc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.187 s, CPU [user: 0.0869 s, system: 0.0139 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:17:51,077 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5956169d-0a465820-522e238c-43c569b7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753845470800': Total: 0.199 s, CPU [user: 0.0446 s, system: 0.00631 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:17:51,126 [ajp-nio-127.0.0.1-8889-exec-8 | cID:595616ab-0a465820-522e238c-ab4c7221 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753845470802] INFO  TXLOGGER - Tx 66165585c8851_0_66165585c8851_0_: finished. Total: 0.116 s, CPU [user: 0.0572 s, system: 0.00909 s], Allocated memory: 5.0 MB, svn: 0.0167 s [75% testConnection (1x), 24% getFile content (2x)] (4x)
2025-07-30 11:17:51,136 [ajp-nio-127.0.0.1-8889-exec-8 | cID:595616ab-0a465820-522e238c-ab4c7221] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753845470802': Total: 0.243 s, CPU [user: 0.0757 s, system: 0.0131 s], Allocated memory: 7.0 MB, transactions: 1, RepositoryConfigService: 0.116 s [100% getReadConfiguration (1x)] (1x), svn: 0.0167 s [75% testConnection (1x), 24% getFile content (2x)] (4x)
2025-07-30 11:17:51,183 [ajp-nio-127.0.0.1-8889-exec-7 | cID:595616a4-0a465820-522e238c-94fe460c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753845470801': Total: 0.299 s, CPU [user: 0.0481 s, system: 0.00563 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.146 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:23:00,009 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.41318359375
2025-07-30 11:23:10,005 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.256103515625
2025-07-30 11:23:20,006 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.11640625
2025-07-30 11:23:30,008 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.044970703125
2025-07-30 11:23:40,008 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.040234375
