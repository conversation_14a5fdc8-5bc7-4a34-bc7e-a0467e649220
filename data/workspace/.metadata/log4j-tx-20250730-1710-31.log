2025-07-30 17:10:36,925 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.101 s [60% update (144x), 40% query (12x)] (221x), svn: 0.0151 s [52% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-30 17:10:37,049 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.031 s [59% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.0548 s, system: 0.0806 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0665 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.249 s, system: 0.296 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.157 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.294 s, system: 0.361 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.142 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.0917 s, system: 0.0979 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.128 s [80% log2 (10x)] (13x), ObjectMaps: 0.115 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.12 s, system: 0.134 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.204 s [60% log2 (10x), 14% getLatestRevision (3x), 11% info (5x)] (24x), ObjectMaps: 0.17 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:10:38,099 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.145 s, system: 0.217 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.195 s [99% getAllPrimaryObjects (1x)] (7x), svn: 0.0797 s [79% log2 (5x), 12% testConnection (1x)] (7x)
2025-07-30 17:10:38,100 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.846 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.506 s [68% log2 (36x), 14% getLatestRevision (9x)] (61x)
2025-07-30 17:10:38,376 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.236 s [100% getReadConfiguration (48x)] (48x), svn: 0.094 s [81% info (18x)] (38x)
2025-07-30 17:10:38,788 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.317 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.238 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:10:39,056 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.133 s, CPU [user: 0.0224 s, system: 0.00579 s], Allocated memory: 2.1 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:39,061 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.138 s, CPU [user: 0.00451 s, system: 0.00222 s], Allocated memory: 810.8 kB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:39,063 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.14 s, CPU [user: 0.034 s, system: 0.00586 s], Allocated memory: 2.8 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:39,086 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.103 s, CPU [user: 0.0251 s, system: 0.0107 s], Allocated memory: 12.8 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:39,105 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.181 s, CPU [user: 0.0299 s, system: 0.00975 s], Allocated memory: 10.5 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:39,170 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.366 s [100% doFinishStartup (1x)] (1x), Lucene: 0.104 s [100% refresh (1x)] (1x), commit: 0.0721 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0213 s [100% objectsToInv (1x)] (1x)
2025-07-30 17:10:41,429 [main | u:p | u:p] INFO  TXLOGGER - Tx 6616a647e983f_0_6616a647e983f_0_: finished. Total: 0.11 s, CPU [user: 0.0732 s, system: 0.0101 s], Allocated memory: 8.2 MB, GlobalHandler: 0.0111 s [96% applyTxChanges (1x)] (4x)
2025-07-30 17:10:41,693 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a991ec5-7f000001-71c5f29c-51534482] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.599 s, CPU [user: 0.336 s, system: 0.041 s], Allocated memory: 41.8 MB, transactions: 2, PolarionAuthenticator: 0.473 s [100% authenticate (1x)] (1x), resolve: 0.0519 s [100% User (1x)] (1x), GC: 0.045 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:10:41,964 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a99215b-7f000001-71c5f29c-4896b12d] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.206 s, CPU [user: 0.023 s, system: 0.0046 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.131 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0252 s [100% readUserData (1x)] (1x)
2025-07-30 17:10:42,547 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a9922f7-7f000001-71c5f29c-aef6070b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.38 s, CPU [user: 0.11 s, system: 0.0114 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 17:10:42,618 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a992342-7f000001-71c5f29c-bcbd2889] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753866642032': Total: 0.375 s, CPU [user: 0.0547 s, system: 0.00523 s], Allocated memory: 1.8 MB, transactions: 0
2025-07-30 17:10:42,665 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5a99233e-7f000001-71c5f29c-ad70a166] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.426 s, CPU [user: 0.0341 s, system: 0.00503 s], Allocated memory: 2.3 MB, transactions: 0, resolve: 0.212 s [100% Project (1x)] (1x), ObjectMaps: 0.0649 s [46% getPrimaryObjectProperty (1x), 42% getPrimaryObjectLocation (1x)] (4x), svn: 0.0561 s [37% log (1x), 26% testConnection (1x), 14% getLatestRevision (1x), 13% getFile content (1x)] (5x), GlobalHandler: 0.0382 s [100% put (1x)] (2x)
2025-07-30 17:10:42,908 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a99234f-7f000001-71c5f29c-1ef60965 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866642034] INFO  TXLOGGER - Tx 6616a64934442_0_6616a64934442_0_: finished. Total: 0.266 s, CPU [user: 0.0884 s, system: 0.0124 s], Allocated memory: 5.6 MB, svn: 0.0384 s [75% info (4x), 18% getFile content (2x)] (7x)
2025-07-30 17:10:42,945 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a99234f-7f000001-71c5f29c-1ef60965] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866642034': Total: 0.688 s, CPU [user: 0.125 s, system: 0.0204 s], Allocated memory: 8.6 MB, transactions: 1, RepositoryConfigService: 0.268 s [100% getReadConfiguration (1x)] (1x), resolve: 0.179 s [100% Project (1x)] (1x), svn: 0.109 s [33% getLatestRevision (1x), 31% info (5x), 13% testConnection (1x), 12% log (1x)] (12x), ObjectMaps: 0.0622 s [36% getPrimaryObjectProperty (1x), 33% getLastPromoted (1x), 31% getPrimaryObjectLocation (1x)] (4x)
2025-07-30 17:10:43,052 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a99234a-7f000001-71c5f29c-8c1cd4e8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753866642033': Total: 0.802 s, CPU [user: 0.104 s, system: 0.0137 s], Allocated memory: 6.3 MB, transactions: 1, RepositoryConfigService: 0.333 s [100% getReadConfiguration (1x)] (1x), resolve: 0.198 s [100% Project (1x)] (1x), ObjectMaps: 0.0565 s [48% getPrimaryObjectProperty (1x), 46% getPrimaryObjectLocation (1x)] (4x), svn: 0.0509 s [43% getLatestRevision (1x), 39% log (1x)] (5x)
2025-07-30 17:10:43,514 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 6, svn: 1.99 s [84% info (167x)] (199x), resolve: 0.648 s [91% Project (3x)] (7x), PolarionAuthenticator: 0.645 s [100% authenticate (35x)] (35x), RepositoryConfigService: 0.626 s [100% getReadConfiguration (5x)] (5x), ObjectMaps: 0.191 s [42% getPrimaryObjectProperty (4x), 41% getPrimaryObjectLocation (4x)] (16x), interceptor: 0.138 s [100% IAuthenticatorManager.getAuthenticators (70x)] (70x), UICustomizationDataProvider: 0.131 s [100% getUserDataValue (1x)] (1x)
2025-07-30 17:10:43,777 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a64a11c47_0_6616a64a11c47_0_: finished. Total: 0.249 s, CPU [user: 0.123 s, system: 0.0188 s], Allocated memory: 17.5 MB, Lucene: 0.0436 s [100% search (1x)] (1x)
2025-07-30 17:10:44,892 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.28 s, CPU [user: 0.00696 s, system: 0.00419 s], Allocated memory: 310.9 kB, transactions: 1
2025-07-30 17:10:44,897 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.296 s [91% RevisionActivityCreator (2x)] (6x), Lucene: 0.145 s [67% add (1x), 30% search (1x)] (3x), persistence listener: 0.0468 s [84% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.0444 s [100% WorkItem (22x)] (22x), resolve: 0.0176 s [88% Revision (3x)] (7x)
2025-07-30 17:10:44,899 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.39 s, CPU [user: 0.204 s, system: 0.0419 s], Allocated memory: 18.6 MB, transactions: 24, svn: 1.14 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0714 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 17:10:45,609 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64a12049_0_6616a64a12049_0_: finished. Total: 2.08 s, CPU [user: 0.441 s, system: 0.135 s], Allocated memory: 47.1 MB, svn: 1.14 s [60% getDatedRevision (181x), 23% getDir2 content (25x)] (307x), resolve: 0.791 s [100% Category (96x)] (96x), ObjectMaps: 0.338 s [64% getPrimaryObjectProperty (96x), 20% getPrimaryObjectLocation (96x)] (388x)
2025-07-30 17:10:45,827 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64c2f44c_0_6616a64c2f44c_0_: finished. Total: 0.133 s, CPU [user: 0.0622 s, system: 0.0133 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0655 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.0578 s [60% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0374 s [100% User (9x)] (9x), ObjectMaps: 0.0205 s [44% getPrimaryObjectProperty (8x), 41% getLastPromoted (8x)] (33x)
2025-07-30 17:10:46,149 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64c5b84e_0_6616a64c5b84e_0_: finished. Total: 0.279 s, CPU [user: 0.0782 s, system: 0.0152 s], Allocated memory: 19.9 MB, svn: 0.208 s [61% getDir2 content (17x), 39% getFile content (44x)] (62x), RepositoryConfigService: 0.13 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:10:47,271 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64ca144f_0_6616a64ca144f_0_: finished. Total: 1.12 s, CPU [user: 0.47 s, system: 0.0605 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.863 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.559 s [67% getFile content (412x), 33% getDir2 content (21x)] (434x), GC: 0.083 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 17:10:47,561 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64db9c50_0_6616a64db9c50_0_: finished. Total: 0.289 s, CPU [user: 0.0659 s, system: 0.01 s], Allocated memory: 17.8 MB, svn: 0.259 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0622 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 17:10:48,036 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a64e12c52_0_6616a64e12c52_0_: finished. Total: 0.408 s, CPU [user: 0.15 s, system: 0.0147 s], Allocated memory: 384.3 MB, svn: 0.26 s [51% getFile content (185x), 49% getDir2 content (21x)] (207x), RepositoryConfigService: 0.259 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 17:10:48,036 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.51 s, CPU [user: 1.34 s, system: 0.263 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.6 s [38% getDir2 content (115x), 33% getFile content (807x), 26% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.44 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.87 s [91% Category (96x)] (117x), ObjectMaps: 0.376 s [63% getPrimaryObjectProperty (108x), 20% getPrimaryObjectLocation (114x)] (444x)
2025-07-30 17:10:48,036 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.74 s [48% getDatedRevision (362x), 26% getDir2 content (115x), 23% getFile content (807x)] (1325x), RepositoryConfigService: 1.44 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.875 s [90% Category (96x)] (118x), ObjectMaps: 0.376 s [63% getPrimaryObjectProperty (108x), 20% getPrimaryObjectLocation (114x)] (444x)
2025-07-30 17:11:06,335 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.122 s, CPU [user: 0.00259 s, system: 0.00769 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0398 s [100% collectChanges (1x)] (1x), svn: 0.0376 s [100% getLatestRevision (1x)] (1x)
