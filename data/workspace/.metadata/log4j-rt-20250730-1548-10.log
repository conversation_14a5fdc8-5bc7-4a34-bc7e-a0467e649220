2025-07-30 15:48:23,613 [Catalina-utility-1] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 15:48:24,513 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 15:48:24,514 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[84]')
2025-07-30 15:48:24,515 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[86]')
2025-07-30 15:48:24,516 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 15:48:24,519 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[84]')
2025-07-30 15:48:24,519 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[84]')
2025-07-30 15:48:24,523 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[90]')
2025-07-30 15:48:27,019 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 15:48:27,158 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a4dd583-7f000001-38ff2165-2955cd75] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 15:48:27,186 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 15:48:27,186 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
