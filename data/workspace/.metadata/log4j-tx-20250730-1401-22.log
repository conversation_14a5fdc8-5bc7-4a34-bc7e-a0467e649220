2025-07-30 14:01:27,953 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0555 s [65% update (144x), 34% query (12x)] (221x), svn: 0.0153 s [46% getLatestRevision (2x), 44% testConnection (1x)] (4x)
2025-07-30 14:01:28,235 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0507 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.847 s, CPU [user: 0.14 s, system: 0.219 s], Allocated memory: 24.3 MB, transactions: 0, ObjectMaps: 0.0987 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.847 s, CPU [user: 0.233 s, system: 0.295 s], Allocated memory: 52.9 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.847 s, CPU [user: 0.079 s, system: 0.101 s], Allocated memory: 9.2 MB, transactions: 0, svn: 0.108 s [38% log2 (5x), 22% info (5x), 19% log (1x), 9% getLatestRevision (2x)] (18x), ObjectMaps: 0.0822 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.283 s, system: 0.354 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.847 s, CPU [user: 0.0979 s, system: 0.147 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0935 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0922 s [75% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-07-30 14:01:29,087 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.0903 s, system: 0.118 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0986 s [81% log2 (10x)] (13x), ObjectMaps: 0.064 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 14:01:29,087 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.592 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.386 s [64% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 14:01:29,395 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.257 s [100% getReadConfiguration (48x)] (48x), svn: 0.109 s [85% info (18x)] (38x)
2025-07-30 14:01:30,022 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.489 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.365 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 14:01:30,398 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.166 s, CPU [user: 0.0311 s, system: 0.00892 s], Allocated memory: 10.5 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 14:01:30,450 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.39 s [100% doFinishStartup (1x)] (1x), commit: 0.079 s [100% Revision (1x)] (1x), Lucene: 0.0557 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0302 s [100% objectsToInv (1x)] (1x), DB: 0.026 s [43% update (3x), 39% query (1x)] (8x)
2025-07-30 14:01:33,741 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59ebf769-0a465820-010463c5-df0981a3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.451 s, CPU [user: 0.273 s, system: 0.0309 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.399 s [100% authenticate (1x)] (1x), resolve: 0.064 s [100% User (1x)] (1x)
2025-07-30 14:01:33,987 [ajp-nio-127.0.0.1-8889-exec-10 | cID:59ebf995-0a465820-010463c5-b4b723b7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/js/model/connection.js?buildId=20220419-1528-22_R1-be3adceb': Total: 0.139 s, CPU [user: 0.00151 s, system: 0.000672 s], Allocated memory: 41.2 kB, transactions: 0, GC: 0.09 s [100% G1 Young Generation (1x)] (1x), PolarionAuthenticator: 0.0102 s [100% authenticate (1x)] (1x)
2025-07-30 14:01:34,013 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59ebf9b6-0a465820-010463c5-911ee2f5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/js/model/proxyConfiguration.js?buildId=20220419-1528-22_R1-be3adceb': Total: 0.117 s, CPU [user: 0.00173 s, system: 0.000677 s], Allocated memory: 39.6 kB, transactions: 0, GC: 0.09 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 14:01:34,025 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59ebf9b9-0a465820-010463c5-6b71a4ca] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/js/model/syncPair.js?buildId=20220419-1528-22_R1-be3adceb': Total: 0.105 s, CPU [user: 0.00117 s, system: 0.000375 s], Allocated memory: 43.6 kB, transactions: 0, GC: 0.09 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 14:01:34,080 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59ebf96c-0a465820-010463c5-c6d5f535] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.271 s, CPU [user: 0.0244 s, system: 0.00645 s], Allocated memory: 4.4 MB, transactions: 0, GC: 0.09 s [100% G1 Young Generation (1x)] (1x), UICustomizationDataProvider: 0.0896 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0563 s [100% readUserData (1x)] (1x), PolarionAuthenticator: 0.0169 s [100% authenticate (1x)] (1x), interceptor: 0.0146 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 14:01:34,392 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59ebfad6-0a465820-010463c5-c92bef85] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.225 s, CPU [user: 0.103 s, system: 0.0107 s], Allocated memory: 9.7 MB, transactions: 0
2025-07-30 14:01:34,437 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59ebfb3b-0a465820-010463c5-4de0ddf0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753855294090': Total: 0.169 s, CPU [user: 0.0453 s, system: 0.00533 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 14:01:34,465 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59ebfb2e-0a465820-010463c5-1b86494b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.21 s, CPU [user: 0.021 s, system: 0.00446 s], Allocated memory: 1.9 MB, transactions: 0, resolve: 0.121 s [100% Project (1x)] (1x), svn: 0.0335 s [36% log (1x), 30% testConnection (1x), 23% getLatestRevision (1x)] (5x), ObjectMaps: 0.0298 s [53% getPrimaryObjectProperty (1x), 28% getPrimaryObjectLocation (1x)] (4x), GlobalHandler: 0.0203 s [99% put (1x)] (2x)
2025-07-30 14:01:34,637 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59ebfb3b-0a465820-010463c5-ebcd1a64 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753855294091] INFO  TXLOGGER - Tx 66167afefec42_0_66167afefec42_0_: finished. Total: 0.171 s, CPU [user: 0.0692 s, system: 0.00855 s], Allocated memory: 5.4 MB, svn: 0.0185 s [65% info (4x), 35% getFile content (2x)] (7x)
2025-07-30 14:01:34,690 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59ebfb3b-0a465820-010463c5-ebcd1a64] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753855294091': Total: 0.422 s, CPU [user: 0.118 s, system: 0.0167 s], Allocated memory: 8.9 MB, transactions: 1, RepositoryConfigService: 0.179 s [100% getReadConfiguration (1x)] (1x), resolve: 0.111 s [100% Project (1x)] (1x), svn: 0.0595 s [24% info (5x), 23% testConnection (1x), 21% getLatestRevision (1x), 18% log (1x)] (12x), ObjectMaps: 0.0303 s [51% getPrimaryObjectProperty (1x), 30% getPrimaryObjectLocation (1x)] (4x)
2025-07-30 14:01:34,697 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59ebfb3c-0a465820-010463c5-a3cab07a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753855294092': Total: 0.428 s, CPU [user: 0.06 s, system: 0.00906 s], Allocated memory: 5.5 MB, transactions: 1, RepositoryConfigService: 0.223 s [100% getReadConfiguration (1x)] (1x), resolve: 0.118 s [100% Project (1x)] (1x), ObjectMaps: 0.0315 s [47% getPrimaryObjectProperty (1x), 29% getLastPromoted (1x), 24% getPrimaryObjectLocation (1x)] (4x), svn: 0.0278 s [41% log (1x), 25% testConnection (1x), 18% getLatestRevision (1x)] (6x)
2025-07-30 14:01:35,205 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 6, svn: 1.55 s [75% info (167x), 16% getDir2 content (7x)] (200x), PolarionAuthenticator: 0.558 s [100% authenticate (34x)] (34x), RepositoryConfigService: 0.423 s [100% getReadConfiguration (5x)] (5x), resolve: 0.416 s [84% Project (3x)] (7x), interceptor: 0.109 s [100% IAuthenticatorManager.getAuthenticators (68x)] (68x), ObjectMaps: 0.103 s [47% getPrimaryObjectProperty (4x), 33% getPrimaryObjectLocation (4x)] (16x), UICustomizationDataProvider: 0.0896 s [100% getUserDataValue (1x)] (1x)
2025-07-30 14:01:35,220 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.00177 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.000159 s [100% GET (2x)] (2x)
2025-07-30 14:01:35,572 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66167affbd446_0_66167affbd446_0_: finished. Total: 0.351 s, CPU [user: 0.124 s, system: 0.0156 s], Allocated memory: 17.5 MB, Lucene: 0.0228 s [100% search (1x)] (1x)
2025-07-30 14:01:36,484 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.14 s, CPU [user: 0.0092 s, system: 0.00361 s], Allocated memory: 310.2 kB, transactions: 1
2025-07-30 14:01:36,485 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.377 s [96% RevisionActivityCreator (2x)] (5x), Lucene: 0.0889 s [49% refresh (1x), 26% search (1x), 25% add (1x)] (3x), Incremental Baseline: 0.0361 s [100% WorkItem (22x)] (22x), persistence listener: 0.0278 s [68% indexRefreshPersistenceListener (1x), 12% PlanActivityCreator (1x), 12% WorkItemActivityCreator (1x)] (7x)
2025-07-30 14:01:36,486 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.29 s, CPU [user: 0.189 s, system: 0.041 s], Allocated memory: 18.6 MB, transactions: 24, svn: 0.966 s [99% getDatedRevision (181x)] (183x)
2025-07-30 14:01:37,122 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167affbe849_0_66167affbe849_0_: finished. Total: 1.9 s, CPU [user: 0.434 s, system: 0.131 s], Allocated memory: 47.1 MB, svn: 1.15 s [51% getDatedRevision (181x), 31% getDir2 content (25x)] (307x), resolve: 0.625 s [100% Category (96x)] (96x), ObjectMaps: 0.2 s [44% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (388x)
2025-07-30 14:01:37,403 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167b01b244c_0_66167b01b244c_0_: finished. Total: 0.177 s, CPU [user: 0.076 s, system: 0.0195 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0882 s [56% getReadConfiguration (162x), 44% getReadUserConfiguration (10x)] (172x), svn: 0.075 s [62% info (19x), 33% getFile content (15x)] (36x), resolve: 0.0443 s [100% User (9x)] (9x), ObjectMaps: 0.0199 s [53% getPrimaryObjectProperty (8x), 27% getLastPromoted (8x), 20% getPrimaryObjectLocation (8x)] (33x)
2025-07-30 14:01:37,839 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167b01ef04e_0_66167b01ef04e_0_: finished. Total: 0.37 s, CPU [user: 0.0981 s, system: 0.0218 s], Allocated memory: 19.9 MB, svn: 0.256 s [57% getDir2 content (17x), 43% getFile content (44x)] (62x), RepositoryConfigService: 0.195 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 14:01:38,976 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167b024bc4f_0_66167b024bc4f_0_: finished. Total: 1.14 s, CPU [user: 0.46 s, system: 0.0664 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.859 s [94% getReadConfiguration (8682x)] (9021x), svn: 0.617 s [67% getFile content (412x), 33% getDir2 content (21x)] (434x)
2025-07-30 14:01:39,167 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167b0368050_0_66167b0368050_0_: finished. Total: 0.191 s, CPU [user: 0.0437 s, system: 0.00501 s], Allocated memory: 17.9 MB, svn: 0.172 s [84% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0408 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 14:01:39,645 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167b03a6052_0_66167b03a6052_0_: finished. Total: 0.421 s, CPU [user: 0.16 s, system: 0.0122 s], Allocated memory: 384.9 MB, svn: 0.289 s [51% getDir2 content (21x), 49% getFile content (185x)] (207x), RepositoryConfigService: 0.254 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 14:01:39,645 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.44 s, CPU [user: 1.36 s, system: 0.277 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.68 s [39% getDir2 content (115x), 35% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.51 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.718 s [87% Category (96x)] (117x), ObjectMaps: 0.236 s [46% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (444x)
2025-07-30 14:01:39,645 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.65 s [42% getDatedRevision (362x), 29% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.51 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.718 s [87% Category (96x)] (118x), ObjectMaps: 0.236 s [46% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (444x)
2025-07-30 14:01:44,303 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59ec2185-0a465820-010463c5-ffc4bd24 | u:admin] INFO  TXLOGGER - Tx 66167b087ec53_0_66167b087ec53_0_: finished. Total: 0.116 s, CPU [user: 0.0501 s, system: 0.0198 s], Allocated memory: 4.0 MB, svn: 0.0435 s [35% info (2x), 21% getDir2 content (1x), 17% log (1x), 14% getFile content (1x)] (7x), resolve: 0.0377 s [99% Project (4x)] (5x), RepositoryConfigService: 0.0259 s [100% getReadConfiguration (10x)] (15x), ObjectMaps: 0.0174 s [69% getPrimaryObjectProperty (1x), 13% getPrimaryObjectLocation (1x)] (6x)
2025-07-30 14:01:44,420 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59ec2185-0a465820-010463c5-ffc4bd24] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.35 s, CPU [user: 0.207 s, system: 0.0662 s], Allocated memory: 66.0 MB, transactions: 1, RPC: 0.197 s [41% decodeRequest (1x), 40% encodeResponse (1x)] (4x), PortalDataService: 0.118 s [100% getInitData (1x)] (1x), svn: 0.0435 s [35% info (2x), 21% getDir2 content (1x), 17% log (1x), 14% getFile content (1x)] (7x), resolve: 0.0377 s [99% Project (4x)] (5x), RepositoryConfigService: 0.0259 s [100% getReadConfiguration (10x)] (15x)
2025-07-30 14:01:44,714 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59ec2335-0a465820-010463c5-dc513beb | u:admin] INFO  TXLOGGER - Tx 66167b08cfc54_0_66167b08cfc54_0_: finished. Total: 0.202 s, CPU [user: 0.11 s, system: 0.0304 s], Allocated memory: 15.7 MB, RepositoryConfigService: 0.0657 s [93% getReadConfiguration (13x)] (18x), svn: 0.0434 s [29% getFile content (6x), 25% getDir2 content (1x), 25% info (4x), 21% testConnection (1x)] (13x)
2025-07-30 14:01:44,739 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59ec2335-0a465820-010463c5-dc513beb] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.237 s, CPU [user: 0.132 s, system: 0.0374 s], Allocated memory: 18.8 MB, transactions: 1, PortalDataService: 0.203 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0657 s [93% getReadConfiguration (13x)] (18x), svn: 0.0434 s [29% getFile content (6x), 25% getDir2 content (1x), 25% info (4x), 21% testConnection (1x)] (13x), RPC: 0.0317 s [72% encodeResponse (1x), 22% decodeRequest (1x)] (4x)
