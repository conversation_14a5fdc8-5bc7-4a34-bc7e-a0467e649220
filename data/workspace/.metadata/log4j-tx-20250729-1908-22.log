2025-07-29 19:08:27,794 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0634 s [62% update (144x), 38% query (12x)] (221x), svn: 0.0138 s [57% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-29 19:08:27,970 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0313 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.181 s, system: 0.27 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.103 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.0524 s, system: 0.0993 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0748 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.035 s [75% log2 (5x), 13% getLatestRevision (1x)] (7x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.688 s, CPU [user: 0.0796 s, system: 0.118 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0816 s [82% log2 (10x)] (13x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.688 s, CPU [user: 0.234 s, system: 0.329 s], Allocated memory: 70.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0732 s [28% info (5x), 24% log2 (5x), 22% log (1x), 9% testConnection (1x)] (18x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.688 s, CPU [user: 0.0773 s, system: 0.14 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0794 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0777 s [80% log2 (10x)] (13x)
2025-07-29 19:08:28,662 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.689 s, CPU [user: 0.107 s, system: 0.202 s], Allocated memory: 24.4 MB, transactions: 0, ObjectMaps: 0.0618 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 19:08:28,663 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.47 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.312 s [64% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 19:08:28,900 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.202 s [100% getReadConfiguration (48x)] (48x), svn: 0.0721 s [82% info (18x)] (38x)
2025-07-29 19:08:29,215 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.242 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.189 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:08:29,455 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.115 s, CPU [user: 0.0269 s, system: 0.00764 s], Allocated memory: 10.7 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 19:08:29,485 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.258 s [100% doFinishStartup (1x)] (1x), commit: 0.0646 s [100% Revision (1x)] (1x), Lucene: 0.0348 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.024 s [100% objectsToInv (1x)] (1x)
2025-07-29 19:08:46,136 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.354 s [73% info (158x), 16% getLatestRevision (6x)] (174x), PullingJob: 0.0558 s [100% collectChanges (5x)] (5x)
2025-07-29 19:08:46,895 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.695 s, CPU [user: 0.00581 s, system: 0.00127 s], Allocated memory: 531.5 kB, transactions: 1
2025-07-29 19:08:46,896 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, Incremental Baseline: 0.0294 s [100% WorkItem (21x)] (21x), notification worker: 0.0177 s [44% RevisionActivityCreator (2x), 24% WorkItemActivityCreator (1x), 18% TestRunActivityCreator (1x)] (6x), resolve: 0.0141 s [81% User (1x)] (3x), persistence listener: 0.0125 s [71% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x), Lucene: 0.00394 s [100% refresh (1x)] (1x), DB: 0.00354 s [87% update (1x)] (2x), ObjectMaps: 0.00314 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 19:08:46,896 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.762 s, CPU [user: 0.157 s, system: 0.0258 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.616 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0501 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (22x)] (23x)
2025-07-29 19:08:47,225 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661577b7b0045_0_661577b7b0045_0_: finished. Total: 1.08 s, CPU [user: 0.356 s, system: 0.0785 s], Allocated memory: 54.6 MB, svn: 0.622 s [49% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.372 s [100% Category (96x)] (96x), ObjectMaps: 0.126 s [44% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 19:08:47,424 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661577b8d4c48_0_661577b8d4c48_0_: finished. Total: 0.108 s, CPU [user: 0.054 s, system: 0.00877 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0481 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0433 s [55% info (19x), 40% getFile content (16x)] (37x), resolve: 0.0354 s [100% User (9x)] (9x), ObjectMaps: 0.0182 s [42% getPrimaryObjectProperty (9x), 40% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 19:08:47,709 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661577b8fa44a_0_661577b8fa44a_0_: finished. Total: 0.243 s, CPU [user: 0.0732 s, system: 0.00784 s], Allocated memory: 19.9 MB, svn: 0.153 s [64% getDir2 content (17x), 36% getFile content (44x)] (62x), RepositoryConfigService: 0.115 s [99% getReadConfiguration (170x)] (192x)
2025-07-29 19:08:48,658 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661577b93744b_0_661577b93744b_0_: finished. Total: 0.948 s, CPU [user: 0.405 s, system: 0.026 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.66 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.544 s [57% getFile content (412x), 43% getDir2 content (21x)] (434x)
2025-07-29 19:08:49,012 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661577ba40c4e_0_661577ba40c4e_0_: finished. Total: 0.241 s, CPU [user: 0.101 s, system: 0.00505 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.157 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.149 s [54% getFile content (185x), 46% getDir2 content (20x)] (206x)
2025-07-29 19:08:49,012 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.87 s, CPU [user: 1.08 s, system: 0.139 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.66 s [42% getDir2 content (114x), 37% getFile content (809x), 18% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.04 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.453 s [82% Category (96x)] (117x), ObjectMaps: 0.16 s [46% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-29 19:08:49,012 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.28 s [40% getDatedRevision (362x), 30% getDir2 content (114x), 27% getFile content (809x)] (1328x), RepositoryConfigService: 1.04 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.453 s [82% Category (96x)] (118x), ObjectMaps: 0.16 s [46% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 21% getLastPromoted (110x)] (452x)
2025-07-29 19:08:54,031 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55defc07-0a465820-3d6c7e2f-d9f8697e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.391 s, CPU [user: 0.225 s, system: 0.041 s], Allocated memory: 42.3 MB, transactions: 2, PolarionAuthenticator: 0.351 s [100% authenticate (1x)] (1x)
2025-07-29 19:08:54,401 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55defe67-0a465820-3d6c7e2f-bb3d41ba] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.154 s, CPU [user: 0.0878 s, system: 0.0117 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-29 19:08:54,402 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55defe90-0a465820-3d6c7e2f-88fdd678] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.112 s, CPU [user: 0.0144 s, system: 0.00321 s], Allocated memory: 1.3 MB, transactions: 0
2025-07-29 19:08:54,421 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55defe90-0a465820-3d6c7e2f-7054d294] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753787334202': Total: 0.132 s, CPU [user: 0.0381 s, system: 0.00505 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-29 19:08:54,464 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55defe94-0a465820-3d6c7e2f-ba31e04e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753787334204': Total: 0.171 s, CPU [user: 0.05 s, system: 0.0116 s], Allocated memory: 8.1 MB, transactions: 1, RepositoryConfigService: 0.0839 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 19:08:54,486 [ajp-nio-127.0.0.1-8889-exec-5 | cID:55defe90-0a465820-3d6c7e2f-5dd2d9df] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753787334203': Total: 0.197 s, CPU [user: 0.0292 s, system: 0.00319 s], Allocated memory: 2.6 MB, transactions: 1, RepositoryConfigService: 0.105 s [100% getReadConfiguration (1x)] (1x)
