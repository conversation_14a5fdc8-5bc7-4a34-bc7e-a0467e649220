2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:51:58,417 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:52:02,776 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:52:02,918 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-30 11:52:02,918 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:52:02,954 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0358 s. ]
2025-07-30 11:52:03,003 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:52:03,113 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 47 s. ]
2025-07-30 11:52:03,314 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-07-30 11:52:03,402 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:03,402 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:52:03,425 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:52:03,425 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:03,425 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 11:52:03,430 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 11:52:03,436 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 11:52:03,562 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:52:03,671 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:52:04,144 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.72 s. ]
2025-07-30 11:52:04,156 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,156 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:52:04,367 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:52:04,380 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 11:52:04,407 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,407 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:52:04,413 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:52:04,462 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:52:04,502 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 11:52:04,553 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 11:52:04,590 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 11:52:04,613 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 11:52:04,635 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 11:52:04,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:52:04,718 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:52:04,718 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 11:52:04,719 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,719 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:52:04,734 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:52:04,734 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,734 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:52:04,832 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:52:04,834 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:52:04,965 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 11:52:04,965 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,965 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:52:04,972 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:52:04,972 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:52:04,972 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:52:07,557 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.59 s. ]
2025-07-30 11:52:07,557 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:52:07,557 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.14 s. ]
2025-07-30 11:52:07,557 [main] INFO  com.polarion.platform.startup - ****************************************************************
