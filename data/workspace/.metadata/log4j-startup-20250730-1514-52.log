2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:52,321 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:14:56,720 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:14:56,866 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.146 s. ]
2025-07-30 15:14:56,866 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:14:56,917 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0507 s. ]
2025-07-30 15:14:56,967 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:14:57,072 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:14:57,280 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.96 s. ]
2025-07-30 15:14:57,364 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:57,367 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:14:57,480 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.2 s. ]
2025-07-30 15:14:57,481 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:57,481 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:14:57,505 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (1/9)
2025-07-30 15:14:57,505 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 15:14:57,505 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 15:14:57,505 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 15:14:57,506 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 15:14:57,507 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 15:14:57,526 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:14:57,743 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:14:57,877 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:14:58,388 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.91 s. ]
2025-07-30 15:14:58,421 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:58,421 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:14:58,886 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:14:58,912 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.52 s. ]
2025-07-30 15:14:58,953 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:58,953 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:14:58,966 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:14:59,069 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:14:59,124 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:14:59,183 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:14:59,248 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:14:59,282 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:14:59,308 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:14:59,373 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:14:59,441 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:14:59,441 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.53 s. ]
2025-07-30 15:14:59,441 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:59,441 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:14:59,455 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 15:14:59,455 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:59,455 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:14:59,609 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:14:59,613 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:14:59,750 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-07-30 15:14:59,751 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:59,751 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:14:59,767 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.02 s. ]
2025-07-30 15:14:59,767 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:14:59,767 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:15:02,302 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.53 s. ]
2025-07-30 15:15:02,302 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:15:02,302 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.98 s. ]
2025-07-30 15:15:02,302 [main] INFO  com.polarion.platform.startup - ****************************************************************
