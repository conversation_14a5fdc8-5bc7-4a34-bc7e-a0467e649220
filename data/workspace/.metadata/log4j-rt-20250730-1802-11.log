2025-07-30 18:02:19,271 [Catalina-utility-2] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 18:02:20,362 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 18:02:20,364 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 18:02:20,365 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 18:02:20,367 [Catalina-utility-2] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-30 18:02:20,371 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 18:02:20,371 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 18:02:20,372 [Catalina-utility-2] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-30 18:02:36,763 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 18:02:36,875 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5ac8a9b7-7f000001-4adc0631-48968fc1] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 18:02:36,894 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 18:02:36,895 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
