2025-07-30 11:46:03,841 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.072 s [74% update (144x), 26% query (12x)] (221x), svn: 0.0159 s [64% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-30 11:46:03,953 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0335 s [54% getDir2 content (2x), 39% info (3x)] (6x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.686 s, CPU [user: 0.22 s, system: 0.319 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.687 s, CPU [user: 0.0733 s, system: 0.106 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.102 s [80% log2 (10x)] (13x), ObjectMaps: 0.0413 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.687 s, CPU [user: 0.0457 s, system: 0.0947 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0625 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0368 s [81% log2 (5x)] (7x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.686 s, CPU [user: 0.0743 s, system: 0.128 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0682 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0635 s [77% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.687 s, CPU [user: 0.183 s, system: 0.26 s], Allocated memory: 53.3 MB, transactions: 0, ObjectMaps: 0.106 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:46:04,643 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.687 s, CPU [user: 0.115 s, system: 0.192 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.084 s [26% log2 (5x), 23% info (5x), 23% log (1x), 15% getLatestRevision (2x)] (18x), ObjectMaps: 0.0622 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:46:04,644 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.463 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.329 s [64% log2 (36x), 15% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 11:46:04,767 [main | u:p] INFO  TXLOGGER - Tx 66165bfbbe001_0_66165bfbbe001_0_: finished. Total: 0.101 s, CPU [user: 0.0809 s, system: 0.00335 s], Allocated memory: 21.8 MB
2025-07-30 11:46:04,905 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.227 s [100% getReadConfiguration (48x)] (48x), svn: 0.0852 s [85% info (18x)] (38x)
2025-07-30 11:46:05,288 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.301 s [77% info (94x), 15% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.232 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:46:05,539 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.117 s, CPU [user: 0.0336 s, system: 0.0105 s], Allocated memory: 10.8 MB
2025-07-30 11:46:05,584 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.278 s [100% doFinishStartup (1x)] (1x), commit: 0.0714 s [100% Revision (1x)] (1x), Lucene: 0.044 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0235 s [100% objectsToInv (1x)] (1x), DB: 0.0142 s [42% update (3x), 28% query (1x), 15% execute (1x)] (8x)
2025-07-30 11:46:08,206 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.419 s [89% info (158x)] (168x)
2025-07-30 11:46:08,558 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165bff3f845_0_66165bff3f845_0_: finished. Total: 0.302 s, CPU [user: 0.147 s, system: 0.0182 s], Allocated memory: 20.7 MB, resolve: 0.0777 s [61% User (2x), 36% Project (1x)] (5x), svn: 0.0332 s [48% getLatestRevision (3x), 20% log (1x), 13% getFile content (2x)] (9x), Lucene: 0.0287 s [100% search (1x)] (1x), ObjectMaps: 0.0234 s [54% getPrimaryObjectProperty (2x), 38% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 11:46:09,095 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.777 s, CPU [user: 0.00757 s, system: 0.00208 s], Allocated memory: 528.8 kB, transactions: 1
2025-07-30 11:46:09,096 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.315 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.116 s [72% User (4x), 24% Project (1x)] (8x), persistence listener: 0.0545 s [51% indexRefreshPersistenceListener (1x), 37% WorkItemActivityCreator (1x)] (7x), Lucene: 0.049 s [58% search (1x), 25% add (1x)] (3x), svn: 0.041 s [51% getLatestRevision (4x), 17% testConnection (2x), 16% log (1x)] (11x), ObjectMaps: 0.0345 s [58% getPrimaryObjectLocation (4x), 36% getPrimaryObjectProperty (2x)] (13x), Incremental Baseline: 0.0177 s [100% WorkItem (22x)] (22x)
2025-07-30 11:46:09,096 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.782 s, CPU [user: 0.16 s, system: 0.0243 s], Allocated memory: 18.3 MB, transactions: 23, svn: 0.685 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0463 s [84% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:46:09,462 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165bff36840_0_66165bff36840_0_: finished. Total: 1.24 s, CPU [user: 0.315 s, system: 0.0778 s], Allocated memory: 46.6 MB, svn: 0.763 s [44% getDatedRevision (181x), 39% getDir2 content (25x)] (307x), resolve: 0.337 s [100% Category (96x)] (96x), ObjectMaps: 0.12 s [42% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 11:46:09,671 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165c007e848_0_66165c007e848_0_: finished. Total: 0.141 s, CPU [user: 0.0691 s, system: 0.011 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0671 s [50% getReadUserConfiguration (10x), 50% getReadConfiguration (162x)] (172x), svn: 0.058 s [57% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0403 s [100% User (9x)] (9x), ObjectMaps: 0.0199 s [56% getPrimaryObjectProperty (8x), 25% getLastPromoted (8x)] (32x)
2025-07-30 11:46:09,939 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165c00b104a_0_66165c00b104a_0_: finished. Total: 0.206 s, CPU [user: 0.0627 s, system: 0.00742 s], Allocated memory: 19.9 MB, svn: 0.166 s [72% getDir2 content (17x), 28% getFile content (44x)] (62x), RepositoryConfigService: 0.0706 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:46:10,716 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165c00e4c4b_0_66165c00e4c4b_0_: finished. Total: 0.777 s, CPU [user: 0.36 s, system: 0.0219 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.602 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.411 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-30 11:46:11,063 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165c01c284e_0_66165c01c284e_0_: finished. Total: 0.237 s, CPU [user: 0.101 s, system: 0.0054 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.152 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.147 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x)
2025-07-30 11:46:11,064 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.85 s, CPU [user: 0.996 s, system: 0.135 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.69 s [42% getDir2 content (115x), 34% getFile content (807x), 20% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.967 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.409 s [82% Category (96x)] (117x), ObjectMaps: 0.154 s [44% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 11:46:11,064 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.38 s [43% getDatedRevision (362x), 30% getDir2 content (115x), 24% getFile content (807x)] (1324x), RepositoryConfigService: 0.967 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.409 s [82% Category (96x)] (118x), ObjectMaps: 0.154 s [44% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 11:46:23,838 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5970379f-7f000001-1cad68ca-d26a1bb2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.573 s, CPU [user: 0.269 s, system: 0.1 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.506 s [100% authenticate (1x)] (1x)
2025-07-30 11:46:24,211 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59703ad1-7f000001-1cad68ca-131e3c22] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.129 s, CPU [user: 0.067 s, system: 0.0146 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 11:46:24,211 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59703aec-7f000001-1cad68ca-61fa8e6d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.102 s, CPU [user: 0.016 s, system: 0.00504 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:46:24,268 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59703aec-7f000001-1cad68ca-c66918ba] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753847184045': Total: 0.159 s, CPU [user: 0.0389 s, system: 0.00734 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 11:46:24,311 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59703af0-7f000001-1cad68ca-dcb6f81f | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847184046] INFO  TXLOGGER - Tx 66165c0ed1851_0_66165c0ed1851_0_: finished. Total: 0.114 s, CPU [user: 0.0424 s, system: 0.00944 s], Allocated memory: 5.0 MB, svn: 0.0219 s [71% getFile content (2x), 29% testConnection (1x)] (4x)
2025-07-30 11:46:24,333 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59703af0-7f000001-1cad68ca-20e29a49] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847184047': Total: 0.22 s, CPU [user: 0.0252 s, system: 0.00443 s], Allocated memory: 3.4 MB, transactions: 1, RepositoryConfigService: 0.134 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:46:24,334 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59703af0-7f000001-1cad68ca-dcb6f81f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847184046': Total: 0.221 s, CPU [user: 0.0649 s, system: 0.0142 s], Allocated memory: 7.1 MB, transactions: 1, RepositoryConfigService: 0.114 s [100% getReadConfiguration (1x)] (1x), svn: 0.0219 s [71% getFile content (2x), 29% testConnection (1x)] (4x)
