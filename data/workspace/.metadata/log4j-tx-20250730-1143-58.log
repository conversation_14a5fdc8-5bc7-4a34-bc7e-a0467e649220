2025-07-30 11:44:05,208 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0471 s [62% update (144x), 38% query (12x)] (221x), svn: 0.0156 s [54% getLatestRevision (2x), 34% testConnection (1x)] (4x)
2025-07-30 11:44:05,349 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0406 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 11:44:06,201 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.0593 s, system: 0.0903 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.067 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:44:06,201 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.254 s, system: 0.33 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:44:06,201 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.126 s, system: 0.205 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0685 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:44:06,201 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.0945 s, system: 0.138 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0746 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0641 s [77% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-07-30 11:44:06,202 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.229 s, system: 0.279 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:44:06,202 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.846 s, CPU [user: 0.113 s, system: 0.112 s], Allocated memory: 13.0 MB, transactions: 0, svn: 0.189 s [37% log2 (10x), 22% log (1x), 17% info (5x), 14% getLatestRevision (3x)] (24x), ObjectMaps: 0.054 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:44:06,202 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.508 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.368 s [53% log2 (36x), 15% getLatestRevision (9x), 11% log (1x), 10% testConnection (6x)] (61x)
2025-07-30 11:44:06,491 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.253 s [100% getReadConfiguration (48x)] (48x), svn: 0.0977 s [86% info (18x)] (38x)
2025-07-30 11:44:06,973 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.366 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.286 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:44:07,230 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.133 s, CPU [user: 0.0293 s, system: 0.00965 s], Allocated memory: 10.6 MB
2025-07-30 11:44:07,294 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.306 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0656 s [100% refresh (1x)] (1x), commit: 0.0648 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0193 s [100% objectsToInv (1x)] (1x), DB: 0.0174 s [39% query (1x), 37% update (3x), 15% execute (1x)] (8x)
2025-07-30 11:44:09,534 [main | u:p | u:p] INFO  TXLOGGER - Tx 66165b8b1f03f_0_66165b8b1f03f_0_: finished. Total: 0.193 s, CPU [user: 0.0902 s, system: 0.0108 s], Allocated memory: 7.5 MB, GlobalHandler: 0.0108 s [95% applyTxChanges (1x)] (4x)
2025-07-30 11:44:10,575 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.844 s [90% info (158x)] (170x)
2025-07-30 11:44:10,900 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165b8c55c44_0_66165b8c55c44_0_: finished. Total: 0.317 s, CPU [user: 0.157 s, system: 0.0219 s], Allocated memory: 21.0 MB, resolve: 0.0821 s [62% User (2x), 36% Project (1x)] (5x), Lucene: 0.0389 s [100% search (1x)] (1x), svn: 0.0293 s [45% getLatestRevision (3x), 19% testConnection (1x), 18% log (1x)] (9x), ObjectMaps: 0.0243 s [43% getPrimaryObjectProperty (2x), 40% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 11:44:11,426 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.00624 s, system: 0.0014 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 11:44:11,426 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.327 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.103 s [68% User (3x), 29% Project (1x)] (7x), Lucene: 0.0543 s [72% search (1x), 18% add (1x)] (3x), ObjectMaps: 0.0308 s [53% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), svn: 0.0293 s [45% getLatestRevision (3x), 19% testConnection (1x), 18% log (1x)] (9x), Incremental Baseline: 0.0237 s [100% WorkItem (22x)] (22x), persistence listener: 0.0214 s [78% indexRefreshPersistenceListener (1x), 15% WorkItemActivityCreator (1x)] (7x)
2025-07-30 11:44:11,427 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.852 s, CPU [user: 0.163 s, system: 0.0246 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.65 s [97% getDatedRevision (181x)] (183x), Lucene: 0.0535 s [85% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:44:11,880 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b8c56045_0_66165b8c56045_0_: finished. Total: 1.3 s, CPU [user: 0.337 s, system: 0.0849 s], Allocated memory: 47.1 MB, svn: 0.813 s [53% getDatedRevision (181x), 34% getDir2 content (25x)] (307x), resolve: 0.33 s [100% Category (96x)] (96x), ObjectMaps: 0.12 s [44% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 11:44:12,168 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b8db3848_0_66165b8db3848_0_: finished. Total: 0.185 s, CPU [user: 0.0816 s, system: 0.0133 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0922 s [56% getReadUserConfiguration (10x), 44% getReadConfiguration (162x)] (172x), svn: 0.0744 s [47% info (19x), 45% getFile content (15x)] (36x), resolve: 0.0372 s [100% User (9x)] (9x), ObjectMaps: 0.0166 s [62% getPrimaryObjectProperty (8x), 20% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:44:12,669 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b8df244a_0_66165b8df244a_0_: finished. Total: 0.435 s, CPU [user: 0.127 s, system: 0.0157 s], Allocated memory: 19.9 MB, svn: 0.275 s [68% getDir2 content (17x), 32% getFile content (44x)] (62x), RepositoryConfigService: 0.21 s [99% getReadConfiguration (170x)] (192x), GC: 0.058 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:44:14,482 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b8e5f44b_0_66165b8e5f44b_0_: finished. Total: 1.81 s, CPU [user: 0.694 s, system: 0.117 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.31 s [97% getReadConfiguration (8682x)] (9021x), svn: 1.02 s [59% getFile content (412x), 41% getDir2 content (21x)] (434x)
2025-07-30 11:44:14,675 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b902484c_0_66165b902484c_0_: finished. Total: 0.193 s, CPU [user: 0.0503 s, system: 0.00588 s], Allocated memory: 17.8 MB, svn: 0.158 s [70% getDir2 content (18x), 30% getFile content (29x)] (48x), RepositoryConfigService: 0.0753 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 11:44:14,794 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b905504d_0_66165b905504d_0_: finished. Total: 0.118 s, CPU [user: 0.0296 s, system: 0.00398 s], Allocated memory: 5.3 MB, svn: 0.105 s [88% getDir2 content (11x)] (20x), RepositoryConfigService: 0.0221 s [97% getReadConfiguration (38x)] (54x)
2025-07-30 11:44:15,544 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165b907284e_0_66165b907284e_0_: finished. Total: 0.75 s, CPU [user: 0.264 s, system: 0.0251 s], Allocated memory: 384.3 MB, svn: 0.503 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x), RepositoryConfigService: 0.492 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 11:44:15,545 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.96 s, CPU [user: 1.66 s, system: 0.278 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.01 s [44% getDir2 content (115x), 40% getFile content (807x)] (1141x), RepositoryConfigService: 2.26 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.414 s [80% Category (96x), 11% Project (6x)] (117x)
2025-07-30 11:44:15,545 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.67 s [36% getDir2 content (115x), 33% getFile content (807x), 29% getDatedRevision (362x)] (1325x), RepositoryConfigService: 2.26 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.414 s [80% Category (96x), 11% Project (6x)] (118x)
2025-07-30 11:44:28,315 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.181201171875
2025-07-30 11:44:28,476 [ajp-nio-127.0.0.1-8889-exec-2 | cID:596e752b-7f000001-1cad68ca-920ccac7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.528 s, CPU [user: 0.249 s, system: 0.0873 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.468 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0279 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:44:28,825 [ajp-nio-127.0.0.1-8889-exec-7 | cID:596e7832-7f000001-1cad68ca-48ada8aa] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.103 s, CPU [user: 0.0159 s, system: 0.00442 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 11:44:28,825 [ajp-nio-127.0.0.1-8889-exec-6 | cID:596e7819-7f000001-1cad68ca-f1340ff8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.127 s, CPU [user: 0.0646 s, system: 0.0138 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 11:44:28,846 [ajp-nio-127.0.0.1-8889-exec-8 | cID:596e7832-7f000001-1cad68ca-a4013088] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753847068661': Total: 0.123 s, CPU [user: 0.0347 s, system: 0.00617 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:44:28,884 [ajp-nio-127.0.0.1-8889-exec-10 | cID:596e7835-7f000001-1cad68ca-7389bf53] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847068663': Total: 0.159 s, CPU [user: 0.0553 s, system: 0.011 s], Allocated memory: 7.0 MB, transactions: 1, RepositoryConfigService: 0.0701 s [100% getReadConfiguration (1x)] (1x), svn: 0.0121 s [53% testConnection (1x), 46% getFile content (2x)] (4x)
2025-07-30 11:44:28,912 [ajp-nio-127.0.0.1-8889-exec-9 | cID:596e7834-7f000001-1cad68ca-0b5fb80f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847068662': Total: 0.185 s, CPU [user: 0.036 s, system: 0.00544 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.0869 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:44:38,311 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.0517578125
