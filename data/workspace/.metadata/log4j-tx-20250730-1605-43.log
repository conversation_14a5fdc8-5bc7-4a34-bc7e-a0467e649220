2025-07-30 16:05:50,243 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, svn: 0.111 s [76% testConnection (1x), 19% getLatestRevision (2x)] (4x), DB: 0.0769 s [75% update (144x), 25% query (12x)] (221x)
2025-07-30 16:05:50,620 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0573 s [71% getDir2 content (2x), 24% info (3x)] (6x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.992 s, CPU [user: 0.286 s, system: 0.265 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.13 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.991 s, CPU [user: 0.0938 s, system: 0.084 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.139 s [78% log2 (10x), 16% getLatestRevision (2x)] (13x), ObjectMaps: 0.0532 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.992 s, CPU [user: 0.101 s, system: 0.107 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.0959 s [78% log2 (10x), 11% testConnection (1x)] (13x), ObjectMaps: 0.0908 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.991 s, CPU [user: 0.0597 s, system: 0.0596 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0597 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0562 s [70% log2 (5x), 16% getLatestRevision (1x)] (7x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.992 s, CPU [user: 0.154 s, system: 0.175 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0998 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0513 s [73% log2 (5x), 15% getLatestRevision (1x)] (7x)
2025-07-30 16:05:51,620 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.992 s, CPU [user: 0.345 s, system: 0.319 s], Allocated memory: 72.4 MB, transactions: 0, svn: 0.153 s [39% info (5x), 18% log (1x), 16% log2 (5x), 10% testConnection (1x)] (18x), ObjectMaps: 0.129 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 16:05:51,621 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.562 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.514 s [57% log2 (36x), 14% getLatestRevision (9x), 12% info (5x)] (61x)
2025-07-30 16:05:51,759 [main | u:p] INFO  TXLOGGER - Tx 6616977167801_0_6616977167801_0_: finished. Total: 0.109 s, CPU [user: 0.0825 s, system: 0.00677 s], Allocated memory: 21.8 MB
2025-07-30 16:05:51,942 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.278 s [100% getReadConfiguration (48x)] (48x), svn: 0.104 s [84% info (18x)] (38x)
2025-07-30 16:05:52,405 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.378 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.272 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 16:05:52,634 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.1 s, CPU [user: 0.0365 s, system: 0.00688 s], Allocated memory: 3.2 MB
2025-07-30 16:05:52,639 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.105 s, CPU [user: 0.0256 s, system: 0.00525 s], Allocated memory: 2.2 MB
2025-07-30 16:05:52,642 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.108 s, CPU [user: 0.00519 s, system: 0.00196 s], Allocated memory: 839.4 kB
2025-07-30 16:05:52,713 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.178 s, CPU [user: 0.0474 s, system: 0.0116 s], Allocated memory: 11.3 MB
2025-07-30 16:05:52,734 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.1 s, CPU [user: 0.0213 s, system: 0.00679 s], Allocated memory: 12.7 MB
2025-07-30 16:05:52,837 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Plan]: finished. Total: 0.148 s, CPU [user: 0.00898 s, system: 0.00442 s], Allocated memory: 6.0 MB
2025-07-30 16:05:52,845 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [ModuleAttachment]: finished. Total: 0.118 s, CPU [user: 0.0104 s, system: 0.00451 s], Allocated memory: 3.6 MB
2025-07-30 16:05:52,862 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.201 s, CPU [user: 0.0434 s, system: 0.00955 s], Allocated memory: 10.6 MB, commit: 0.197 s [100% Revision (1x)] (1x)
2025-07-30 16:05:52,878 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [DocumentWorkflowSignature]: finished. Total: 0.193 s, CPU [user: 0.00144 s, system: 0.000423 s], Allocated memory: 80.4 kB
2025-07-30 16:05:52,884 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPage]: finished. Total: 0.144 s, CPU [user: 0.00988 s, system: 0.00551 s], Allocated memory: 4.8 MB
2025-07-30 16:05:52,896 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.467 s [100% doFinishStartup (1x)] (1x), commit: 0.197 s [100% Revision (1x)] (1x), Lucene: 0.0689 s [100% refresh (1x)] (1x)
2025-07-30 16:05:58,788 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.659 s [81% info (158x)] (170x)
2025-07-30 16:05:59,192 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616977868043_0_6616977868043_0_: finished. Total: 0.376 s, CPU [user: 0.159 s, system: 0.0233 s], Allocated memory: 20.3 MB, resolve: 0.0929 s [60% User (2x), 36% Project (1x)] (5x), Lucene: 0.0344 s [100% search (1x)] (1x), ObjectMaps: 0.0282 s [45% getPrimaryObjectLocation (2x), 37% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 16:05:59,859 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.935 s, CPU [user: 0.00683 s, system: 0.0028 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 16:05:59,859 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.4 s [96% RevisionActivityCreator (2x)] (6x), resolve: 0.116 s [63% User (3x), 29% Project (1x)] (7x), persistence listener: 0.0913 s [95% indexRefreshPersistenceListener (1x)] (7x), Lucene: 0.0591 s [58% search (1x), 33% add (1x)] (3x), ObjectMaps: 0.0315 s [51% getPrimaryObjectLocation (3x), 33% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0309 s [100% WorkItem (22x)] (22x), svn: 0.025 s [46% getLatestRevision (3x), 17% testConnection (1x), 16% log (1x), 13% info (1x)] (9x)
2025-07-30 16:05:59,860 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.07 s, CPU [user: 0.185 s, system: 0.0311 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.809 s [97% getDatedRevision (181x)] (183x)
2025-07-30 16:06:00,333 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616977864040_0_6616977864040_0_: finished. Total: 1.53 s, CPU [user: 0.405 s, system: 0.105 s], Allocated memory: 46.6 MB, svn: 0.902 s [49% getDatedRevision (181x), 34% getDir2 content (25x)] (307x), resolve: 0.483 s [100% Category (96x)] (96x), ObjectMaps: 0.171 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 16:06:00,567 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66169779fb048_0_66169779fb048_0_: finished. Total: 0.138 s, CPU [user: 0.0682 s, system: 0.0112 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0687 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0641 s [55% info (19x), 39% getFile content (15x)] (36x), resolve: 0.0395 s [100% User (9x)] (9x), ObjectMaps: 0.0167 s [65% getPrimaryObjectProperty (8x), 19% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 16:06:00,913 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616977a2944a_0_6616977a2944a_0_: finished. Total: 0.289 s, CPU [user: 0.0942 s, system: 0.0101 s], Allocated memory: 19.9 MB, svn: 0.226 s [69% getDir2 content (17x), 30% getFile content (44x)] (62x), RepositoryConfigService: 0.11 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 16:06:02,070 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616977a7444b_0_6616977a7444b_0_: finished. Total: 1.16 s, CPU [user: 0.48 s, system: 0.048 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.856 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.598 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 16:06:02,180 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616977b9584c_0_6616977b9584c_0_: finished. Total: 0.109 s, CPU [user: 0.0215 s, system: 0.00243 s], Allocated memory: 17.8 MB, svn: 0.0968 s [84% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0248 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 16:06:02,518 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616977bba44e_0_6616977bba44e_0_: finished. Total: 0.301 s, CPU [user: 0.126 s, system: 0.00813 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.199 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.186 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x)
2025-07-30 16:06:02,519 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.72 s, CPU [user: 1.27 s, system: 0.197 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.17 s [41% getDir2 content (115x), 36% getFile content (807x), 20% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.31 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.564 s [86% Category (96x)] (117x), ObjectMaps: 0.203 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 16:06:02,519 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.98 s [41% getDatedRevision (362x), 30% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.31 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.564 s [86% Category (96x)] (118x), ObjectMaps: 0.203 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 16:06:08,472 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a5e0507-7f000001-6d80091d-4efd57b8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.591 s, CPU [user: 0.28 s, system: 0.0837 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.519 s [100% authenticate (1x)] (1x), GC: 0.052 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 16:06:08,835 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a5e07d8-7f000001-6d80091d-cfebf7d9] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.228 s, CPU [user: 0.023 s, system: 0.00815 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0779 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0464 s [100% readUserData (1x)] (1x), PolarionAuthenticator: 0.0214 s [100% authenticate (1x)] (1x)
2025-07-30 16:06:09,281 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a5e095b-7f000001-6d80091d-b70f81e5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.293 s, CPU [user: 0.0938 s, system: 0.0163 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 16:06:09,283 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a5e09b3-7f000001-6d80091d-d0413854] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.206 s, CPU [user: 0.0233 s, system: 0.00679 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 16:06:09,348 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a5e09cc-7f000001-6d80091d-92f92069] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753862768917': Total: 0.247 s, CPU [user: 0.0534 s, system: 0.00924 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 16:06:09,421 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a5e09d1-7f000001-6d80091d-fd38e3fd | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753862768919] INFO  TXLOGGER - Tx 6616978296451_0_6616978296451_0_: finished. Total: 0.18 s, CPU [user: 0.0664 s, system: 0.0135 s], Allocated memory: 5.6 MB, svn: 0.0267 s [62% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 16:06:09,450 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a5e09d1-7f000001-6d80091d-fd38e3fd] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753862768919': Total: 0.344 s, CPU [user: 0.0896 s, system: 0.0194 s], Allocated memory: 7.6 MB, transactions: 1, RepositoryConfigService: 0.181 s [100% getReadConfiguration (1x)] (1x), svn: 0.0267 s [62% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 16:06:09,534 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a5e09ca-7f000001-6d80091d-de84fac8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753862768918': Total: 0.436 s, CPU [user: 0.0713 s, system: 0.011 s], Allocated memory: 5.2 MB, transactions: 1, RepositoryConfigService: 0.235 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 16:12:44,519 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.151 s, CPU [user: 0.00309 s, system: 0.00752 s], Allocated memory: 130.7 kB, transactions: 0, PullingJob: 0.143 s [100% collectChanges (1x)] (1x), svn: 0.142 s [100% getLatestRevision (1x)] (1x)
