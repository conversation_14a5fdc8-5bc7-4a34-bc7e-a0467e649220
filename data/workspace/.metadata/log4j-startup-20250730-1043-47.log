2025-07-30 10:43:47,121 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:43:47,121 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:43:47,121 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:43:47,121 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:43:47,122 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:43:47,122 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:47,122 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:43:52,805 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:43:52,970 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.164 s. ]
2025-07-30 10:43:52,970 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:43:53,080 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.11 s. ]
2025-07-30 10:43:53,198 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:43:53,354 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 95 s. ]
2025-07-30 10:43:53,580 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.46 s. ]
2025-07-30 10:43:53,735 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:53,735 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:43:53,762 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.18 s. ]
2025-07-30 10:43:53,763 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:53,763 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 10:43:53,768 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 10:43:53,784 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:43:53,959 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:43:54,068 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:43:54,650 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.89 s. ]
2025-07-30 10:43:54,665 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:54,665 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:43:55,036 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:43:55,056 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.41 s. ]
2025-07-30 10:43:55,103 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:55,103 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:43:55,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:43:55,175 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:43:55,243 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:43:55,272 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:43:55,302 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:43:55,366 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:43:55,439 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:43:55,475 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:43:55,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:43:55,526 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.47 s. ]
2025-07-30 10:43:55,526 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:55,526 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:43:55,546 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:43:55,546 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:55,546 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:43:55,673 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:43:55,676 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:43:55,802 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 10:43:55,803 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:55,803 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:43:55,811 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:43:55,811 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:43:55,811 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:43:58,728 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.92 s. ]
2025-07-30 10:43:58,729 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:43:58,729 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.6 s. ]
2025-07-30 10:43:58,729 [main] INFO  com.polarion.platform.startup - ****************************************************************
