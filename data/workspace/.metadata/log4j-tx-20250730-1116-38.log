2025-07-30 11:16:43,408 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0475 s [55% update (144x), 45% query (12x)] (221x), svn: 0.0106 s [50% getLatestRevision (2x), 38% testConnection (1x)] (4x)
2025-07-30 11:16:43,517 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0322 s [51% getDir2 content (2x), 41% info (3x)] (6x)
2025-07-30 11:16:44,261 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.0457 s, system: 0.0812 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0644 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:16:44,261 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.0724 s, system: 0.116 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0981 s [82% log2 (10x)] (13x), ObjectMaps: 0.0559 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:16:44,261 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.181 s, system: 0.266 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:16:44,261 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.237 s, system: 0.335 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.147 s [100% getAllPrimaryObjects (1x)] (12x), svn: 0.0443 s [75% log2 (5x), 14% getLatestRevision (1x)] (7x)
2025-07-30 11:16:44,261 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.0738 s, system: 0.131 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0838 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0714 s [68% log2 (10x), 16% testConnection (1x)] (13x)
2025-07-30 11:16:44,262 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.126 s, system: 0.2 s], Allocated memory: 26.1 MB, transactions: 0, ObjectMaps: 0.0789 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.072 s [26% info (5x), 25% log2 (5x), 23% log (1x), 10% testConnection (1x)] (18x)
2025-07-30 11:16:44,262 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.553 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.338 s [61% log2 (36x), 14% testConnection (6x), 13% getLatestRevision (9x)] (61x)
2025-07-30 11:16:44,482 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.187 s [100% getReadConfiguration (48x)] (48x), svn: 0.0723 s [83% info (18x)] (38x)
2025-07-30 11:16:44,760 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.212 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.164 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:16:45,001 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.228 s [100% doFinishStartup (1x)] (1x), commit: 0.063 s [100% Revision (1x)] (1x), Lucene: 0.0338 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0129 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:17:04,550 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.67 s [82% info (158x)] (175x), PullingJob: 0.0651 s [100% collectChanges (6x)] (6x)
2025-07-30 11:17:04,568 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, persistence listener: 0.0414 s [56% indexRefreshPersistenceListener (1x), 23% PlanActivityCreator (1x), 12% WorkItemActivityCreator (1x)] (7x)
2025-07-30 11:17:04,980 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Tx command #679: finished. Total: 0.193 s, CPU [user: 0.00588 s, system: 0.00445 s], Allocated memory: 201.4 kB, GC: 0.152 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:17:05,282 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661655586f444_0_661655586f444_0_: finished. Total: 0.709 s, CPU [user: 0.184 s, system: 0.0293 s], Allocated memory: 22.3 MB, GC: 0.152 s [100% G1 Young Generation (1x)] (1x), resolve: 0.111 s [70% User (2x), 25% Project (1x)] (5x), Lucene: 0.0875 s [100% search (1x)] (1x)
2025-07-30 11:17:05,906 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.12 s, CPU [user: 0.00761 s, system: 0.00537 s], Allocated memory: 371.0 kB, transactions: 1, GC: 0.152 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:17:05,906 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.73 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.149 s [73% User (3x), 19% Project (1x)] (7x), Lucene: 0.114 s [77% search (1x), 15% add (1x)] (3x)
2025-07-30 11:17:05,907 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.36 s, CPU [user: 0.179 s, system: 0.0295 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.796 s [98% getDatedRevision (181x)] (183x), GC: 0.152 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.103 s [83% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:17:06,434 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661655586f845_0_661655586f845_0_: finished. Total: 1.86 s, CPU [user: 0.391 s, system: 0.1 s], Allocated memory: 46.6 MB, svn: 1.21 s [47% getDir2 content (25x), 41% getDatedRevision (181x)] (307x), resolve: 0.45 s [100% Category (96x)] (96x), ObjectMaps: 0.162 s [44% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x)] (387x), GC: 0.152 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:17:06,685 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616555a5d448_0_6616555a5d448_0_: finished. Total: 0.136 s, CPU [user: 0.0642 s, system: 0.012 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0629 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0598 s [59% info (19x), 32% getFile content (15x)] (36x), resolve: 0.0386 s [100% User (9x)] (9x), ObjectMaps: 0.0172 s [64% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:17:06,901 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616555a8d84a_0_6616555a8d84a_0_: finished. Total: 0.159 s, CPU [user: 0.0449 s, system: 0.00516 s], Allocated memory: 19.9 MB, svn: 0.128 s [82% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0388 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:17:07,716 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616555ab544b_0_6616555ab544b_0_: finished. Total: 0.814 s, CPU [user: 0.364 s, system: 0.0287 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.619 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.428 s [67% getFile content (412x), 33% getDir2 content (21x)] (434x), GC: 0.045 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 11:17:07,824 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616555b8104c_0_6616555b8104c_0_: finished. Total: 0.108 s, CPU [user: 0.0196 s, system: 0.00225 s], Allocated memory: 17.8 MB, svn: 0.0993 s [88% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0182 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 11:17:08,157 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616555ba544e_0_6616555ba544e_0_: finished. Total: 0.295 s, CPU [user: 0.128 s, system: 0.0081 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.199 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.18 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 11:17:08,157 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.59 s, CPU [user: 1.09 s, system: 0.17 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.21 s [47% getDir2 content (115x), 28% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.998 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.54 s [83% Category (96x)] (117x), GC: 0.201 s [100% G1 Young Generation (6x)] (6x), ObjectMaps: 0.194 s [46% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 11:17:08,157 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3 s [43% getDatedRevision (362x), 34% getDir2 content (115x), 20% getFile content (807x)] (1325x), RepositoryConfigService: 0.998 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.54 s [83% Category (96x)] (118x), ObjectMaps: 0.194 s [46% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x)] (442x), Lucene: 0.151 s [56% buildBaselineSnapshots (2x), 29% search (5x)] (54x)
2025-07-30 11:17:12,970 [ajp-nio-127.0.0.1-8889-exec-2 | cID:595580b3-0a465820-1d182374-2ce66532] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.47 s, CPU [user: 0.23 s, system: 0.0904 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.396 s [100% authenticate (1x)] (1x)
2025-07-30 11:17:13,384 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5955839c-0a465820-1d182374-e492a4f9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.139 s, CPU [user: 0.0149 s, system: 0.00446 s], Allocated memory: 1.5 MB, transactions: 0
2025-07-30 11:17:13,384 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59558376-0a465820-1d182374-fa1a88f9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.178 s, CPU [user: 0.087 s, system: 0.0233 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:17:13,407 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5955839e-0a465820-1d182374-73a14b27] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753845433171': Total: 0.16 s, CPU [user: 0.0362 s, system: 0.00711 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:17:13,473 [ajp-nio-127.0.0.1-8889-exec-7 | cID:595583a0-0a465820-1d182374-e0c8560c | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753845433173] INFO  TXLOGGER - Tx 6616556106051_0_6616556106051_0_: finished. Total: 0.105 s, CPU [user: 0.0468 s, system: 0.0138 s], Allocated memory: 8.1 MB, svn: 0.015 s [51% testConnection (1x), 49% getFile content (2x)] (4x)
2025-07-30 11:17:13,474 [ajp-nio-127.0.0.1-8889-exec-7 | cID:595583a0-0a465820-1d182374-e0c8560c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753845433173': Total: 0.226 s, CPU [user: 0.0652 s, system: 0.0186 s], Allocated memory: 10.0 MB, transactions: 1, RepositoryConfigService: 0.105 s [100% getReadConfiguration (1x)] (1x), svn: 0.015 s [51% testConnection (1x), 49% getFile content (2x)] (4x)
2025-07-30 11:17:13,502 [ajp-nio-127.0.0.1-8889-exec-8 | cID:595583a0-0a465820-1d182374-9edc45d2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753845433172': Total: 0.253 s, CPU [user: 0.0283 s, system: 0.00509 s], Allocated memory: 4.4 MB, transactions: 1, RepositoryConfigService: 0.127 s [100% getReadConfiguration (1x)] (1x)
