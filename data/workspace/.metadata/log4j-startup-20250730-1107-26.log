2025-07-30 11:07:27,016 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:07:27,016 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:07:27,016 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:07:27,017 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:07:27,017 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:07:27,017 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:27,017 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:07:31,406 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:07:31,551 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.145 s. ]
2025-07-30 11:07:31,551 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:07:31,595 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0439 s. ]
2025-07-30 11:07:31,650 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:07:31,763 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 74 s. ]
2025-07-30 11:07:31,971 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.96 s. ]
2025-07-30 11:07:32,071 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:32,071 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:07:32,100 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 11:07:32,100 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:32,100 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:07:32,105 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 11:07:32,106 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-30 11:07:32,106 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 11:07:32,106 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:07:32,106 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:07:32,106 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 11:07:32,114 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:07:32,287 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:07:32,395 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:07:32,868 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 11:07:32,883 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:32,883 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:07:33,131 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:07:33,141 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-07-30 11:07:33,172 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:33,172 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:07:33,176 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:07:33,234 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:07:33,282 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:07:33,306 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:07:33,332 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:07:33,370 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:07:33,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:07:33,439 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:07:33,478 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:07:33,478 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 11:07:33,479 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:33,479 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:07:33,494 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:07:33,495 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:33,495 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:07:33,635 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:07:33,640 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:07:33,876 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.38 s. ]
2025-07-30 11:07:33,877 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:33,877 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:07:33,888 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:07:33,888 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:07:33,888 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:07:37,887 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.0 s. ]
2025-07-30 11:07:37,887 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:07:37,887 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.9 s. ]
2025-07-30 11:07:37,887 [main] INFO  com.polarion.platform.startup - ****************************************************************
