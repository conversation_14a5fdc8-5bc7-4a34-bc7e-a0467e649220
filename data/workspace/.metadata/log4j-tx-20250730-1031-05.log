2025-07-30 10:31:11,081 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0517 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0128 s [52% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-30 10:31:11,206 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0381 s [57% getDir2 content (2x), 37% info (3x)] (6x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.12 s, system: 0.181 s], Allocated memory: 24.3 MB, transactions: 0, ObjectMaps: 0.063 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.0513 s, system: 0.08 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0607 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0396 s [80% log2 (5x)] (7x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.082 s, system: 0.121 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.0717 s [77% log2 (10x), 12% getLatestRevision (2x)] (13x), ObjectMaps: 0.0669 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.2 s, system: 0.248 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.099 s, system: 0.0933 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.153 s [50% log2 (10x), 17% getLatestRevision (3x), 14% info (5x)] (24x), ObjectMaps: 0.0546 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:31:11,911 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.7 s, CPU [user: 0.237 s, system: 0.308 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.127 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:31:11,912 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.489 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.342 s [63% log2 (36x), 15% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 10:31:12,165 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.214 s [100% getReadConfiguration (48x)] (48x), svn: 0.0757 s [86% info (18x)] (38x)
2025-07-30 10:31:12,457 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.222 s [72% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.169 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:31:12,688 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0287 s, system: 0.00797 s], Allocated memory: 10.6 MB
2025-07-30 10:31:12,724 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.251 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0464 s [100% refresh (1x)] (1x), commit: 0.0462 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0178 s [100% objectsToInv (1x)] (1x)
2025-07-30 10:31:16,350 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.587 s [90% info (158x)] (170x)
2025-07-30 10:31:16,639 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Tx command #668: finished. Total: 0.106 s, CPU [user: 0.00705 s, system: 0.00257 s], Allocated memory: 416.7 kB, Lucene: 0.0905 s [100% refresh (1x)] (1x)
2025-07-30 10:31:18,085 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.56 s, CPU [user: 0.00997 s, system: 0.00395 s], Allocated memory: 585.8 kB, transactions: 1, Lucene: 0.0905 s [100% refresh (1x)] (1x)
2025-07-30 10:31:18,086 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, Lucene: 0.123 s [73% refresh (1x), 27% add (1x)] (2x), Incremental Baseline: 0.103 s [100% WorkItem (21x)] (21x), notification worker: 0.0548 s [69% RevisionActivityCreator (2x), 12% BuildActivityCreator (1x)] (6x), resolve: 0.0542 s [51% User (1x), 49% Revision (2x)] (3x), Full Baseline: 0.0424 s [100% WorkItem (1x)] (1x), persistence listener: 0.0326 s [63% indexRefreshPersistenceListener (1x), 19% WorkItemActivityCreator (1x)] (7x), ObjectMaps: 0.00856 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-30 10:31:18,087 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.76 s, CPU [user: 0.22 s, system: 0.0371 s], Allocated memory: 18.3 MB, transactions: 22, svn: 1.24 s [99% getDatedRevision (181x)] (183x), Lucene: 0.166 s [60% buildBaselineSnapshots (1x), 40% buildBaseline (22x)] (23x)
2025-07-30 10:31:18,584 [ajp-nio-127.0.0.1-8889-exec-2 | cID:592b782a-0a465820-0f8fd472-55108bec] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.764 s, CPU [user: 0.345 s, system: 0.05 s], Allocated memory: 37.4 MB, transactions: 2, PolarionAuthenticator: 0.672 s [100% authenticate (1x)] (1x)
2025-07-30 10:31:19,060 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164adca3840_0_66164adca3840_0_: finished. Total: 2.69 s, CPU [user: 0.591 s, system: 0.142 s], Allocated memory: 57.6 MB, svn: 1.64 s [53% getDatedRevision (181x), 33% getDir2 content (25x)] (307x), resolve: 0.826 s [100% Category (96x)] (96x), ObjectMaps: 0.312 s [46% getPrimaryObjectLocation (96x), 32% getPrimaryObjectProperty (96x), 21% getLastPromoted (96x)] (388x)
2025-07-30 10:31:19,135 [ajp-nio-127.0.0.1-8889-exec-4 | cID:592b7c7d-0a465820-0f8fd472-0f47e1df] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.226 s, CPU [user: 0.0889 s, system: 0.0122 s], Allocated memory: 9.3 MB, transactions: 0
2025-07-30 10:31:19,172 [ajp-nio-127.0.0.1-8889-exec-6 | cID:592b7cde-0a465820-0f8fd472-0256afd7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753842678839': Total: 0.165 s, CPU [user: 0.0466 s, system: 0.00493 s], Allocated memory: 1.8 MB, transactions: 0
2025-07-30 10:31:19,179 [ajp-nio-127.0.0.1-8889-exec-5 | cID:592b7cd6-0a465820-0f8fd472-cebd6a39] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.178 s, CPU [user: 0.0236 s, system: 0.0038 s], Allocated memory: 2.2 MB, transactions: 0, resolve: 0.0774 s [100% Project (1x)] (1x), svn: 0.0272 s [37% log (1x), 26% getLatestRevision (1x), 19% testConnection (1x)] (5x), ObjectMaps: 0.0213 s [70% getPrimaryObjectProperty (1x), 18% getPrimaryObjectLocation (1x)] (4x)
2025-07-30 10:31:19,272 [ajp-nio-127.0.0.1-8889-exec-8 | cID:592b7ce0-0a465820-0f8fd472-40431500] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753842678841': Total: 0.261 s, CPU [user: 0.068 s, system: 0.0114 s], Allocated memory: 8.0 MB, transactions: 1, RepositoryConfigService: 0.0933 s [100% getReadConfiguration (1x)] (1x), resolve: 0.0768 s [100% Project (1x)] (1x), svn: 0.0386 s [30% getLatestRevision (1x), 27% log (1x), 19% getFile content (2x), 12% testConnection (1x)] (8x), ObjectMaps: 0.0148 s [93% getPrimaryObjectProperty (1x)] (4x)
2025-07-30 10:31:19,326 [ajp-nio-127.0.0.1-8889-exec-7 | cID:592b7cde-0a465820-0f8fd472-f92a6764] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753842678840': Total: 0.31 s, CPU [user: 0.0557 s, system: 0.00722 s], Allocated memory: 5.3 MB, transactions: 1, RepositoryConfigService: 0.142 s [100% getReadConfiguration (1x)] (1x), resolve: 0.0752 s [100% Project (1x)] (1x), svn: 0.0393 s [33% log (1x), 25% testConnection (1x), 23% info (2x)] (7x), ObjectMaps: 0.0222 s [65% getPrimaryObjectProperty (1x), 32% getPrimaryObjectLocation (1x)] (4x)
2025-07-30 10:31:19,472 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164adf7304b_0_66164adf7304b_0_: finished. Total: 0.227 s, CPU [user: 0.0786 s, system: 0.0157 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.133 s [62% getReadConfiguration (162x), 38% getReadUserConfiguration (10x)] (172x), svn: 0.0974 s [51% info (19x), 39% getFile content (15x)] (36x), resolve: 0.0463 s [100% User (9x)] (9x), ObjectMaps: 0.0226 s [65% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (33x)
2025-07-30 10:31:19,618 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164adfac04d_0_66164adfac04d_0_: finished. Total: 0.146 s, CPU [user: 0.0502 s, system: 0.00681 s], Allocated memory: 5.4 MB, svn: 0.0932 s [81% info (29x)] (42x), RepositoryConfigService: 0.0578 s [68% getReadConfiguration (54x), 32% getExistingPrefixes (9x)] (77x)
2025-07-30 10:31:19,947 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164adfd0c4e_0_66164adfd0c4e_0_: finished. Total: 0.328 s, CPU [user: 0.0671 s, system: 0.0114 s], Allocated memory: 20.8 MB, svn: 0.244 s [48% getDir2 content (13x), 32% info (35x)] (93x), RepositoryConfigService: 0.138 s [72% getReadConfiguration (170x), 28% getExistingPrefixes (11x)] (192x), GC: 0.032 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 10:31:21,241 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ae022c4f_0_66164ae022c4f_0_: finished. Total: 1.29 s, CPU [user: 0.486 s, system: 0.0471 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.05 s [63% getReadConfiguration (8682x), 37% getExistingPrefixes (129x)] (9021x), svn: 0.82 s [47% info (226x), 35% getFile content (412x)] (656x)
2025-07-30 10:31:21,380 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ae166450_0_66164ae166450_0_: finished. Total: 0.139 s, CPU [user: 0.0287 s, system: 0.00329 s], Allocated memory: 18.9 MB, svn: 0.127 s [44% getDir2 content (14x), 42% info (37x)] (81x), RepositoryConfigService: 0.0449 s [56% getReadConfiguration (124x), 44% getExistingPrefixes (12x)] (148x)
2025-07-30 10:31:21,907 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ae196c52_0_66164ae196c52_0_: finished. Total: 0.471 s, CPU [user: 0.168 s, system: 0.0123 s], Allocated memory: 391.3 MB, RepositoryConfigService: 0.364 s [50% getExistingPrefixes (89x), 50% getReadConfiguration (2787x)] (3025x), svn: 0.343 s [55% info (152x), 26% getFile content (185x)] (354x)
2025-07-30 10:31:21,907 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.54 s, CPU [user: 1.53 s, system: 0.255 s], Allocated memory: 1.7 GB, transactions: 10, svn: 3.49 s [28% getDir2 content (95x), 25% info (539x), 25% getDatedRevision (181x), 20% getFile content (807x)] (1636x), RepositoryConfigService: 1.84 s [61% getReadConfiguration (12019x), 36% getExistingPrefixes (259x)] (12691x), resolve: 0.928 s [89% Category (96x)] (117x), ObjectMaps: 0.354 s [44% getPrimaryObjectLocation (114x), 35% getPrimaryObjectProperty (108x), 21% getLastPromoted (108x)] (444x)
2025-07-30 10:31:21,907 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 58, svn: 4.86 s [43% getDatedRevision (362x), 20% getDir2 content (95x), 18% info (544x)] (1846x), RepositoryConfigService: 2.09 s [66% getReadConfiguration (12022x), 32% getExistingPrefixes (259x)] (12694x), resolve: 1.19 s [69% Category (96x), 24% Project (9x)] (122x), PolarionAuthenticator: 0.681 s [100% authenticate (7x)] (7x), ObjectMaps: 0.421 s [41% getPrimaryObjectLocation (118x), 40% getPrimaryObjectProperty (112x)] (460x), Lucene: 0.249 s [40% buildBaselineSnapshots (2x), 30% search (5x), 30% buildBaseline (44x)] (52x)
2025-07-30 10:31:48,873 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.107 s, CPU [user: 0.00293 s, system: 0.00355 s], Allocated memory: 130.6 kB, transactions: 0, PullingJob: 0.105 s [100% collectChanges (1x)] (1x), svn: 0.104 s [100% getLatestRevision (1x)] (1x)
