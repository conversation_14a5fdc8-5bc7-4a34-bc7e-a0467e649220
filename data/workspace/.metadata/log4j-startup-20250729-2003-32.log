2025-07-29 20:03:32,885 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:32,886 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 20:03:37,036 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 20:03:37,184 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-07-29 20:03:37,184 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 20:03:37,238 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.054 s. ]
2025-07-29 20:03:37,288 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 20:03:37,391 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 81 s. ]
2025-07-29 20:03:37,588 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.71 s. ]
2025-07-29 20:03:37,669 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:37,669 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 20:03:37,696 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 20:03:37,696 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:37,696 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 20:03:37,701 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 20:03:37,707 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 20:03:37,836 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 20:03:37,936 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 20:03:38,383 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-07-29 20:03:38,394 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:38,394 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 20:03:38,632 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 20:03:38,642 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-29 20:03:38,679 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:38,679 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 20:03:38,686 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 20:03:38,737 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 20:03:38,781 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 20:03:38,804 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 20:03:38,825 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 20:03:38,856 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 20:03:38,886 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 20:03:38,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 20:03:38,937 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 20:03:38,937 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-29 20:03:38,937 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:38,937 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 20:03:38,951 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 20:03:38,952 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:38,952 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 20:03:39,044 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 20:03:39,047 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 20:03:39,151 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-07-29 20:03:39,152 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:39,152 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 20:03:39,160 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 20:03:39,160 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:03:39,160 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
