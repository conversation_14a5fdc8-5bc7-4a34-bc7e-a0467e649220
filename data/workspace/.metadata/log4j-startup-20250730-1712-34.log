2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:34,151 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:12:38,427 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:12:38,575 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.148 s. ]
2025-07-30 17:12:38,575 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:12:38,636 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0607 s. ]
2025-07-30 17:12:38,685 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:12:38,819 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:12:39,037 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.89 s. ]
2025-07-30 17:12:39,240 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:39,240 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:12:39,271 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.23 s. ]
2025-07-30 17:12:39,271 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:39,271 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 17:12:39,276 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 17:12:39,285 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:12:39,408 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:12:39,522 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:12:40,015 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 17:12:40,030 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,030 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:12:40,317 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:12:40,332 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 17:12:40,359 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,359 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:12:40,363 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:12:40,424 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:12:40,501 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:12:40,524 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:12:40,573 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:12:40,597 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:12:40,613 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:12:40,638 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:12:40,662 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:12:40,662 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.33 s. ]
2025-07-30 17:12:40,662 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,662 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:12:40,678 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:12:40,678 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,678 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:12:40,790 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:12:40,795 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:12:40,936 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 17:12:40,936 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,936 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:12:40,944 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:12:40,944 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:12:40,944 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:12:43,805 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.86 s. ]
2025-07-30 17:12:43,805 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:12:43,805 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.65 s. ]
2025-07-30 17:12:43,805 [main] INFO  com.polarion.platform.startup - ****************************************************************
