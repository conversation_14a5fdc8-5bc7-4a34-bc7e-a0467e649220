2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:46:43,000 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:43,001 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:46:47,188 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:46:47,330 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-30 16:46:47,330 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:46:47,432 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.102 s. ]
2025-07-30 16:46:47,496 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:46:47,626 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 16:46:47,840 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.84 s. ]
2025-07-30 16:46:47,933 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:47,933 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:46:47,961 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 16:46:47,962 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:47,962 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 16:46:47,966 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 16:46:47,972 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:46:48,141 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:46:48,349 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:46:48,834 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.87 s. ]
2025-07-30 16:46:48,846 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:48,846 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:46:49,083 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:46:49,096 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 16:46:49,126 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:49,126 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:46:49,130 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:46:49,203 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:46:49,267 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:46:49,316 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:46:49,372 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:46:49,395 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:46:49,420 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:46:49,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:46:49,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:46:49,504 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.41 s. ]
2025-07-30 16:46:49,504 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:49,504 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:46:49,518 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 16:46:49,518 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:49,518 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:46:49,628 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:46:49,632 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:46:49,747 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 16:46:49,747 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:49,747 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:46:49,755 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 16:46:49,755 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:46:49,755 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:46:56,764 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 7.01 s. ]
2025-07-30 16:46:56,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:46:56,765 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 13.8 s. ]
2025-07-30 16:46:56,765 [main] INFO  com.polarion.platform.startup - ****************************************************************
