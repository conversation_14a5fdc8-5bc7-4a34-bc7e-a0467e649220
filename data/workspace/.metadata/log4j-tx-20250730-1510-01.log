2025-07-30 15:10:06,588 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0464 s [60% update (144x), 40% query (12x)] (221x), svn: 0.0131 s [58% getLatestRevision (2x), 33% testConnection (1x)] (4x)
2025-07-30 15:10:06,690 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0254 s [60% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 15:10:07,697 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.0965 s, system: 0.0684 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.165 s [89% log2 (10x)] (13x), ObjectMaps: 0.0608 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:10:07,697 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.246 s, system: 0.237 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.14 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:10:07,697 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.148 s, system: 0.16 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.106 s [98% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:10:07,698 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.301 s, system: 0.304 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:10:07,698 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.0733 s, system: 0.0568 s], Allocated memory: 9.4 MB, transactions: 0, ObjectMaps: 0.186 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.164 s [54% log2 (5x), 17% getLatestRevision (2x), 15% info (5x)] (18x)
2025-07-30 15:10:07,698 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.0968 s, system: 0.0858 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.175 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.152 s [70% log2 (10x), 25% getLatestRevision (2x)] (13x)
2025-07-30 15:10:07,700 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.802 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.571 s [70% log2 (36x), 16% getLatestRevision (9x)] (61x)
2025-07-30 15:10:07,836 [main | u:p] INFO  TXLOGGER - Tx 66168aafda401_0_66168aafda401_0_: finished. Total: 0.114 s, CPU [user: 0.0801 s, system: 0.00393 s], Allocated memory: 21.8 MB
2025-07-30 15:10:08,001 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.261 s [100% getReadConfiguration (48x)] (48x), svn: 0.089 s [81% info (18x)] (38x)
2025-07-30 15:10:08,448 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.36 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.267 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:10:08,771 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.138 s, CPU [user: 0.0423 s, system: 0.0116 s], Allocated memory: 11.7 MB, GC: 0.024 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:10:08,845 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.382 s [100% doFinishStartup (1x)] (1x), commit: 0.084 s [100% Revision (1x)] (1x), Lucene: 0.0385 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0241 s [100% objectsToInv (1x)] (1x), DB: 0.0217 s [46% query (1x), 30% update (3x), 14% commit (2x)] (8x)
2025-07-30 15:10:11,365 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.318 s [89% info (158x)] (168x)
2025-07-30 15:10:11,641 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66168ab36d844_0_66168ab36d844_0_: finished. Total: 0.258 s, CPU [user: 0.128 s, system: 0.0136 s], Allocated memory: 20.3 MB, resolve: 0.0892 s [59% User (2x), 38% Project (1x)] (5x), svn: 0.028 s [36% getLatestRevision (2x), 23% log (1x), 16% info (1x), 14% testConnection (1x)] (8x), ObjectMaps: 0.0221 s [54% getPrimaryObjectProperty (2x), 40% getPrimaryObjectLocation (2x)] (11x), Lucene: 0.0202 s [100% search (1x)] (1x), GlobalHandler: 0.0173 s [98% applyTxChanges (1x)] (4x)
2025-07-30 15:10:12,313 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a2acebc-7f000001-058c7300-ffe8b5d1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.665 s, CPU [user: 0.326 s, system: 0.0374 s], Allocated memory: 38.6 MB, transactions: 2, PolarionAuthenticator: 0.582 s [100% authenticate (1x)] (1x)
2025-07-30 15:10:12,424 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.986 s, CPU [user: 0.00661 s, system: 0.00137 s], Allocated memory: 367.1 kB, transactions: 1
2025-07-30 15:10:12,424 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, PolarionAuthenticator: 0.59 s [100% authenticate (2x)] (2x), notification worker: 0.268 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.108 s [65% User (4x), 31% Project (1x)] (8x), Lucene: 0.0485 s [48% add (1x), 42% search (1x)] (3x), svn: 0.0473 s [38% getLatestRevision (3x), 29% testConnection (3x), 14% log (1x)] (13x)
2025-07-30 15:10:12,425 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.06 s, CPU [user: 0.176 s, system: 0.0284 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.878 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0582 s [75% buildBaselineSnapshots (1x), 25% buildBaseline (23x)] (24x)
2025-07-30 15:10:12,794 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5a2ad29a-7f000001-058c7300-4205096d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.159 s, CPU [user: 0.0843 s, system: 0.00718 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 15:10:12,819 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a2ad2dc-7f000001-058c7300-c414ff8d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753859412581': Total: 0.117 s, CPU [user: 0.0328 s, system: 0.00353 s], Allocated memory: 1.7 MB, transactions: 0
2025-07-30 15:10:12,861 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a2ad2dc-7f000001-058c7300-f867c7a4 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753859412582] INFO  TXLOGGER - Tx 66168ab4c6048_0_66168ab4c6048_0_: finished. Total: 0.1 s, CPU [user: 0.0487 s, system: 0.00508 s], Allocated memory: 5.0 MB, svn: 0.0152 s [38% testConnection (1x), 34% info (2x), 27% getFile content (2x)] (6x)
2025-07-30 15:10:12,888 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a2ad2dc-7f000001-058c7300-f867c7a4] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753859412582': Total: 0.188 s, CPU [user: 0.0803 s, system: 0.00901 s], Allocated memory: 8.1 MB, transactions: 1, RepositoryConfigService: 0.101 s [100% getReadConfiguration (1x)] (1x), svn: 0.0152 s [38% testConnection (1x), 34% info (2x), 27% getFile content (2x)] (6x)
2025-07-30 15:10:12,905 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a2ad2dc-7f000001-058c7300-eb70c2c8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753859412583': Total: 0.203 s, CPU [user: 0.0358 s, system: 0.00397 s], Allocated memory: 3.6 MB, transactions: 1, RepositoryConfigService: 0.136 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 15:10:13,164 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab36a040_0_66168ab36a040_0_: finished. Total: 1.79 s, CPU [user: 0.39 s, system: 0.0961 s], Allocated memory: 50.1 MB, svn: 1.09 s [65% getDatedRevision (181x), 20% getDir2 content (25x)] (307x), resolve: 0.484 s [100% Category (96x)] (96x), ObjectMaps: 0.163 s [45% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 15:10:13,341 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab53b04c_0_66168ab53b04c_0_: finished. Total: 0.113 s, CPU [user: 0.0575 s, system: 0.00958 s], Allocated memory: 8.2 MB, RepositoryConfigService: 0.0512 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (162x)] (172x), svn: 0.0468 s [56% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0317 s [100% User (9x)] (9x), ObjectMaps: 0.0154 s [57% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 15:10:13,582 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab56a04e_0_66168ab56a04e_0_: finished. Total: 0.166 s, CPU [user: 0.0492 s, system: 0.00435 s], Allocated memory: 20.8 MB, svn: 0.138 s [54% getDir2 content (13x), 32% info (35x)] (93x), RepositoryConfigService: 0.0467 s [70% getReadConfiguration (170x), 30% getExistingPrefixes (11x)] (192x)
2025-07-30 15:10:14,654 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab59384f_0_66168ab59384f_0_: finished. Total: 1.07 s, CPU [user: 0.439 s, system: 0.0321 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.907 s [63% getReadConfiguration (8682x), 37% getExistingPrefixes (129x)] (9021x), svn: 0.665 s [49% info (226x), 37% getFile content (412x)] (656x)
2025-07-30 15:10:14,754 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab69f850_0_66168ab69f850_0_: finished. Total: 0.101 s, CPU [user: 0.0199 s, system: 0.00219 s], Allocated memory: 18.8 MB, svn: 0.0919 s [51% getDir2 content (14x), 36% info (37x)] (81x), RepositoryConfigService: 0.0272 s [62% getReadConfiguration (124x), 38% getExistingPrefixes (12x)] (148x)
2025-07-30 15:10:15,195 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168ab6c4452_0_66168ab6c4452_0_: finished. Total: 0.393 s, CPU [user: 0.136 s, system: 0.0104 s], Allocated memory: 390.8 MB, svn: 0.293 s [46% info (152x), 28% getDir2 content (17x), 26% getFile content (185x)] (355x), RepositoryConfigService: 0.268 s [55% getReadConfiguration (2787x), 45% getExistingPrefixes (89x)] (3025x)
2025-07-30 15:10:15,195 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.83 s, CPU [user: 1.16 s, system: 0.165 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.44 s [29% getDatedRevision (181x), 26% info (539x), 22% getFile content (807x), 22% getDir2 content (96x)] (1637x), RepositoryConfigService: 1.37 s [61% getReadConfiguration (12019x), 37% getExistingPrefixes (259x)] (12691x), resolve: 0.543 s [89% Category (96x)] (117x)
2025-07-30 15:10:15,195 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 58, svn: 3.34 s [47% getDatedRevision (362x), 19% info (541x), 16% getFile content (809x)] (1827x), RepositoryConfigService: 1.61 s [67% getReadConfiguration (12021x), 31% getExistingPrefixes (259x)] (12693x), resolve: 0.547 s [89% Category (96x)] (121x), ObjectMaps: 0.19 s [46% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 24% getLastPromoted (108x)] (442x)
