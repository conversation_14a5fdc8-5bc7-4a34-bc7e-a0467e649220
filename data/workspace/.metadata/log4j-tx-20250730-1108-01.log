2025-07-30 11:08:07,256 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0757 s [65% update (144x), 34% query (12x)] (221x), svn: 0.0158 s [48% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-30 11:08:07,400 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0381 s [65% getDir2 content (2x), 27% info (3x)] (6x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0499 s, system: 0.111 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.072 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.259 s, system: 0.361 s], Allocated memory: 73.0 MB, transactions: 0, ObjectMaps: 0.139 s [100% getAllPrimaryObjects (1x)] (12x), svn: 0.0835 s [28% info (5x), 23% log2 (5x), 18% log (1x), 13% getLatestRevision (2x)] (18x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0749 s, system: 0.131 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0817 s [79% log2 (10x), 13% getLatestRevision (2x)] (13x), ObjectMaps: 0.065 s [99% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.186 s, system: 0.296 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.112 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.103 s, system: 0.231 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0642 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:08:08,159 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.754 s, CPU [user: 0.0767 s, system: 0.163 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0756 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0607 s [76% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-30 11:08:08,160 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.528 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.305 s [60% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 11:08:08,328 [main | u:p] INFO  TXLOGGER - Tx 6616534c9ec01_0_6616534c9ec01_0_: finished. Total: 0.139 s, CPU [user: 0.102 s, system: 0.00618 s], Allocated memory: 21.8 MB
2025-07-30 11:08:08,492 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.29 s [100% getReadConfiguration (48x)] (48x), svn: 0.0923 s [85% info (18x)] (38x)
2025-07-30 11:08:08,833 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.264 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.198 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:08:09,107 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Attachment]: finished. Total: 0.143 s, CPU [user: 0.00271 s, system: 0.000908 s], Allocated memory: 241.6 kB
2025-07-30 11:08:09,112 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.147 s, CPU [user: 0.0297 s, system: 0.00696 s], Allocated memory: 2.5 MB
2025-07-30 11:08:09,115 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.151 s, CPU [user: 0.023 s, system: 0.00532 s], Allocated memory: 2.0 MB
2025-07-30 11:08:09,117 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.153 s, CPU [user: 0.0048 s, system: 0.0017 s], Allocated memory: 839.9 kB
2025-07-30 11:08:09,155 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.191 s, CPU [user: 0.0578 s, system: 0.0171 s], Allocated memory: 12.0 MB
2025-07-30 11:08:09,223 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.375 s [100% doFinishStartup (1x)] (1x), Lucene: 0.107 s [100% refresh (1x)] (1x), commit: 0.0825 s [100% Revision (1x)] (1x)
2025-07-30 11:08:11,932 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.373 s [89% info (158x)] (168x)
2025-07-30 11:08:12,302 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616535049845_0_6616535049845_0_: finished. Total: 0.36 s, CPU [user: 0.147 s, system: 0.0196 s], Allocated memory: 22.3 MB, resolve: 0.0877 s [62% User (2x), 33% Project (1x)] (5x), Lucene: 0.0648 s [100% search (1x)] (1x), GC: 0.033 s [100% G1 Young Generation (1x)] (1x), svn: 0.0239 s [47% getLatestRevision (3x), 21% testConnection (1x), 16% log (1x)] (9x), ObjectMaps: 0.0236 s [49% getPrimaryObjectLocation (2x), 36% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 11:08:12,826 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.816 s, CPU [user: 0.00561 s, system: 0.00111 s], Allocated memory: 315.7 kB, transactions: 1
2025-07-30 11:08:12,826 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.373 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.108 s [67% User (3x), 26% Project (1x)] (7x), Lucene: 0.0693 s [94% search (1x)] (2x), svn: 0.0345 s [47% getLatestRevision (4x), 30% testConnection (2x), 11% log (1x)] (11x), ObjectMaps: 0.0282 s [58% getPrimaryObjectLocation (3x), 30% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0244 s [100% WorkItem (22x)] (22x)
2025-07-30 11:08:12,827 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.9 s, CPU [user: 0.174 s, system: 0.0265 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.646 s [99% getDatedRevision (181x)] (183x), Lucene: 0.129 s [93% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:08:13,393 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616535048440_0_6616535048440_0_: finished. Total: 1.45 s, CPU [user: 0.375 s, system: 0.0926 s], Allocated memory: 46.6 MB, svn: 0.944 s [57% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.364 s [100% Category (96x)] (96x), ObjectMaps: 0.126 s [45% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 11:08:13,599 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165351ca048_0_66165351ca048_0_: finished. Total: 0.118 s, CPU [user: 0.0586 s, system: 0.00879 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0598 s [57% getReadConfiguration (162x), 43% getReadUserConfiguration (10x)] (172x), svn: 0.0532 s [61% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0311 s [100% User (9x)] (9x), ObjectMaps: 0.015 s [53% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x), 24% getLastPromoted (8x)] (32x)
2025-07-30 11:08:13,802 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165351f044a_0_66165351f044a_0_: finished. Total: 0.169 s, CPU [user: 0.06 s, system: 0.00519 s], Allocated memory: 19.9 MB, svn: 0.127 s [62% getDir2 content (17x), 38% getFile content (44x)] (62x), RepositoryConfigService: 0.0748 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:08:14,582 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661653521a84b_0_661653521a84b_0_: finished. Total: 0.779 s, CPU [user: 0.362 s, system: 0.0182 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.619 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.422 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x)
2025-07-30 11:08:15,226 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165352ff44e_0_66165352ff44e_0_: finished. Total: 0.509 s, CPU [user: 0.175 s, system: 0.0189 s], Allocated memory: 384.9 MB, svn: 0.323 s [54% getDir2 content (21x), 46% getFile content (185x)] (207x), RepositoryConfigService: 0.301 s [95% getReadConfiguration (2787x)] (3025x)
2025-07-30 11:08:15,226 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.29 s, CPU [user: 1.11 s, system: 0.157 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.04 s [37% getDir2 content (115x), 33% getFile content (807x), 26% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.11 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.432 s [84% Category (96x)] (117x)
2025-07-30 11:08:15,227 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.69 s [44% getDatedRevision (362x), 28% getDir2 content (115x), 25% getFile content (807x)] (1324x), RepositoryConfigService: 1.11 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.432 s [84% Category (96x)] (118x), Lucene: 0.183 s [65% buildBaselineSnapshots (2x), 19% search (5x)] (54x), ObjectMaps: 0.155 s [47% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:09:05,368 [ajp-nio-127.0.0.1-8889-exec-2 | cID:594e0fec-0a465820-08f44eb4-875759ad] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.49 s, CPU [user: 0.24 s, system: 0.0869 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.409 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0375 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:09:05,767 [ajp-nio-127.0.0.1-8889-exec-4 | cID:594e12c8-0a465820-08f44eb4-6f087703] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.159 s, CPU [user: 0.0777 s, system: 0.0203 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 11:09:05,767 [ajp-nio-127.0.0.1-8889-exec-5 | cID:594e12ee-0a465820-08f44eb4-7fb98215] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.121 s, CPU [user: 0.0186 s, system: 0.00524 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 11:09:05,798 [ajp-nio-127.0.0.1-8889-exec-6 | cID:594e12f2-0a465820-08f44eb4-55a86bf7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753844945563': Total: 0.147 s, CPU [user: 0.0405 s, system: 0.00812 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 11:09:05,880 [ajp-nio-127.0.0.1-8889-exec-8 | cID:594e12f3-0a465820-08f44eb4-5d92be67 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753844945564] INFO  TXLOGGER - Tx 66165384d4051_0_66165384d4051_0_: finished. Total: 0.136 s, CPU [user: 0.0642 s, system: 0.0159 s], Allocated memory: 7.9 MB, svn: 0.0188 s [57% getFile content (2x), 42% testConnection (1x)] (4x)
2025-07-30 11:09:05,883 [ajp-nio-127.0.0.1-8889-exec-8 | cID:594e12f3-0a465820-08f44eb4-5d92be67] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753844945564': Total: 0.231 s, CPU [user: 0.083 s, system: 0.0207 s], Allocated memory: 9.8 MB, transactions: 1, RepositoryConfigService: 0.137 s [100% getReadConfiguration (1x)] (1x), svn: 0.0188 s [57% getFile content (2x), 42% testConnection (1x)] (4x)
2025-07-30 11:09:05,918 [ajp-nio-127.0.0.1-8889-exec-7 | cID:594e12f3-0a465820-08f44eb4-ea7ccf0c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753844945565': Total: 0.266 s, CPU [user: 0.035 s, system: 0.00615 s], Allocated memory: 4.0 MB, transactions: 1, RepositoryConfigService: 0.173 s [100% getReadConfiguration (1x)] (1x)
