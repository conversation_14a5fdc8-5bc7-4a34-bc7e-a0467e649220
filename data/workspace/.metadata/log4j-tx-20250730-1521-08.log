2025-07-30 15:21:13,848 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0376 s [54% update (144x), 45% query (12x)] (221x), svn: 0.00958 s [49% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-07-30 15:21:13,958 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0324 s [60% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 15:21:14,673 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.119 s, system: 0.206 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0697 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:21:14,674 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.255 s, system: 0.333 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:21:14,673 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.712 s, CPU [user: 0.0782 s, system: 0.113 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0849 s [84% log2 (10x)] (13x), ObjectMaps: 0.0436 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:21:14,674 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.203 s, system: 0.273 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:21:14,674 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.0846 s, system: 0.136 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0776 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0651 s [74% log2 (10x), 18% getLatestRevision (2x)] (13x)
2025-07-30 15:21:14,674 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.713 s, CPU [user: 0.0709 s, system: 0.1 s], Allocated memory: 9.4 MB, transactions: 0, svn: 0.0773 s [32% log2 (5x), 26% info (5x), 19% log (1x), 11% getLatestRevision (2x)] (18x), ObjectMaps: 0.0665 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:21:14,674 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.513 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.295 s [62% log2 (36x), 15% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 15:21:14,906 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.196 s [100% getReadConfiguration (48x)] (48x), svn: 0.0719 s [83% info (18x)] (38x)
2025-07-30 15:21:15,216 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.248 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.185 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 15:21:15,441 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.209 s [100% doFinishStartup (1x)] (1x), commit: 0.0485 s [100% Revision (1x)] (1x), Lucene: 0.0312 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0158 s [100% objectsToInv (1x)] (1x), DB: 0.0128 s [46% update (3x), 29% query (1x), 17% execute (1x)] (8x)
2025-07-30 15:21:17,811 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.33 s [89% info (158x)] (168x)
2025-07-30 15:21:18,150 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66168d3e3f043_0_66168d3e3f043_0_: finished. Total: 0.329 s, CPU [user: 0.137 s, system: 0.0175 s], Allocated memory: 21.5 MB, resolve: 0.132 s [72% User (2x), 28% Project (1x)] (5x), Lucene: 0.0553 s [100% search (1x)] (1x), ObjectMaps: 0.0424 s [64% getPrimaryObjectLocation (2x), 30% getPrimaryObjectProperty (2x)] (11x), svn: 0.0337 s [31% getLatestRevision (2x), 29% testConnection (1x), 16% log (1x), 14% info (1x)] (8x), GC: 0.022 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 15:21:18,727 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.844 s, CPU [user: 0.00625 s, system: 0.00125 s], Allocated memory: 316.3 kB, transactions: 1
2025-07-30 15:21:18,727 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.341 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.147 s [73% User (3x), 25% Project (1x)] (7x), Lucene: 0.0764 s [72% search (1x), 22% add (1x)] (3x), ObjectMaps: 0.0479 s [68% getPrimaryObjectLocation (3x), 26% getPrimaryObjectProperty (2x)] (12x), svn: 0.0399 s [34% getLatestRevision (3x), 32% testConnection (2x), 14% log (1x), 12% info (1x)] (10x), Incremental Baseline: 0.0252 s [100% WorkItem (22x)] (22x)
2025-07-30 15:21:18,728 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.928 s, CPU [user: 0.176 s, system: 0.0255 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.748 s [96% getDatedRevision (181x)] (183x), Lucene: 0.0589 s [81% buildBaselineSnapshots (1x)] (24x)
2025-07-30 15:21:19,153 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d3e3f845_0_66168d3e3f845_0_: finished. Total: 1.33 s, CPU [user: 0.335 s, system: 0.0851 s], Allocated memory: 46.6 MB, svn: 0.805 s [49% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.375 s [100% Category (96x)] (96x), ObjectMaps: 0.134 s [39% getPrimaryObjectProperty (96x), 38% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 15:21:19,398 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d3faa848_0_66168d3faa848_0_: finished. Total: 0.124 s, CPU [user: 0.0604 s, system: 0.00984 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0593 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0533 s [60% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0386 s [100% User (9x)] (9x), ObjectMaps: 0.018 s [54% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x), 22% getLastPromoted (8x)] (32x)
2025-07-30 15:21:19,721 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d3fd784a_0_66168d3fd784a_0_: finished. Total: 0.268 s, CPU [user: 0.069 s, system: 0.00784 s], Allocated memory: 19.9 MB, svn: 0.227 s [81% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0673 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 15:21:20,755 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d401a84b_0_66168d401a84b_0_: finished. Total: 1.03 s, CPU [user: 0.431 s, system: 0.031 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.779 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.529 s [64% getFile content (412x), 36% getDir2 content (21x)] (434x)
2025-07-30 15:21:20,896 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d411cc4c_0_66168d411cc4c_0_: finished. Total: 0.141 s, CPU [user: 0.0285 s, system: 0.00382 s], Allocated memory: 17.9 MB, svn: 0.13 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0246 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 15:21:21,231 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168d414784e_0_66168d414784e_0_: finished. Total: 0.304 s, CPU [user: 0.127 s, system: 0.00794 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.204 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.192 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 15:21:21,231 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.41 s, CPU [user: 1.13 s, system: 0.161 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.03 s [44% getDir2 content (115x), 33% getFile content (807x), 20% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.19 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.478 s [79% Category (96x), 12% Project (6x)] (117x), ObjectMaps: 0.18 s [42% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 15:21:21,231 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.77 s [40% getDatedRevision (362x), 32% getDir2 content (115x), 24% getFile content (807x)] (1324x), RepositoryConfigService: 1.19 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.478 s [79% Category (96x), 12% Project (6x)] (118x), ObjectMaps: 0.18 s [42% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 15:21:30,421 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a352880-7f000001-1fe79413-4b895c5b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.436 s, CPU [user: 0.224 s, system: 0.0683 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.38 s [100% authenticate (1x)] (1x), interceptor: 0.0249 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 15:21:30,825 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a352b46-7f000001-1fe79413-71e27e18] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.13 s, CPU [user: 0.0887 s, system: 0.0128 s], Allocated memory: 10.0 MB, transactions: 0
