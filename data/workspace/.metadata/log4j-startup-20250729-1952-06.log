2025-07-29 19:52:06,246 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:06,247 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:52:10,538 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:52:10,685 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-07-29 19:52:10,685 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:52:10,734 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0483 s. ]
2025-07-29 19:52:10,798 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:52:10,913 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 84 s. ]
2025-07-29 19:52:11,128 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.89 s. ]
2025-07-29 19:52:11,218 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:11,218 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:52:11,249 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-29 19:52:11,249 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:11,249 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 19:52:11,254 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 19:52:11,262 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:52:11,396 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:52:11,517 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:52:11,988 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-29 19:52:12,001 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,001 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:52:12,217 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:52:12,230 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 19:52:12,254 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,254 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:52:12,257 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:52:12,310 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:52:12,360 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:52:12,383 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:52:12,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:52:12,447 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:52:12,485 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:52:12,535 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:52:12,584 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:52:12,584 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-07-29 19:52:12,584 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,585 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:52:12,601 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 19:52:12,601 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,601 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:52:12,710 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:52:12,712 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:52:12,839 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-29 19:52:12,839 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,839 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:52:12,847 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:52:12,847 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:52:12,847 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:52:15,633 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.79 s. ]
2025-07-29 19:52:15,633 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:52:15,634 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.39 s. ]
2025-07-29 19:52:15,634 [main] INFO  com.polarion.platform.startup - ****************************************************************
