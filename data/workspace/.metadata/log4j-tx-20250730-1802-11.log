2025-07-30 18:02:16,463 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.037 s [55% update (144x), 44% query (12x)] (221x), svn: 0.0137 s [68% getLatestRevision (2x), 25% testConnection (1x)] (4x)
2025-07-30 18:02:16,593 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.036 s [64% getDir2 content (2x), 28% info (3x)] (6x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.202 s, system: 0.265 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.106 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.0823 s, system: 0.115 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.095 s [82% log2 (10x)] (13x), ObjectMaps: 0.063 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.124 s, system: 0.2 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.063 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0378 s [69% log2 (5x), 20% testConnection (1x)] (7x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.244 s, system: 0.319 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.0717 s, system: 0.0924 s], Allocated memory: 9.1 MB, transactions: 0, svn: 0.12 s [40% log2 (5x), 21% info (5x), 19% log (1x)] (18x), ObjectMaps: 0.0757 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:02:17,329 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.0876 s, system: 0.135 s], Allocated memory: 12.4 MB, transactions: 0, svn: 0.0884 s [84% log2 (10x)] (13x), ObjectMaps: 0.0819 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:02:17,330 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.508 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.376 s [65% log2 (36x), 12% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 18:02:17,583 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.22 s [100% getReadConfiguration (48x)] (48x), svn: 0.0894 s [86% info (18x)] (38x)
2025-07-30 18:02:17,892 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.238 s [77% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.178 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:02:18,177 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.271 s [100% doFinishStartup (1x)] (1x), commit: 0.0809 s [100% Revision (1x)] (1x), Lucene: 0.04 s [100% refresh (1x)] (1x)
2025-07-30 18:02:36,936 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.51 s [82% info (158x)] (175x), PullingJob: 0.0496 s [100% collectChanges (6x)] (6x)
2025-07-30 18:02:37,407 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616b22a85444_0_6616b22a85444_0_: finished. Total: 0.458 s, CPU [user: 0.154 s, system: 0.0297 s], Allocated memory: 20.3 MB, resolve: 0.165 s [73% User (2x), 25% Project (1x)] (5x), ObjectMaps: 0.0753 s [55% getPrimaryObjectLocation (2x), 24% getPrimaryObjectProperty (2x), 13% getPrimaryObjectLocations (1x)] (11x), Lucene: 0.0355 s [100% search (1x)] (1x), svn: 0.031 s [37% getLatestRevision (2x), 27% testConnection (1x), 17% log (1x)] (8x)
2025-07-30 18:02:37,999 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.963 s, CPU [user: 0.00277 s, system: 0.00143 s], Allocated memory: 204.8 kB, transactions: 1
2025-07-30 18:02:37,999 [DBHistoryCreator-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.963 s, CPU [user: 0.00396 s, system: 0.0019 s], Allocated memory: 244.2 kB, transactions: 1
2025-07-30 18:02:37,999 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.963 s, CPU [user: 0.0034 s, system: 0.00204 s], Allocated memory: 232.4 kB, transactions: 1
2025-07-30 18:02:38,000 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, notification worker: 0.472 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.183 s [73% User (3x), 23% Project (1x)] (7x), ObjectMaps: 0.0783 s [57% getPrimaryObjectLocation (3x), 23% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0678 s [52% search (1x), 27% add (1x), 21% refresh (3x)] (5x), svn: 0.031 s [37% getLatestRevision (2x), 27% testConnection (1x), 17% log (1x)] (8x), Incremental Baseline: 0.0266 s [100% WorkItem (22x)] (22x)
2025-07-30 18:02:38,001 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.07 s, CPU [user: 0.181 s, system: 0.032 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.847 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0714 s [78% buildBaselineSnapshots (1x), 22% buildBaseline (23x)] (24x)
2025-07-30 18:02:38,458 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22a85c45_0_6616b22a85c45_0_: finished. Total: 1.51 s, CPU [user: 0.368 s, system: 0.0973 s], Allocated memory: 47.1 MB, svn: 0.874 s [49% getDatedRevision (181x), 34% getDir2 content (25x)] (307x), resolve: 0.427 s [100% Category (96x)] (96x), ObjectMaps: 0.153 s [41% getPrimaryObjectProperty (96x), 38% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 18:02:38,642 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22c11048_0_6616b22c11048_0_: finished. Total: 0.11 s, CPU [user: 0.0572 s, system: 0.0105 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0485 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), svn: 0.0438 s [58% info (19x), 37% getFile content (15x)] (36x), resolve: 0.0333 s [100% User (9x)] (9x), ObjectMaps: 0.0134 s [55% getPrimaryObjectProperty (8x), 28% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 18:02:38,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22c3744a_0_6616b22c3744a_0_: finished. Total: 0.171 s, CPU [user: 0.061 s, system: 0.00868 s], Allocated memory: 19.9 MB, svn: 0.126 s [66% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.0716 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 18:02:39,600 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22c6244b_0_6616b22c6244b_0_: finished. Total: 0.743 s, CPU [user: 0.348 s, system: 0.0256 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.574 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.387 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-30 18:02:39,718 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22d1c04c_0_6616b22d1c04c_0_: finished. Total: 0.118 s, CPU [user: 0.0228 s, system: 0.00279 s], Allocated memory: 17.8 MB, svn: 0.107 s [86% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0229 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 18:02:40,192 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b22d41c4e_0_6616b22d41c4e_0_: finished. Total: 0.441 s, CPU [user: 0.158 s, system: 0.0125 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.314 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.266 s [64% getFile content (185x), 36% getDir2 content (21x)] (207x)
2025-07-30 18:02:40,193 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.24 s, CPU [user: 1.08 s, system: 0.167 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.88 s [38% getDir2 content (115x), 36% getFile content (807x), 23% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.08 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.491 s [87% Category (96x)] (117x), ObjectMaps: 0.177 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 18:02:40,193 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.73 s [46% getDatedRevision (362x), 27% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.08 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.491 s [87% Category (96x)] (118x), ObjectMaps: 0.177 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 18:04:27,065 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5ac8f6da-7f000001-4adc0631-d8e0db2f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 90.5 s, CPU [user: 0.966 s, system: 0.158 s], Allocated memory: 58.7 MB, transactions: 2
2025-07-30 18:04:52,462 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5acabab5-7f000001-4adc0631-abdc403a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753868189343': Total: 0.184 s, CPU [user: 0.068 s, system: 0.0454 s], Allocated memory: 7.3 MB, transactions: 1, RepositoryConfigService: 0.0924 s [100% getReadConfiguration (1x)] (1x), permissions: 0.0346 s [100% readInstance (1x)] (1x), svn: 0.0199 s [64% testConnection (1x), 36% getFile content (2x)] (4x)
2025-07-30 18:05:25,108 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5acb0d95-7f000001-4adc0631-00b5cec9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 11.6 s, CPU [user: 0.227 s, system: 0.0411 s], Allocated memory: 2.8 MB, transactions: 0
