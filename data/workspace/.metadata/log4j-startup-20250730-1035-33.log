2025-07-30 10:35:33,516 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:35:33,516 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:35:33,516 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:35:33,516 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:35:33,516 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:35:33,517 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:33,517 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:35:38,066 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:35:38,212 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.146 s. ]
2025-07-30 10:35:38,212 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:35:38,261 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0491 s. ]
2025-07-30 10:35:38,315 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:35:38,503 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 112 s. ]
2025-07-30 10:35:38,765 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.25 s. ]
2025-07-30 10:35:38,883 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:38,883 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:35:38,910 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 10:35:38,910 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:38,910 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:35:38,916 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 10:35:38,917 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-30 10:35:38,916 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 10:35:38,916 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 10:35:38,916 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 10:35:38,916 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 10:35:38,930 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:35:39,206 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:35:39,343 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:35:39,838 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.93 s. ]
2025-07-30 10:35:39,853 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:39,853 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:35:40,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:35:40,122 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-30 10:35:40,155 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:40,155 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:35:40,163 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:35:40,218 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:35:40,284 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:35:40,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:35:40,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:35:40,440 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:35:40,508 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:35:40,572 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:35:40,627 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:35:40,627 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.5 s. ]
2025-07-30 10:35:40,627 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:40,627 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:35:40,642 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 10:35:40,642 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:40,642 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:35:40,791 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:35:40,795 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:35:40,960 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-07-30 10:35:40,961 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:40,961 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:35:40,974 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:35:40,975 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:35:40,975 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:35:44,188 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.21 s. ]
2025-07-30 10:35:44,188 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:35:44,188 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.7 s. ]
2025-07-30 10:35:44,188 [main] INFO  com.polarion.platform.startup - ****************************************************************
