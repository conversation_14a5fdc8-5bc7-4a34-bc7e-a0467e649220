2025-07-30 10:25:46,963 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0885 s [52% update (144x), 48% query (12x)] (221x), svn: 0.0171 s [59% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-07-30 10:25:47,153 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0484 s [59% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 10:25:48,164 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.0645 s, system: 0.0834 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0719 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:25:48,164 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.3 s, system: 0.346 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.218 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:25:48,164 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.148 s, system: 0.197 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0863 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 10:25:48,165 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.254 s, system: 0.28 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.137 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:25:48,165 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.101 s, system: 0.125 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0816 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0731 s [75% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-07-30 10:25:48,165 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1 s, CPU [user: 0.135 s, system: 0.114 s], Allocated memory: 13.3 MB, transactions: 0, svn: 0.199 s [51% log2 (10x), 17% info (5x), 15% log (1x)] (24x), ObjectMaps: 0.0545 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:25:48,165 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.65 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.4 s [60% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 10:25:48,394 [main | u:p] INFO  TXLOGGER - Tx 6616499c36c01_0_6616499c36c01_0_: finished. Total: 0.139 s, CPU [user: 0.0938 s, system: 0.00764 s], Allocated memory: 21.8 MB
2025-07-30 10:25:48,582 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.317 s [100% getReadConfiguration (48x)] (48x), svn: 0.11 s [87% info (18x)] (38x)
2025-07-30 10:25:49,228 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.442 s [72% info (94x), 23% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.362 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:25:49,498 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.123 s, CPU [user: 0.0256 s, system: 0.00769 s], Allocated memory: 10.6 MB, GC: 0.019 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 10:25:49,541 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.295 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0442 s [100% refresh (1x)] (1x), DB: 0.0424 s [63% update (3x), 20% query (1x)] (8x), commit: 0.0383 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.023 s [100% objectsToInv (1x)] (1x)
2025-07-30 10:25:52,846 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.596 s [91% info (158x)] (170x)
2025-07-30 10:25:54,080 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.12 s, CPU [user: 0.00785 s, system: 0.00286 s], Allocated memory: 531.2 kB, transactions: 1, GC: 0.116 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 10:25:54,080 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, Lucene: 0.0641 s [62% add (1x), 38% refresh (1x)] (2x), resolve: 0.0593 s [82% User (2x)] (4x), persistence listener: 0.0461 s [79% indexRefreshPersistenceListener (1x), 13% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0258 s [100% WorkItem (21x)] (21x), notification worker: 0.0252 s [82% RevisionActivityCreator (2x)] (6x), ObjectMaps: 0.0126 s [100% getPrimaryObjectLocation (2x)] (2x), Full Baseline: 0.00453 s [100% WorkItem (1x)] (1x)
2025-07-30 10:25:54,081 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.13 s, CPU [user: 0.187 s, system: 0.0324 s], Allocated memory: 18.3 MB, transactions: 22, svn: 0.988 s [98% getDatedRevision (181x)] (183x), GC: 0.116 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0707 s [86% buildBaselineSnapshots (1x)] (23x)
2025-07-30 10:25:54,726 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661649a0b8840_0_661649a0b8840_0_: finished. Total: 1.86 s, CPU [user: 0.518 s, system: 0.117 s], Allocated memory: 54.7 MB, svn: 1.19 s [51% getDatedRevision (181x), 27% getDir2 content (25x), 21% getFile content (98x)] (307x), resolve: 0.647 s [100% Category (96x)] (96x), ObjectMaps: 0.183 s [37% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 30% getLastPromoted (96x)] (388x), GC: 0.116 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 10:25:55,175 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661649a2a2c48_0_661649a2a2c48_0_: finished. Total: 0.347 s, CPU [user: 0.0802 s, system: 0.0163 s], Allocated memory: 8.4 MB, resolve: 0.156 s [100% User (9x)] (9x), svn: 0.138 s [50% info (19x), 46% getFile content (16x)] (37x), RepositoryConfigService: 0.107 s [70% getReadUserConfiguration (10x), 30% getReadConfiguration (162x)] (172x), ObjectMaps: 0.0526 s [44% getPrimaryObjectProperty (9x), 37% getLastPromoted (9x)] (37x)
2025-07-30 10:25:55,497 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661649a30a84a_0_661649a30a84a_0_: finished. Total: 0.255 s, CPU [user: 0.0671 s, system: 0.0088 s], Allocated memory: 19.9 MB, svn: 0.208 s [72% getDir2 content (17x), 28% getFile content (44x)] (62x), RepositoryConfigService: 0.0872 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 10:25:56,412 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661649a34a44b_0_661649a34a44b_0_: finished. Total: 0.914 s, CPU [user: 0.375 s, system: 0.0363 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.714 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.514 s [72% getFile content (412x), 28% getDir2 content (21x)] (434x)
2025-07-30 10:25:56,863 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661649a44fc4e_0_661649a44fc4e_0_: finished. Total: 0.32 s, CPU [user: 0.13 s, system: 0.00883 s], Allocated memory: 384.7 MB, RepositoryConfigService: 0.22 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.203 s [61% getFile content (185x), 39% getDir2 content (20x)] (206x)
2025-07-30 10:25:56,864 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4 s, CPU [user: 1.28 s, system: 0.202 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.44 s [38% getFile content (809x), 33% getDir2 content (114x), 25% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.21 s [91% getReadConfiguration (12019x)] (12691x), resolve: 0.851 s [76% Category (96x), 18% User (9x)] (117x), ObjectMaps: 0.25 s [40% getPrimaryObjectProperty (110x), 30% getLastPromoted (110x), 30% getPrimaryObjectLocation (116x)] (452x)
2025-07-30 10:25:56,864 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 54, svn: 3.43 s [46% getDatedRevision (362x), 27% getFile content (809x), 23% getDir2 content (114x)] (1328x), RepositoryConfigService: 1.21 s [91% getReadConfiguration (12019x)] (12691x), resolve: 0.851 s [76% Category (96x), 18% User (10x)] (118x), ObjectMaps: 0.25 s [40% getPrimaryObjectProperty (110x), 30% getLastPromoted (110x), 30% getPrimaryObjectLocation (116x)] (452x)
2025-07-30 10:25:59,861 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59269bf0-0a465820-3d1e2b3c-477bfd27] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753799218700': Total: 0.58 s, CPU [user: 0.321 s, system: 0.0775 s], Allocated memory: 55.5 MB, transactions: 3, PolarionAuthenticator: 0.396 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0805 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 10:26:00,336 [ajp-nio-127.0.0.1-8889-exec-3 | cID:59269fa0-0a465820-3d1e2b3c-1bef82de] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.11 s, CPU [user: 0.0762 s, system: 0.0123 s], Allocated memory: 3.9 MB, transactions: 0
