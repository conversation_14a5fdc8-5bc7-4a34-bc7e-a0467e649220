2025-07-30 15:54:29,684 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:54:29,685 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:54:29,685 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:54:29,685 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:54:29,686 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:54:29,686 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:29,686 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:54:35,428 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:54:35,701 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.272 s. ]
2025-07-30 15:54:35,701 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:54:35,791 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0902 s. ]
2025-07-30 15:54:35,859 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:54:36,002 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:54:36,237 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.56 s. ]
2025-07-30 15:54:36,410 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:36,410 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:54:36,443 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.21 s. ]
2025-07-30 15:54:36,443 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:36,443 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 15:54:36,448 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 15:54:36,462 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:54:36,681 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:54:36,794 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:54:37,319 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.88 s. ]
2025-07-30 15:54:37,335 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:37,335 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:54:37,770 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:54:37,793 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.47 s. ]
2025-07-30 15:54:37,853 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:37,854 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:54:37,861 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:54:37,931 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:54:37,991 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:54:38,047 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:54:38,109 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:54:38,145 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:54:38,176 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:54:38,244 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:54:38,298 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:54:38,298 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.51 s. ]
2025-07-30 15:54:38,298 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:38,298 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:54:38,318 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:54:38,318 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:38,318 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:54:38,459 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:54:38,464 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:54:38,750 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.43 s. ]
2025-07-30 15:54:38,752 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:38,752 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:54:38,764 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:54:38,764 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:54:38,764 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:54:42,532 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.77 s. ]
2025-07-30 15:54:42,532 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:54:42,532 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.8 s. ]
2025-07-30 15:54:42,532 [main] INFO  com.polarion.platform.startup - ****************************************************************
