2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:47,491 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:01:52,241 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:01:52,398 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.157 s. ]
2025-07-30 11:01:52,399 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:01:52,452 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0533 s. ]
2025-07-30 11:01:52,506 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:01:52,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 78 s. ]
2025-07-30 11:01:52,838 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.35 s. ]
2025-07-30 11:01:52,943 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:52,943 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:01:52,970 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 11:01:52,970 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:52,970 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:01:52,975 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 11:01:52,975 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 11:01:52,976 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 11:01:52,976 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:01:52,976 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 11:01:52,976 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 11:01:52,984 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:01:53,127 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:01:53,207 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:01:53,672 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-07-30 11:01:53,685 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:53,685 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:01:53,936 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:01:53,950 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.28 s. ]
2025-07-30 11:01:53,983 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:53,984 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:01:53,988 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:01:54,048 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:01:54,108 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:01:54,138 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:01:54,171 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:01:54,212 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:01:54,255 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:01:54,289 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:01:54,323 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:01:54,323 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-07-30 11:01:54,323 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:54,323 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:01:54,338 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:01:54,338 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:54,338 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:01:54,452 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:01:54,454 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:01:54,569 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 11:01:54,570 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:54,570 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:01:54,577 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:01:54,578 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:01:54,578 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:01:57,424 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.85 s. ]
2025-07-30 11:01:57,425 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:01:57,425 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.93 s. ]
2025-07-30 11:01:57,425 [main] INFO  com.polarion.platform.startup - ****************************************************************
