2025-07-30 18:59:37,514 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.063 s [56% update (144x), 44% query (12x)] (221x), svn: 0.024 s [58% testConnection (1x), 30% getLatestRevision (2x)] (4x)
2025-07-30 18:59:37,628 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0346 s [61% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 18:59:38,362 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0481 s, system: 0.0783 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0656 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:59:38,362 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.193 s, system: 0.255 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:59:38,363 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.0762 s, system: 0.0912 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.113 s [85% log2 (10x)] (13x), ObjectMaps: 0.038 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:59:38,363 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.0837 s, system: 0.13 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0928 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0763 s [75% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-07-30 18:59:38,363 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.256 s, system: 0.312 s], Allocated memory: 72.8 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0889 s [29% info (5x), 22% log (1x), 20% log2 (5x), 14% getLatestRevision (2x)] (18x)
2025-07-30 18:59:38,363 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.73 s, CPU [user: 0.106 s, system: 0.182 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0815 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.041 s [71% log2 (5x), 18% getLatestRevision (1x)] (7x)
2025-07-30 18:59:38,363 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.507 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.363 s [63% log2 (36x), 14% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 18:59:38,588 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.192 s [100% getReadConfiguration (48x)] (48x), svn: 0.0744 s [86% info (18x)] (38x)
2025-07-30 18:59:39,019 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.321 s [73% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.248 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:59:39,270 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.236 s [100% doFinishStartup (1x)] (1x), commit: 0.0591 s [100% Revision (1x)] (1x), Lucene: 0.0378 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0177 s [100% objectsToInv (1x)] (1x)
2025-07-30 18:59:42,092 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.388 s [91% info (158x)] (168x)
2025-07-30 18:59:42,457 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616bf3b68045_0_6616bf3b68045_0_: finished. Total: 0.344 s, CPU [user: 0.151 s, system: 0.02 s], Allocated memory: 20.3 MB, resolve: 0.104 s [69% User (2x), 29% Project (1x)] (5x), Lucene: 0.0387 s [100% search (1x)] (1x), svn: 0.0299 s [51% getLatestRevision (2x), 17% testConnection (1x), 12% log (1x)] (8x), ObjectMaps: 0.0205 s [48% getPrimaryObjectProperty (2x), 44% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 18:59:43,007 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.81 s, CPU [user: 0.00723 s, system: 0.00194 s], Allocated memory: 315.5 kB, transactions: 1
2025-07-30 18:59:43,007 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.355 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.156 s [78% User (4x), 19% Project (1x)] (8x), Lucene: 0.0629 s [62% search (1x), 25% refresh (1x)] (3x), svn: 0.0427 s [56% getLatestRevision (3x), 23% testConnection (2x), 9% log (1x)] (10x), ObjectMaps: 0.0393 s [71% getPrimaryObjectLocation (4x), 25% getPrimaryObjectProperty (2x)] (13x), persistence listener: 0.0272 s [88% indexRefreshPersistenceListener (1x)] (7x), Incremental Baseline: 0.0264 s [100% WorkItem (22x)] (22x)
2025-07-30 18:59:43,008 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.816 s, CPU [user: 0.167 s, system: 0.026 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.682 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0757 s [87% buildBaselineSnapshots (1x)] (24x)
2025-07-30 18:59:43,388 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bf3b66440_0_6616bf3b66440_0_: finished. Total: 1.28 s, CPU [user: 0.313 s, system: 0.082 s], Allocated memory: 47.1 MB, svn: 0.763 s [47% getDatedRevision (181x), 38% getDir2 content (25x)] (307x), resolve: 0.348 s [100% Category (96x)] (96x), ObjectMaps: 0.126 s [43% getPrimaryObjectProperty (96x), 30% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 18:59:43,745 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bf3cd7c4a_0_6616bf3cd7c4a_0_: finished. Total: 0.163 s, CPU [user: 0.0565 s, system: 0.00651 s], Allocated memory: 19.9 MB, svn: 0.114 s [59% getDir2 content (17x), 41% getFile content (44x)] (62x), RepositoryConfigService: 0.0799 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 18:59:44,451 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bf3d0084b_0_6616bf3d0084b_0_: finished. Total: 0.705 s, CPU [user: 0.327 s, system: 0.0185 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.524 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.372 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-30 18:59:44,829 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bf3dcd44e_0_6616bf3dcd44e_0_: finished. Total: 0.264 s, CPU [user: 0.108 s, system: 0.00663 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.173 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.163 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x)
2025-07-30 18:59:44,830 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.73 s, CPU [user: 0.927 s, system: 0.131 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.59 s [40% getDir2 content (115x), 35% getFile content (807x), 22% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.881 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.399 s [87% Category (96x)] (117x), ObjectMaps: 0.148 s [45% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
2025-07-30 18:59:44,830 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.27 s [45% getDatedRevision (362x), 28% getDir2 content (115x), 24% getFile content (807x)] (1324x), RepositoryConfigService: 0.881 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.399 s [87% Category (96x)] (118x), ObjectMaps: 0.148 s [45% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
2025-07-30 19:01:21,277 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5afe6f44-0a465820-1fd31566-86c26c1e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.44 s, CPU [user: 0.204 s, system: 0.0775 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.381 s [100% authenticate (1x)] (1x)
2025-07-30 19:01:21,718 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5afe721d-0a465820-1fd31566-47ac864d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.152 s, CPU [user: 0.0728 s, system: 0.0195 s], Allocated memory: 9.7 MB, transactions: 0
2025-07-30 19:01:21,718 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5afe7237-0a465820-1fd31566-b7160a47] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.126 s, CPU [user: 0.0168 s, system: 0.00576 s], Allocated memory: 1.4 MB, transactions: 0, permissions: 0.0101 s [100% readInstance (1x)] (1x)
2025-07-30 19:01:21,752 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5afe7237-0a465820-1fd31566-91b4b0f6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753873281526': Total: 0.16 s, CPU [user: 0.0392 s, system: 0.00942 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 19:01:21,835 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5afe7238-0a465820-1fd31566-9ad8a0b8 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753873281528] INFO  TXLOGGER - Tx 6616bf9ca7051_0_6616bf9ca7051_0_: finished. Total: 0.143 s, CPU [user: 0.0578 s, system: 0.0151 s], Allocated memory: 8.1 MB, svn: 0.0193 s [65% testConnection (1x), 35% getFile content (2x)] (4x)
2025-07-30 19:01:21,837 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5afe7238-0a465820-1fd31566-9ad8a0b8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753873281528': Total: 0.245 s, CPU [user: 0.0683 s, system: 0.0189 s], Allocated memory: 9.3 MB, transactions: 1, RepositoryConfigService: 0.144 s [100% getReadConfiguration (1x)] (1x), svn: 0.0193 s [65% testConnection (1x), 35% getFile content (2x)] (4x)
2025-07-30 19:01:21,859 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5afe7238-0a465820-1fd31566-98893645] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753873281527': Total: 0.267 s, CPU [user: 0.0307 s, system: 0.00671 s], Allocated memory: 4.8 MB, transactions: 1, RepositoryConfigService: 0.166 s [100% getReadConfiguration (1x)] (1x)
