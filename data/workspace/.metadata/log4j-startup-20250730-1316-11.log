2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:11,888 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:16:16,077 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:16:16,237 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.16 s. ]
2025-07-30 13:16:16,237 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:16:16,294 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0573 s. ]
2025-07-30 13:16:16,346 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:16:16,464 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 13:16:16,668 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.79 s. ]
2025-07-30 13:16:16,747 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:16,747 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:16:16,776 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 13:16:16,777 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:16,777 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 13:16:16,782 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 13:16:16,788 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:16:16,900 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:16:17,028 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:16:17,455 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.68 s. ]
2025-07-30 13:16:17,466 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:17,466 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:16:17,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:16:17,677 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 13:16:17,700 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:17,700 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:16:17,704 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:16:17,749 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:16:17,784 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:16:17,822 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:16:17,846 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:16:17,860 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:16:17,872 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:16:17,897 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:16:17,923 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:16:17,923 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-07-30 13:16:17,923 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:17,923 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:16:17,935 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 13:16:17,936 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:17,936 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:16:18,032 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:16:18,036 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:16:18,194 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 13:16:18,194 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:18,194 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:16:18,201 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:16:18,201 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:16:18,201 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:16:20,348 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.15 s. ]
2025-07-30 13:16:20,348 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:16:20,348 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.46 s. ]
2025-07-30 13:16:20,348 [main] INFO  com.polarion.platform.startup - ****************************************************************
