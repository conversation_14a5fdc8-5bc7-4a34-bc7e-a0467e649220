2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:29,521 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:38:33,899 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:38:34,039 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.139 s. ]
2025-07-29 19:38:34,039 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:38:34,090 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0513 s. ]
2025-07-29 19:38:34,157 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:38:34,258 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 83 s. ]
2025-07-29 19:38:34,463 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.95 s. ]
2025-07-29 19:38:34,553 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:34,553 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:38:34,579 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-29 19:38:34,579 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:34,579 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-29 19:38:34,584 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-29 19:38:34,592 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:38:34,706 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:38:34,827 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:38:35,286 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-29 19:38:35,297 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:35,298 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:38:35,496 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:38:35,509 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-29 19:38:35,534 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:35,534 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:38:35,538 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:38:35,586 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:38:35,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:38:35,654 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:38:35,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:38:35,690 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:38:35,710 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:38:35,734 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:38:35,757 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:38:35,757 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.25 s. ]
2025-07-29 19:38:35,757 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:35,757 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:38:35,772 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:38:35,772 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:35,772 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:38:35,864 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:38:35,867 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:38:35,996 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 19:38:35,997 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:35,997 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:38:36,007 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:38:36,007 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:38:36,007 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:41:07,490 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 151.48 s. ]
2025-07-29 19:41:07,491 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:41:07,491 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 158 s. ]
2025-07-29 19:41:07,491 [main] INFO  com.polarion.platform.startup - ****************************************************************
