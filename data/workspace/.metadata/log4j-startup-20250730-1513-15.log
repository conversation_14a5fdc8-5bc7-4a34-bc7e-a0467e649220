2025-07-30 15:13:15,582 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:13:15,582 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:13:15,582 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:13:15,583 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:13:15,583 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:13:15,583 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:15,583 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:13:20,262 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:13:20,398 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-07-30 15:13:20,399 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:13:20,484 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0855 s. ]
2025-07-30 15:13:20,545 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:13:20,652 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 15:13:20,877 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.3 s. ]
2025-07-30 15:13:20,966 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:20,967 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:13:21,004 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 15:13:21,005 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:21,005 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:13:21,009 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (1/9)
2025-07-30 15:13:21,009 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 15:13:21,009 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 15:13:21,009 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 15:13:21,009 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 15:13:21,010 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 15:13:21,015 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:13:21,140 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:13:21,379 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:13:22,021 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.02 s. ]
2025-07-30 15:13:22,032 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,032 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:13:22,236 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:13:22,251 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 15:13:22,278 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,278 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:13:22,281 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:13:22,328 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:13:22,372 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:13:22,422 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:13:22,471 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:13:22,499 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:13:22,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:13:22,583 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:13:22,631 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:13:22,631 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.38 s. ]
2025-07-30 15:13:22,631 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,631 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:13:22,645 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 15:13:22,645 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,645 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:13:22,788 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:13:22,791 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:13:22,986 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.34 s. ]
2025-07-30 15:13:22,987 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,987 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:13:22,998 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:13:22,998 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:13:22,998 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:13:25,926 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.93 s. ]
2025-07-30 15:13:25,927 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:13:25,928 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.3 s. ]
2025-07-30 15:13:25,928 [main] INFO  com.polarion.platform.startup - ****************************************************************
