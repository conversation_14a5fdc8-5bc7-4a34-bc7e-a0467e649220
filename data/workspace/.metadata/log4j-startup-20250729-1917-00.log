2025-07-29 19:17:00,570 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:17:00,570 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:17:00,570 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:17:00,570 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:17:00,570 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:17:00,571 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:00,571 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:17:04,691 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:17:04,879 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.188 s. ]
2025-07-29 19:17:04,879 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:17:04,933 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0545 s. ]
2025-07-29 19:17:04,985 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:17:05,095 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 94 s. ]
2025-07-29 19:17:05,302 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.74 s. ]
2025-07-29 19:17:05,396 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:05,396 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:17:05,428 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-29 19:17:05,429 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:05,429 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 19:17:05,434 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-29 19:17:05,442 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:17:05,572 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:17:05,679 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:17:06,169 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-29 19:17:06,180 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,180 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:17:06,391 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:17:06,403 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 19:17:06,429 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,429 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:17:06,432 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:17:06,484 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:17:06,542 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:17:06,568 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:17:06,596 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:17:06,636 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:17:06,674 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:17:06,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:17:06,731 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:17:06,731 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.33 s. ]
2025-07-29 19:17:06,731 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,731 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:17:06,746 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 19:17:06,746 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,746 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:17:06,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:17:06,873 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:17:06,988 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-29 19:17:06,988 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,988 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:17:06,995 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:17:06,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:17:06,995 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:17:22,744 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 15.75 s. ]
2025-07-29 19:17:22,745 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:17:22,745 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 22.2 s. ]
2025-07-29 19:17:22,745 [main] INFO  com.polarion.platform.startup - ****************************************************************
