2025-07-30 13:16:16,668 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0584 s [62% update (144x), 38% query (12x)] (221x), svn: 0.00881 s [52% getLatestRevision (2x), 37% testConnection (1x)] (4x)
2025-07-30 13:16:16,777 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0357 s [62% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.0716 s, system: 0.124 s], Allocated memory: 10.8 MB, transactions: 0, svn: 0.0748 s [81% log2 (10x)] (13x), ObjectMaps: 0.0556 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.184 s, system: 0.268 s], Allocated memory: 52.7 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.234 s, system: 0.329 s], Allocated memory: 70.7 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.103 s, system: 0.206 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0592 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.0639 s, system: 0.101 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0824 s [27% log2 (5x), 21% getLatestRevision (2x), 20% log (1x), 19% info (5x)] (18x), ObjectMaps: 0.0576 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:16:17,455 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.675 s, CPU [user: 0.0759 s, system: 0.145 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0663 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0652 s [65% log2 (10x), 26% getLatestRevision (2x)] (13x)
2025-07-30 13:16:17,456 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.46 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.283 s [58% log2 (36x), 19% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 13:16:17,677 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.192 s [100% getReadConfiguration (48x)] (48x), svn: 0.0747 s [85% info (18x)] (38x)
2025-07-30 13:16:17,923 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.187 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.146 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:16:18,194 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.258 s [100% doFinishStartup (1x)] (1x), commit: 0.0776 s [100% Revision (1x)] (1x), Lucene: 0.0362 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0134 s [100% objectsToInv (1x)] (1x)
2025-07-30 13:16:20,348 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.276 s [87% info (158x)] (168x)
2025-07-30 13:16:20,541 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661670a480441_0_661670a480441_0_: finished. Total: 0.187 s, CPU [user: 0.114 s, system: 0.0136 s], Allocated memory: 20.7 MB, resolve: 0.0546 s [63% User (2x), 34% Project (1x)] (5x), Lucene: 0.0207 s [100% search (1x)] (1x), svn: 0.0182 s [41% getLatestRevision (2x), 21% testConnection (1x), 20% log (1x)] (8x), ObjectMaps: 0.0141 s [47% getPrimaryObjectProperty (2x), 45% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 13:16:21,016 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.607 s, CPU [user: 0.00534 s, system: 0.00132 s], Allocated memory: 316.0 kB, transactions: 1
2025-07-30 13:16:21,017 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.197 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.0684 s [68% User (3x), 27% Project (1x)] (7x), Lucene: 0.0379 s [55% search (1x), 37% add (1x)] (3x), ObjectMaps: 0.0187 s [58% getPrimaryObjectLocation (3x), 35% getPrimaryObjectProperty (2x)] (12x), svn: 0.0182 s [41% getLatestRevision (2x), 21% testConnection (1x), 20% log (1x)] (8x), Incremental Baseline: 0.0171 s [100% WorkItem (22x)] (22x), persistence listener: 0.011 s [73% indexRefreshPersistenceListener (1x), 21% WorkItemActivityCreator (1x)] (7x)
2025-07-30 13:16:21,017 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.671 s, CPU [user: 0.132 s, system: 0.0214 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.54 s [99% getDatedRevision (181x)] (183x)
2025-07-30 13:16:21,315 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661670a47fc40_0_661670a47fc40_0_: finished. Total: 0.963 s, CPU [user: 0.257 s, system: 0.0698 s], Allocated memory: 47.1 MB, svn: 0.557 s [50% getDatedRevision (181x), 31% getDir2 content (25x)] (307x), resolve: 0.298 s [100% Category (96x)] (96x), ObjectMaps: 0.101 s [41% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 27% getLastPromoted (96x)] (387x)
2025-07-30 13:16:21,602 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661670a598c4a_0_661670a598c4a_0_: finished. Total: 0.126 s, CPU [user: 0.0464 s, system: 0.004 s], Allocated memory: 19.9 MB, svn: 0.0931 s [67% getDir2 content (17x), 33% getFile content (44x)] (62x), RepositoryConfigService: 0.0492 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 13:16:22,272 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661670a5b884b_0_661670a5b884b_0_: finished. Total: 0.67 s, CPU [user: 0.321 s, system: 0.0244 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.465 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.358 s [56% getFile content (412x), 44% getDir2 content (21x)] (434x)
2025-07-30 13:16:22,666 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661670a67b04e_0_661670a67b04e_0_: finished. Total: 0.285 s, CPU [user: 0.12 s, system: 0.00704 s], Allocated memory: 390.8 MB, RepositoryConfigService: 0.195 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.174 s [59% getFile content (185x), 41% getDir2 content (21x)] (207x)
2025-07-30 13:16:22,666 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.32 s, CPU [user: 0.854 s, system: 0.12 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.34 s [41% getDir2 content (115x), 35% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.802 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.339 s [88% Category (96x)] (117x)
2025-07-30 13:16:22,666 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 1.89 s [43% getDatedRevision (362x), 29% getDir2 content (115x), 25% getFile content (807x)] (1326x), RepositoryConfigService: 0.802 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.34 s [88% Category (96x)] (118x), ObjectMaps: 0.115 s [43% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 26% getLastPromoted (108x)] (442x)
2025-07-30 13:16:29,683 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59c2b538-0a465820-5ac79bd5-5d21656c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.315 s, CPU [user: 0.193 s, system: 0.0241 s], Allocated memory: 43.2 MB, transactions: 2, PolarionAuthenticator: 0.265 s [100% authenticate (1x)] (1x)
