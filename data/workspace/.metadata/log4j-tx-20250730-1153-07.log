2025-07-30 11:53:12,739 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0553 s [67% update (144x), 33% query (12x)] (221x), svn: 0.0228 s [45% getLatestRevision (2x), 39% testConnection (1x)] (4x)
2025-07-30 11:53:12,866 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0514 s [59% getDir2 content (2x), 28% info (3x)] (6x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.0497 s, system: 0.0973 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0641 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.7 s, CPU [user: 0.0798 s, system: 0.124 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0859 s [83% log2 (10x)] (13x), ObjectMaps: 0.0573 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.128 s, system: 0.214 s], Allocated memory: 26.3 MB, transactions: 0, svn: 0.0805 s [27% info (5x), 27% log2 (5x), 24% log (1x), 10% getLatestRevision (2x)] (18x), ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.7 s, CPU [user: 0.231 s, system: 0.338 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.127 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.7 s, CPU [user: 0.0793 s, system: 0.14 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0594 s [75% log2 (10x), 18% getLatestRevision (2x)] (13x)
2025-07-30 11:53:13,570 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.701 s, CPU [user: 0.189 s, system: 0.277 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:53:13,570 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.52 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.295 s [61% log2 (36x), 14% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 11:53:13,810 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.197 s [100% getReadConfiguration (48x)] (48x), svn: 0.092 s [80% info (18x)] (38x)
2025-07-30 11:53:14,166 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.277 s [77% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.203 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:53:14,394 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.215 s [100% doFinishStartup (1x)] (1x), commit: 0.0472 s [100% Revision (1x)] (1x), Lucene: 0.0344 s [100% refresh (1x)] (1x), DB: 0.0136 s [43% query (1x), 30% update (3x), 16% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0127 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:53:16,777 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.328 s [88% info (158x)] (168x)
2025-07-30 11:53:17,064 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165da1bbc43_0_66165da1bbc43_0_: finished. Total: 0.28 s, CPU [user: 0.12 s, system: 0.0183 s], Allocated memory: 22.0 MB, resolve: 0.0922 s [74% User (2x), 25% Project (1x)] (5x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.025 s [100% search (1x)] (1x), svn: 0.0235 s [31% getLatestRevision (2x), 27% getFile content (2x), 19% log (1x), 15% testConnection (1x)] (8x), ObjectMaps: 0.0219 s [60% getPrimaryObjectLocation (2x), 35% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 11:53:17,512 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.661 s, CPU [user: 0.00524 s, system: 0.00126 s], Allocated memory: 316.0 kB, transactions: 1, GC: 0.038 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:53:17,513 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.292 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.105 s [76% User (3x), 22% Project (1x)] (7x), Lucene: 0.0403 s [62% search (1x), 31% add (1x)] (3x), svn: 0.0309 s [37% getLatestRevision (3x), 22% testConnection (2x), 21% getFile content (2x)] (10x), ObjectMaps: 0.0247 s [64% getPrimaryObjectLocation (3x), 31% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0216 s [100% WorkItem (22x)] (22x)
2025-07-30 11:53:17,514 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.741 s, CPU [user: 0.134 s, system: 0.0208 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.547 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0853 s [87% buildBaselineSnapshots (1x)] (24x), GC: 0.038 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:53:17,790 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165da1bbc45_0_66165da1bbc45_0_: finished. Total: 1.01 s, CPU [user: 0.245 s, system: 0.0682 s], Allocated memory: 46.6 MB, svn: 0.61 s [42% getDatedRevision (181x), 41% getDir2 content (25x)] (307x), resolve: 0.282 s [100% Category (96x)] (96x), ObjectMaps: 0.096 s [45% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 11:53:18,082 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165da2e144a_0_66165da2e144a_0_: finished. Total: 0.125 s, CPU [user: 0.0457 s, system: 0.00389 s], Allocated memory: 19.9 MB, svn: 0.0934 s [71% getDir2 content (17x), 29% getFile content (44x)] (62x), RepositoryConfigService: 0.0439 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:53:18,710 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165da300c4b_0_66165da300c4b_0_: finished. Total: 0.627 s, CPU [user: 0.311 s, system: 0.013 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.442 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.333 s [57% getFile content (412x), 43% getDir2 content (21x)] (434x)
2025-07-30 11:53:19,042 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165da3b704e_0_66165da3b704e_0_: finished. Total: 0.229 s, CPU [user: 0.0929 s, system: 0.00508 s], Allocated memory: 385.4 MB, svn: 0.144 s [53% getDir2 content (21x), 47% getFile content (185x)] (207x), RepositoryConfigService: 0.136 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 11:53:19,042 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.26 s, CPU [user: 0.802 s, system: 0.105 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.34 s [46% getDir2 content (115x), 32% getFile content (807x), 19% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.707 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.326 s [87% Category (96x)] (117x)
2025-07-30 11:53:19,042 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 1.89 s [42% getDatedRevision (362x), 33% getDir2 content (115x), 23% getFile content (807x)] (1324x), RepositoryConfigService: 0.707 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.327 s [86% Category (96x)] (118x), Lucene: 0.117 s [64% buildBaselineSnapshots (2x), 25% search (5x)] (54x), ObjectMaps: 0.112 s [47% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 11:53:59,428 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59772b6e-7f000001-625ee079-18723abe] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.533 s, CPU [user: 0.247 s, system: 0.105 s], Allocated memory: 42.1 MB, transactions: 2, PolarionAuthenticator: 0.464 s [100% authenticate (1x)] (1x)
2025-07-30 11:53:59,746 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59772e40-7f000001-625ee079-6225b65d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.129 s, CPU [user: 0.0631 s, system: 0.015 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 11:53:59,746 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59772e48-7f000001-625ee079-987ecd42] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.122 s, CPU [user: 0.0136 s, system: 0.00374 s], Allocated memory: 1.3 MB, transactions: 0
2025-07-30 11:53:59,767 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59772e4c-7f000001-625ee079-2bc535ce] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753847639568': Total: 0.138 s, CPU [user: 0.0364 s, system: 0.00652 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 11:55:09,610 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59772e4c-7f000001-625ee079-966afb10 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847639570] INFO  TXLOGGER - Tx 66165dcbac451_0_66165dcbac451_0_: finished. Total: 69.9 s, CPU [user: 0.0548 s, system: 0.0286 s], Allocated memory: 5.4 MB
2025-07-30 11:55:09,650 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59772e4c-7f000001-625ee079-966afb10] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847639570': Total: 70 s, CPU [user: 0.076 s, system: 0.0448 s], Allocated memory: 7.8 MB, transactions: 1, RepositoryConfigService: 69.9 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:55:14,014 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59772e4d-7f000001-625ee079-a99a65ab | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847639569] INFO  TXLOGGER - Tx 66165e0fec052_0_66165e0fec052_0_: finished. Total: 4.39 s, CPU [user: 0.0328 s, system: 0.0272 s], Allocated memory: 2.4 MB
2025-07-30 11:55:14,065 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59772e4d-7f000001-625ee079-a99a65ab] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847639569': Total: 74.4 s, CPU [user: 0.0614 s, system: 0.0467 s], Allocated memory: 4.7 MB, transactions: 1, RepositoryConfigService: 74.3 s [100% getReadConfiguration (1x)] (1x)
