2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:28,667 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:29:33,619 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:29:33,768 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.149 s. ]
2025-07-30 16:29:33,768 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:29:33,826 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0575 s. ]
2025-07-30 16:29:33,897 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:29:34,036 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 16:29:34,268 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.61 s. ]
2025-07-30 16:29:34,384 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:34,384 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:29:34,410 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-07-30 16:29:34,410 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:34,410 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:29:34,416 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 16:29:34,416 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 16:29:34,417 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 16:29:34,416 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 16:29:34,416 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 16:29:34,416 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 16:29:34,425 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:29:34,602 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:29:34,709 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:29:35,233 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.82 s. ]
2025-07-30 16:29:35,244 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:35,244 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:29:35,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:29:35,482 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 16:29:35,506 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:35,506 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:29:35,509 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:29:35,644 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:29:35,819 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:29:35,862 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:29:35,911 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:29:35,938 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:29:35,963 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:29:36,017 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:29:36,055 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:29:36,055 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.57 s. ]
2025-07-30 16:29:36,055 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:36,055 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:29:36,070 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 16:29:36,070 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:36,070 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:29:36,252 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:29:36,255 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:29:36,459 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.39 s. ]
2025-07-30 16:29:36,462 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:36,462 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:29:36,486 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.03 s. ]
2025-07-30 16:29:36,486 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:29:36,486 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:29:40,517 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 4.03 s. ]
2025-07-30 16:29:40,518 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:29:40,518 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.8 s. ]
2025-07-30 16:29:40,518 [main] INFO  com.polarion.platform.startup - ****************************************************************
