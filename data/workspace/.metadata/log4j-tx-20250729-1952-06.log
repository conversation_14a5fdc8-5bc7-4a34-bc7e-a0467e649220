2025-07-29 19:52:11,128 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0338 s [66% update (144x), 34% query (12x)] (221x), svn: 0.00958 s [53% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-29 19:52:11,249 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0353 s [62% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.0585 s, system: 0.0824 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0563 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0478 s [72% log2 (5x), 16% testConnection (1x)] (7x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.212 s, system: 0.274 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.734 s, CPU [user: 0.236 s, system: 0.336 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.734 s, CPU [user: 0.0818 s, system: 0.107 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0866 s [86% log2 (10x)] (13x), ObjectMaps: 0.0689 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.133 s, system: 0.204 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0779 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 19:52:11,987 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.735 s, CPU [user: 0.107 s, system: 0.134 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.126 s [42% log2 (10x), 18% info (5x), 14% getLatestRevision (3x), 13% log (1x)] (24x), ObjectMaps: 0.0656 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:52:11,988 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.511 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.325 s [63% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-29 19:52:12,230 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.203 s [100% getReadConfiguration (48x)] (48x), svn: 0.0768 s [82% info (18x)] (38x)
2025-07-29 19:52:12,584 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.288 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.211 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:52:12,808 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.107 s, CPU [user: 0.0265 s, system: 0.00688 s], Allocated memory: 10.5 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 19:52:12,839 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.238 s [100% doFinishStartup (1x)] (1x), commit: 0.0491 s [100% Revision (1x)] (1x), Lucene: 0.0484 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0148 s [100% objectsToInv (1x)] (1x)
2025-07-29 19:52:15,633 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.432 s [93% info (158x)] (168x)
2025-07-29 19:52:16,443 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.739 s, CPU [user: 0.0057 s, system: 0.00123 s], Allocated memory: 531.6 kB, transactions: 1
2025-07-29 19:52:16,445 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, Incremental Baseline: 0.0251 s [100% WorkItem (21x)] (21x), persistence listener: 0.022 s [65% indexRefreshPersistenceListener (1x), 24% WorkItemActivityCreator (1x)] (7x), resolve: 0.016 s [77% User (1x), 23% Revision (2x)] (3x), Lucene: 0.0146 s [60% add (1x), 40% refresh (1x)] (2x), notification worker: 0.0134 s [65% RevisionActivityCreator (2x), 13% WorkItemActivityCreator (1x), 11% TestRunActivityCreator (1x)] (6x), PullingJob: 0.00882 s [100% collectChanges (1x)] (1x), svn: 0.00824 s [53% testConnection (1x), 47% getLatestRevision (1x)] (2x), ObjectMaps: 0.00376 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.0016 s [75% update (1x), 25% commit (1x)] (2x), EHCache: 0.00155 s [99% GET (15x)] (36x)
2025-07-29 19:52:16,445 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.811 s, CPU [user: 0.164 s, system: 0.0247 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.658 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0486 s [72% buildBaselineSnapshots (1x), 28% buildBaseline (22x)] (23x)
2025-07-29 19:52:16,856 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581ac05c40_0_661581ac05c40_0_: finished. Total: 1.22 s, CPU [user: 0.404 s, system: 0.0841 s], Allocated memory: 54.6 MB, svn: 0.727 s [53% getDatedRevision (181x), 30% getDir2 content (25x)] (307x), resolve: 0.369 s [100% Category (96x)] (96x), ObjectMaps: 0.121 s [45% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (388x)
2025-07-29 19:52:17,088 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581ad51448_0_661581ad51448_0_: finished. Total: 0.123 s, CPU [user: 0.0611 s, system: 0.00986 s], Allocated memory: 8.4 MB, RepositoryConfigService: 0.0582 s [50% getReadConfiguration (162x), 50% getReadUserConfiguration (10x)] (172x), svn: 0.0514 s [58% info (19x), 38% getFile content (16x)] (37x), resolve: 0.0345 s [100% User (9x)] (9x), ObjectMaps: 0.017 s [45% getPrimaryObjectProperty (9x), 34% getPrimaryObjectLocation (9x), 21% getLastPromoted (9x)] (37x)
2025-07-29 19:52:17,350 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581ad7a44a_0_661581ad7a44a_0_: finished. Total: 0.221 s, CPU [user: 0.0781 s, system: 0.00761 s], Allocated memory: 19.9 MB, svn: 0.168 s [65% getDir2 content (17x), 35% getFile content (44x)] (62x), RepositoryConfigService: 0.0928 s [99% getReadConfiguration (170x)] (192x)
2025-07-29 19:52:18,741 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581adb184b_0_661581adb184b_0_: finished. Total: 1.39 s, CPU [user: 0.548 s, system: 0.0485 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.783 s [61% getFile content (412x), 39% getDir2 content (21x)] (434x)
2025-07-29 19:52:19,564 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5606bd61-7f000001-35a454bf-a1bc9ae9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.521 s, CPU [user: 0.307 s, system: 0.0408 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.436 s [100% authenticate (1x)] (1x)
2025-07-29 19:52:20,045 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581af2d84e_0_661581af2d84e_0_: finished. Total: 1.17 s, CPU [user: 0.347 s, system: 0.0334 s], Allocated memory: 389.7 MB, RepositoryConfigService: 0.892 s [64% getReadConfiguration (2787x), 36% getExistingPrefixes (89x)] (3025x), svn: 0.836 s [39% getFile content (185x), 34% info (94x), 27% getDir2 content (20x)] (300x)
2025-07-29 19:52:20,045 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.41 s, CPU [user: 1.54 s, system: 0.199 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.73 s [38% getFile content (809x), 35% getDir2 content (114x), 14% getDatedRevision (181x)] (1238x), RepositoryConfigService: 2.12 s [82% getReadConfiguration (12019x)] (12691x), resolve: 0.468 s [79% Category (96x), 13% Project (6x)] (117x)
2025-07-29 19:52:20,045 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.4 s [30% getFile content (810x), 30% getDatedRevision (362x), 28% getDir2 content (114x)] (1425x), RepositoryConfigService: 2.13 s [82% getReadConfiguration (12020x)] (12692x), resolve: 0.469 s [79% Category (96x), 13% Project (6x)] (119x), PolarionAuthenticator: 0.44 s [100% authenticate (2x)] (2x)
2025-07-29 19:52:20,097 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5606c0bd-7f000001-35a454bf-02f2960c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.196 s, CPU [user: 0.0915 s, system: 0.0103 s], Allocated memory: 9.3 MB, transactions: 0
2025-07-29 19:52:20,099 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5606c0f0-7f000001-35a454bf-330bfe3e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.145 s, CPU [user: 0.0223 s, system: 0.00377 s], Allocated memory: 2.0 MB, transactions: 0, permissions: 0.0103 s [100% readInstance (1x)] (1x)
2025-07-29 19:52:20,133 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5606c0f0-7f000001-35a454bf-3d7694cd] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753789939830': Total: 0.181 s, CPU [user: 0.0424 s, system: 0.00596 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-29 19:52:20,152 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5606c0f0-7f000001-35a454bf-509104d6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753789939832': Total: 0.198 s, CPU [user: 0.0506 s, system: 0.00818 s], Allocated memory: 4.9 MB, transactions: 1, RepositoryConfigService: 0.0898 s [100% getReadConfiguration (1x)] (1x), svn: 0.0169 s [61% testConnection (1x), 38% getFile content (1x)] (3x)
2025-07-29 19:52:20,185 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5606c0f0-7f000001-35a454bf-4ea56fd6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753789939831': Total: 0.232 s, CPU [user: 0.0393 s, system: 0.00291 s], Allocated memory: 3.0 MB, transactions: 1, RepositoryConfigService: 0.102 s [100% getReadConfiguration (1x)] (1x)
