2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:03,793 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:17:07,955 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:17:08,127 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.172 s. ]
2025-07-30 18:17:08,127 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:17:08,211 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0838 s. ]
2025-07-30 18:17:08,283 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:17:08,400 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-07-30 18:17:08,611 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.82 s. ]
2025-07-30 18:17:08,760 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:08,760 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:17:08,794 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.18 s. ]
2025-07-30 18:17:08,794 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:08,794 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (4/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 18:17:08,805 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 18:17:08,812 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:17:09,017 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:17:09,080 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:17:09,679 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.89 s. ]
2025-07-30 18:17:09,697 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:09,697 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:17:09,933 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:17:09,943 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 18:17:09,969 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:09,969 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:17:09,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:17:10,019 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:17:10,061 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:17:10,100 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:17:10,140 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:17:10,155 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:17:10,174 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:17:10,206 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:17:10,235 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:17:10,235 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-30 18:17:10,236 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:10,236 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:17:10,250 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:17:10,250 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:10,250 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:17:10,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:17:10,353 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:17:10,456 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-07-30 18:17:10,457 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:10,457 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:17:10,464 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:17:10,464 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:17:10,464 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:17:50,972 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 40.51 s. ]
2025-07-30 18:17:50,974 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:17:50,974 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 47.2 s. ]
2025-07-30 18:17:50,974 [main] INFO  com.polarion.platform.startup - ****************************************************************
