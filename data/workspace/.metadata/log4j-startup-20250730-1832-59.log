2025-07-30 18:32:59,669 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:32:59,670 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:33:05,047 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:33:05,551 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.504 s. ]
2025-07-30 18:33:05,551 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:33:05,639 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0879 s. ]
2025-07-30 18:33:05,770 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:33:05,918 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 21 s. ]
2025-07-30 18:33:06,165 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 6.5 s. ]
2025-07-30 18:33:06,262 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:06,262 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:33:06,289 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 18:33:06,289 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:06,289 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:33:06,294 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (3/9)
2025-07-30 18:33:06,294 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 18:33:06,294 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 18:33:06,294 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 18:33:06,294 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 18:33:06,295 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 18:33:06,301 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:33:06,474 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:33:06,559 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:33:07,075 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.79 s. ]
2025-07-30 18:33:07,086 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:07,086 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:33:07,297 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:33:07,311 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 18:33:07,340 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:07,340 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:33:07,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:33:07,408 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:33:07,469 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:33:07,517 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:33:07,560 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:33:07,583 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:33:07,604 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:33:07,656 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:33:07,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:33:07,708 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.4 s. ]
2025-07-30 18:33:07,708 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:07,708 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:33:07,723 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:33:07,723 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:07,723 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:33:07,837 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:33:07,844 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:33:08,014 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 18:33:08,015 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:08,015 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:33:08,023 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:33:08,023 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:33:08,023 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:33:17,103 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 9.08 s. ]
2025-07-30 18:33:17,106 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:33:17,106 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 17.4 s. ]
2025-07-30 18:33:17,106 [main] INFO  com.polarion.platform.startup - ****************************************************************
