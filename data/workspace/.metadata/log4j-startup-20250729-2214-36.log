2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:36,098 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 22:14:40,716 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 22:14:40,886 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.17 s. ]
2025-07-29 22:14:40,886 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 22:14:40,956 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0698 s. ]
2025-07-29 22:14:41,006 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 22:14:41,158 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 80 s. ]
2025-07-29 22:14:41,384 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.29 s. ]
2025-07-29 22:14:41,510 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:41,510 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 22:14:41,548 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.16 s. ]
2025-07-29 22:14:41,549 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:41,549 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 22:14:41,559 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-29 22:14:41,559 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 22:14:41,559 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-29 22:14:41,559 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-29 22:14:41,560 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-29 22:14:41,559 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 22:14:41,585 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 22:14:41,755 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 22:14:41,841 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 22:14:42,305 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-29 22:14:42,318 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:42,318 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 22:14:42,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 22:14:42,596 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.29 s. ]
2025-07-29 22:14:42,625 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:42,625 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 22:14:42,628 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 22:14:42,685 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 22:14:42,740 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 22:14:42,765 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 22:14:42,782 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 22:14:42,812 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 22:14:42,842 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 22:14:42,876 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 22:14:42,916 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 22:14:42,916 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 22:14:42,916 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:42,916 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 22:14:42,930 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 22:14:42,931 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:42,931 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 22:14:43,047 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 22:14:43,050 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 22:14:43,185 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-29 22:14:43,186 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:43,186 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 22:14:43,194 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 22:14:43,194 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 22:14:43,194 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 22:26:44,328 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 721.14 s. ]
2025-07-29 22:26:44,330 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 22:26:44,330 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 728 s. ]
2025-07-29 22:26:44,330 [main] INFO  com.polarion.platform.startup - ****************************************************************
