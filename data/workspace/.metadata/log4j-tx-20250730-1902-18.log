2025-07-30 19:02:23,386 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0702 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0153 s [44% getLatestRevision (2x), 43% testConnection (1x)] (4x)
2025-07-30 19:02:23,510 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0316 s [59% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.25 s, system: 0.35 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.112 s, system: 0.218 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0926 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.039 s [69% log2 (5x), 18% testConnection (1x)] (7x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.0899 s, system: 0.115 s], Allocated memory: 13.3 MB, transactions: 0, svn: 0.138 s [52% log2 (10x), 14% log (1x), 13% info (5x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0562 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.0769 s, system: 0.145 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.0814 s [70% log2 (10x), 25% getLatestRevision (2x)] (13x), ObjectMaps: 0.0667 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.198 s, system: 0.288 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.153 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 19:02:24,273 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.0491 s, system: 0.0853 s], Allocated memory: 6.1 MB, transactions: 0, ObjectMaps: 0.0608 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0427 s [78% log2 (5x), 11% getLatestRevision (1x)] (7x)
2025-07-30 19:02:24,274 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.552 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.352 s [62% log2 (36x), 17% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 19:02:24,527 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.217 s [100% getReadConfiguration (48x)] (48x), svn: 0.0884 s [82% info (18x)] (38x)
2025-07-30 19:02:24,868 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.269 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.206 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 19:02:25,110 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.12 s, CPU [user: 0.0376 s, system: 0.0103 s], Allocated memory: 11.0 MB, GC: 0.011 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 19:02:25,146 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.262 s [100% doFinishStartup (1x)] (1x), commit: 0.0708 s [100% Revision (1x)] (1x), Lucene: 0.0372 s [100% refresh (1x)] (1x), DB: 0.0146 s [32% update (3x), 32% query (1x), 27% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0143 s [100% objectsToInv (1x)] (1x)
2025-07-30 19:02:28,169 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.374 s [85% info (158x)] (170x)
2025-07-30 19:02:28,173 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 3, persistence listener: 0.0238 s [85% indexRefreshPersistenceListener (1x)] (7x), notification worker: 0.00453 s [40% WorkItemActivityCreator (1x), 29% TestRunActivityCreator (1x), 16% BuildActivityCreator (1x)] (4x)
2025-07-30 19:02:28,538 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616bfdd93444_0_6616bfdd93444_0_: finished. Total: 0.365 s, CPU [user: 0.129 s, system: 0.0214 s], Allocated memory: 21.5 MB, resolve: 0.0683 s [53% User (2x), 44% Project (1x)] (5x), planQueryExpander: 0.0617 s [100% expand (1x)] (2x), GC: 0.055 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0468 s [100% search (1x)] (1x), hasLinkedResourcesQueryExpander: 0.026 s [100% expand (1x)] (2x), ObjectMaps: 0.0242 s [48% getPrimaryObjectProperty (2x), 46% getPrimaryObjectLocation (2x)] (11x), svn: 0.022 s [35% log (1x), 33% getLatestRevision (2x), 15% testConnection (1x)] (8x)
2025-07-30 19:02:29,153 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.817 s, CPU [user: 0.00616 s, system: 0.00135 s], Allocated memory: 315.6 kB, transactions: 1
2025-07-30 19:02:29,153 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 25, notification worker: 0.37 s [100% RevisionActivityCreator (2x)] (2x), resolve: 0.102 s [67% User (3x), 29% Project (1x)] (7x), Lucene: 0.0747 s [63% search (1x), 21% refresh (1x)] (3x), planQueryExpander: 0.0617 s [100% expand (1x)] (2x), ObjectMaps: 0.0317 s [59% getPrimaryObjectLocation (3x), 37% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0316 s [100% WorkItem (22x)] (22x), hasLinkedResourcesQueryExpander: 0.026 s [100% expand (1x)] (2x), svn: 0.022 s [35% log (1x), 33% getLatestRevision (2x), 15% testConnection (1x)] (8x)
2025-07-30 19:02:29,154 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.99 s, CPU [user: 0.165 s, system: 0.0285 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.711 s [98% getDatedRevision (181x)] (183x), Lucene: 0.056 s [70% buildBaselineSnapshots (1x), 30% buildBaseline (23x)] (24x), GC: 0.055 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 19:02:29,551 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfdd94445_0_6616bfdd94445_0_: finished. Total: 1.37 s, CPU [user: 0.332 s, system: 0.0923 s], Allocated memory: 46.6 MB, svn: 0.818 s [46% getDatedRevision (181x), 38% getDir2 content (25x)] (307x), resolve: 0.406 s [100% Category (96x)] (96x), ObjectMaps: 0.149 s [43% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 19:02:29,779 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfdefd448_0_6616bfdefd448_0_: finished. Total: 0.158 s, CPU [user: 0.0646 s, system: 0.0122 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0776 s [56% getReadConfiguration (162x), 44% getReadUserConfiguration (10x)] (172x), svn: 0.0697 s [62% info (19x), 30% getFile content (15x)] (36x), resolve: 0.0407 s [100% User (9x)] (9x), ObjectMaps: 0.0198 s [60% getPrimaryObjectProperty (8x), 24% getLastPromoted (8x)] (32x)
2025-07-30 19:02:30,024 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfdf34c4a_0_6616bfdf34c4a_0_: finished. Total: 0.181 s, CPU [user: 0.0531 s, system: 0.00684 s], Allocated memory: 19.9 MB, svn: 0.15 s [79% getDir2 content (17x), 21% getFile content (44x)] (62x), RepositoryConfigService: 0.0475 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 19:02:30,844 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfdf6204b_0_6616bfdf6204b_0_: finished. Total: 0.819 s, CPU [user: 0.362 s, system: 0.0327 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.616 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.443 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 19:02:31,371 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfe02f04c_0_6616bfe02f04c_0_: finished. Total: 0.526 s, CPU [user: 0.0639 s, system: 0.013 s], Allocated memory: 17.8 MB, svn: 0.431 s [85% getDir2 content (18x)] (48x), RepositoryConfigService: 0.141 s [96% getReadConfiguration (124x)] (148x)
2025-07-30 19:02:31,491 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfe0b2c4d_0_6616bfe0b2c4d_0_: finished. Total: 0.12 s, CPU [user: 0.021 s, system: 0.005 s], Allocated memory: 5.3 MB, svn: 0.106 s [89% getDir2 content (11x)] (20x), RepositoryConfigService: 0.0201 s [98% getReadConfiguration (38x)] (54x)
2025-07-30 19:02:31,929 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616bfe0d104e_0_6616bfe0d104e_0_: finished. Total: 0.436 s, CPU [user: 0.157 s, system: 0.0178 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.293 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.256 s [54% getFile content (185x), 46% getDir2 content (21x)] (207x), GC: 0.025 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 19:02:31,930 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.76 s, CPU [user: 1.12 s, system: 0.191 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.33 s [50% getDir2 content (115x), 30% getFile content (807x)] (1141x), RepositoryConfigService: 1.25 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.482 s [84% Category (96x)] (117x)
2025-07-30 19:02:31,930 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.06 s [38% getDir2 content (115x), 35% getDatedRevision (362x), 23% getFile content (807x)] (1325x), RepositoryConfigService: 1.25 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.482 s [84% Category (96x)] (118x), ObjectMaps: 0.18 s [46% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 19:02:33,902 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5aff8b61-0a465820-339ae5bb-48e4c804] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.329 s, CPU [user: 0.187 s, system: 0.0351 s], Allocated memory: 42.1 MB, transactions: 2, PolarionAuthenticator: 0.281 s [100% authenticate (1x)] (1x)
2025-07-30 19:02:34,278 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5aff8daf-0a465820-339ae5bb-98341916] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.118 s, CPU [user: 0.0628 s, system: 0.0117 s], Allocated memory: 9.4 MB, transactions: 0
2025-07-30 19:02:34,304 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5aff8dc6-0a465820-339ae5bb-0bcc6f88] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753873354117': Total: 0.121 s, CPU [user: 0.0415 s, system: 0.00658 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-30 19:02:34,367 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5aff8dc6-0a465820-339ae5bb-11dc9b1d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753873354119': Total: 0.185 s, CPU [user: 0.0687 s, system: 0.0146 s], Allocated memory: 7.6 MB, transactions: 1, RepositoryConfigService: 0.088 s [100% getReadConfiguration (1x)] (1x), svn: 0.0125 s [62% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 19:02:34,416 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5aff8dc6-0a465820-339ae5bb-36a65417] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753873354118': Total: 0.233 s, CPU [user: 0.0561 s, system: 0.00958 s], Allocated memory: 5.3 MB, transactions: 1, RepositoryConfigService: 0.118 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 19:03:30,555 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5b004590-0a465820-339ae5bb-2b9b9780 | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/connections/test] INFO  TXLOGGER - Tx 6616c01a51853_0_6616c01a51853_0_: finished. Total: 0.181 s, CPU [user: 0.0368 s, system: 0.012 s], Allocated memory: 5.4 MB, svn: 0.12 s [94% endActivity (1x)] (5x), PullingJob Listeners: 0.0308 s [91% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0278 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0274 s [98% objectsCreated (1x)] (3x), DB: 0.0104 s [64% update (4x), 17% execute (1x)] (10x), processed revs: [208, 208 (delay 0s)], write: true
2025-07-30 19:03:30,558 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5b004590-0a465820-339ae5bb-2b9b9780] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 9.32 s, CPU [user: 1.95 s, system: 0.0822 s], Allocated memory: 16.2 MB, transactions: 1
2025-07-30 19:03:36,144 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5b007f61-0a465820-339ae5bb-af2cdf8c] INFO  TXLOGGER - Summary for 'servlet /polarion/svnwebclient/fileContent.jsp?url=WBSdev%2F.polarion%2Fsynchronizer%2Fconfiguration.xml': Total: 0.111 s, CPU [user: 0.042 s, system: 0.0196 s], Allocated memory: 3.1 MB, transactions: 1
2025-07-30 19:04:08,441 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5b00fd21-0a465820-339ae5bb-465a027f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxyInstance?projectId=WBSdev': Total: 0.215 s, CPU [user: 0.0952 s, system: 0.0334 s], Allocated memory: 22.1 MB, transactions: 0
2025-07-30 19:04:25,449 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.161 s, CPU [user: 0.00834 s, system: 0.00282 s], Allocated memory: 130.6 kB, transactions: 0, PullingJob: 0.0479 s [100% collectChanges (1x)] (1x), svn: 0.047 s [100% getLatestRevision (1x)] (1x)
2025-07-30 19:04:47,490 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5b016736-0a465820-339ae5bb-9f8d336a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 12.1 s, CPU [user: 0.404 s, system: 0.0299 s], Allocated memory: 1.2 MB, transactions: 0
2025-07-30 19:06:48,420 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5b034be1-0a465820-339ae5bb-a5a587a8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 8.96 s, CPU [user: 0.435 s, system: 0.0143 s], Allocated memory: 1.1 MB, transactions: 0
2025-07-30 19:07:50,209 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5b043266-0a465820-339ae5bb-9fe8362c | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/connections/test] INFO  TXLOGGER - Tx 6616c117e605c_0_6616c117e605c_0_: finished. Total: 0.168 s, CPU [user: 0.0202 s, system: 0.0207 s], Allocated memory: 3.3 MB, svn: 0.118 s [94% endActivity (1x)] (5x), PullingJob Listeners: 0.0364 s [96% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0348 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0348 s [100% objectsCreated (1x)] (3x), processed revs: [209, 209 (delay 0s)], write: true
2025-07-30 19:07:50,212 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5b043266-0a465820-339ae5bb-9fe8362c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 11.7 s, CPU [user: 0.442 s, system: 0.0681 s], Allocated memory: 7.7 MB, transactions: 1
2025-07-30 19:08:37,604 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5b04bbf4-0a465820-339ae5bb-cef05001 | u:admin | PUT:/polarion/synchronizer/rest/projects/WBSdev/connections/test] INFO  TXLOGGER - Tx 6616c14638464_0_6616c14638464_0_: finished. Total: 0.13 s, CPU [user: 0.0165 s, system: 0.00667 s], Allocated memory: 2.9 MB, svn: 0.0987 s [93% endActivity (1x)] (5x), PullingJob Listeners: 0.0194 s [95% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0182 s [100% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0181 s [100% objectsCreated (1x)] (3x), processed revs: [210, 210 (delay 0s)], write: true
2025-07-30 19:08:37,606 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5b04bbf4-0a465820-339ae5bb-cef05001] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 23.9 s, CPU [user: 0.425 s, system: 0.05 s], Allocated memory: 7.1 MB, transactions: 1
