2025-07-29 19:53:26,155 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:53:26,155 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:53:26,155 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:53:26,155 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:53:26,156 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:53:26,156 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:26,156 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:53:30,698 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:53:30,839 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.14 s. ]
2025-07-29 19:53:30,839 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:53:30,891 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0523 s. ]
2025-07-29 19:53:30,949 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:53:31,067 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 81 s. ]
2025-07-29 19:53:31,267 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.12 s. ]
2025-07-29 19:53:31,360 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:31,360 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:53:31,385 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-29 19:53:31,385 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:31,385 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:53:31,391 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-29 19:53:31,391 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-29 19:53:31,391 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-29 19:53:31,391 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-29 19:53:31,391 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-29 19:53:31,392 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-29 19:53:31,397 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:53:31,541 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:53:31,634 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:53:32,042 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.66 s. ]
2025-07-29 19:53:32,053 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,053 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:53:32,259 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:53:32,270 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-29 19:53:32,297 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,297 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:53:32,300 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:53:32,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:53:32,403 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:53:32,432 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:53:32,460 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:53:32,515 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:53:32,630 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:53:32,664 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:53:32,691 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:53:32,691 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-29 19:53:32,691 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,691 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:53:32,704 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:53:32,705 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,705 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:53:32,813 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:53:32,817 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:53:32,925 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 19:53:32,925 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,925 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:53:32,932 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:53:32,932 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:53:32,932 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
