2025-07-29 19:56:55,433 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:56:55,433 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:56:55,433 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:56:55,434 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:56:55,434 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:56:55,434 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:56:55,434 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:56:59,943 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:57:00,093 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.15 s. ]
2025-07-29 19:57:00,093 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:57:00,153 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0597 s. ]
2025-07-29 19:57:00,217 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:57:00,318 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 81 s. ]
2025-07-29 19:57:00,536 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.11 s. ]
2025-07-29 19:57:00,630 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:00,630 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:57:00,656 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-29 19:57:00,657 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:00,657 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-29 19:57:00,662 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-29 19:57:00,670 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:57:00,849 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:57:00,915 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:57:01,386 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-29 19:57:01,398 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:01,398 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:57:01,615 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:57:01,630 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 19:57:01,666 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:01,666 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:57:01,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:57:01,718 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:57:01,772 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:57:01,795 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:57:01,819 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:57:01,853 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:57:01,890 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:57:01,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:57:02,000 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:57:02,000 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.37 s. ]
2025-07-29 19:57:02,000 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:02,000 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:57:02,018 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-29 19:57:02,018 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:02,018 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:57:02,130 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:57:02,132 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:57:02,235 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 19:57:02,236 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:02,236 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:57:02,242 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:57:02,242 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:57:02,242 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
