2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:11,428 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 18:02:15,843 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 18:02:15,966 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.123 s. ]
2025-07-30 18:02:15,966 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 18:02:16,010 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0434 s. ]
2025-07-30 18:02:16,088 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 18:02:16,239 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 14 s. ]
2025-07-30 18:02:16,462 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.04 s. ]
2025-07-30 18:02:16,560 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:16,560 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 18:02:16,593 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 18:02:16,594 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:16,594 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 18:02:16,599 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 18:02:16,608 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 18:02:16,768 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 18:02:16,886 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 18:02:17,329 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 18:02:17,341 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:17,341 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 18:02:17,571 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 18:02:17,583 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 18:02:17,615 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:17,615 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 18:02:17,619 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 18:02:17,668 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 18:02:17,707 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 18:02:17,748 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 18:02:17,790 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 18:02:17,813 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 18:02:17,833 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 18:02:17,863 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 18:02:17,892 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 18:02:17,892 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.31 s. ]
2025-07-30 18:02:17,892 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:17,892 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 18:02:17,906 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 18:02:17,906 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:17,906 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 18:02:18,023 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 18:02:18,027 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 18:02:18,177 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-30 18:02:18,177 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:18,177 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 18:02:18,185 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 18:02:18,185 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 18:02:18,185 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 18:02:36,936 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 18.75 s. ]
2025-07-30 18:02:36,936 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 18:02:36,936 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 25.5 s. ]
2025-07-30 18:02:36,936 [main] INFO  com.polarion.platform.startup - ****************************************************************
