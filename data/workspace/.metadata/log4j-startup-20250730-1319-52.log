2025-07-30 13:19:53,060 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:19:53,060 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:19:53,060 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:19:53,061 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:19:53,061 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:19:53,061 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:53,061 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:19:57,260 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:19:57,404 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.144 s. ]
2025-07-30 13:19:57,405 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:19:57,445 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0404 s. ]
2025-07-30 13:19:57,495 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:19:57,600 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 13:19:57,805 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.75 s. ]
2025-07-30 13:19:57,887 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:57,887 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:19:57,917 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 13:19:57,918 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:57,918 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 13:19:57,922 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 13:19:57,929 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:19:58,081 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:19:58,139 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:19:58,651 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.73 s. ]
2025-07-30 13:19:58,663 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:58,663 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:19:58,898 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:19:58,912 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 13:19:58,937 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:58,937 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:19:58,941 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:19:58,996 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:19:59,046 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:19:59,094 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:19:59,127 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:19:59,149 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:19:59,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:19:59,192 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:19:59,229 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:19:59,229 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-30 13:19:59,229 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:59,229 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:19:59,243 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 13:19:59,243 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:59,243 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:19:59,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:19:59,350 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:19:59,475 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 13:19:59,476 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:59,476 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:19:59,483 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:19:59,483 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:59,483 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:20:01,967 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.48 s. ]
2025-07-30 13:20:01,967 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:20:01,967 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.91 s. ]
2025-07-30 13:20:01,967 [main] INFO  com.polarion.platform.startup - ****************************************************************
