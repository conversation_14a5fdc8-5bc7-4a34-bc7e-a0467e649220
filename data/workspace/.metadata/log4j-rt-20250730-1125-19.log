2025-07-30 11:25:27,415 [Catalina-utility-4] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 11:25:28,226 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 11:25:28,229 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:25:28,229 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:25:28,231 [Catalina-utility-4] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-30 11:25:28,235 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 11:25:28,236 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:25:28,236 [Catalina-utility-4] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-30 11:25:37,018 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 11:25:37,204 [ajp-nio-127.0.0.1-8889-exec-1 | cID:595d3404-0a465820-522e238c-11a44a8f] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 11:25:37,229 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 11:25:37,230 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
