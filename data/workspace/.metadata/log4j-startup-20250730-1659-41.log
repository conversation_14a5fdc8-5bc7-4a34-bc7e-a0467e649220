2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:41,483 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:59:45,856 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:59:45,995 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.139 s. ]
2025-07-30 16:59:45,996 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:59:46,071 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0751 s. ]
2025-07-30 16:59:46,129 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:59:46,254 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 16:59:46,482 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.0 s. ]
2025-07-30 16:59:46,569 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:46,569 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:59:46,598 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 16:59:46,598 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:46,598 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 16:59:46,603 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 16:59:46,611 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:59:46,779 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:59:46,901 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:59:47,376 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.78 s. ]
2025-07-30 16:59:47,389 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:47,389 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:59:47,619 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:59:47,632 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 16:59:47,659 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:47,659 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:59:47,663 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:59:47,725 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:59:47,770 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:59:47,811 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:59:47,842 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:59:47,870 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:59:47,897 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:59:47,935 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:59:47,963 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:59:47,964 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.33 s. ]
2025-07-30 16:59:47,964 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:47,965 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:59:47,982 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 16:59:47,982 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:47,982 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:59:48,088 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:59:48,093 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:59:48,216 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 16:59:48,216 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:48,216 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:59:48,224 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 16:59:48,224 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:59:48,224 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:59:51,052 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.83 s. ]
2025-07-30 16:59:51,052 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:59:51,052 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.57 s. ]
2025-07-30 16:59:51,052 [main] INFO  com.polarion.platform.startup - ****************************************************************
