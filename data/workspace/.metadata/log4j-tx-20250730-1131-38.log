2025-07-30 11:31:43,546 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0305 s [66% update (144x), 34% query (12x)] (221x), svn: 0.00897 s [49% getLatestRevision (2x), 40% testConnection (1x)] (4x)
2025-07-30 11:31:43,715 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0369 s [53% getDir2 content (2x), 39% info (3x)] (6x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.201 s, system: 0.26 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.245 s, system: 0.315 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.0864 s, system: 0.125 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.073 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0609 s [79% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.0828 s, system: 0.104 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0857 s [83% log2 (10x)] (13x), ObjectMaps: 0.0501 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.703 s, CPU [user: 0.0529 s, system: 0.0869 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0454 s [79% log2 (5x), 12% testConnection (1x)] (7x)
2025-07-30 11:31:44,422 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.139 s, system: 0.199 s], Allocated memory: 26.4 MB, transactions: 0, svn: 0.0853 s [29% log (1x), 25% info (5x), 24% log2 (5x), 12% getLatestRevision (2x)] (18x), ObjectMaps: 0.0682 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:31:44,423 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.485 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.311 s [63% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 11:31:44,700 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.238 s [100% getReadConfiguration (48x)] (48x), svn: 0.102 s [83% info (18x)] (38x)
2025-07-30 11:31:45,098 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.3 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.221 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:31:45,362 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.245 s [100% doFinishStartup (1x)] (1x), commit: 0.051 s [100% Revision (1x)] (1x), Lucene: 0.0331 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0168 s [100% objectsToInv (1x)] (1x), DB: 0.015 s [49% update (3x), 27% query (1x), 18% execute (1x)] (8x)
2025-07-30 11:31:50,252 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.362 s [88% info (158x)] (170x)
2025-07-30 11:31:50,569 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661658b95e444_0_661658b95e444_0_: finished. Total: 0.303 s, CPU [user: 0.141 s, system: 0.0182 s], Allocated memory: 21.0 MB, resolve: 0.0881 s [60% User (2x), 39% Project (1x)] (5x), Lucene: 0.045 s [100% search (1x)] (1x), ObjectMaps: 0.0274 s [55% getPrimaryObjectLocation (2x), 33% getPrimaryObjectProperty (2x)] (11x), svn: 0.0241 s [50% getLatestRevision (3x), 16% testConnection (1x), 15% log (1x)] (9x)
2025-07-30 11:31:51,108 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.759 s, CPU [user: 0.00626 s, system: 0.00158 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 11:31:51,108 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.317 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.101 s [64% User (3x), 33% Project (1x)] (7x), Lucene: 0.0607 s [74% search (1x), 14% add (1x)] (3x), ObjectMaps: 0.0301 s [59% getPrimaryObjectLocation (3x), 30% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.027 s [100% WorkItem (22x)] (22x), svn: 0.0241 s [50% getLatestRevision (3x), 16% testConnection (1x), 15% log (1x)] (9x)
2025-07-30 11:31:51,109 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.86 s, CPU [user: 0.164 s, system: 0.0255 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.659 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0592 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (23x)] (24x)
2025-07-30 11:31:51,459 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661658b95c040_0_661658b95c040_0_: finished. Total: 1.2 s, CPU [user: 0.3 s, system: 0.076 s], Allocated memory: 47.1 MB, svn: 0.7 s [46% getDatedRevision (181x), 38% getDir2 content (25x)] (307x), resolve: 0.321 s [100% Category (96x)] (96x), ObjectMaps: 0.123 s [43% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 11:31:51,690 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661658ba9b448_0_661658ba9b448_0_: finished. Total: 0.156 s, CPU [user: 0.0706 s, system: 0.0119 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0696 s [55% getReadUserConfiguration (10x), 45% getReadConfiguration (162x)] (172x), svn: 0.0682 s [57% info (19x), 37% getFile content (15x)] (36x), resolve: 0.0433 s [100% User (9x)] (9x), ObjectMaps: 0.019 s [64% getPrimaryObjectProperty (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 11:31:52,050 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661658bad104a_0_661658bad104a_0_: finished. Total: 0.302 s, CPU [user: 0.087 s, system: 0.0116 s], Allocated memory: 19.9 MB, svn: 0.241 s [74% getDir2 content (17x), 26% getFile content (44x)] (62x), RepositoryConfigService: 0.0946 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 11:31:52,868 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661658bb1c84b_0_661658bb1c84b_0_: finished. Total: 0.817 s, CPU [user: 0.352 s, system: 0.0223 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.612 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.455 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x)
2025-07-30 11:31:53,265 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661658bc05c4e_0_661658bc05c4e_0_: finished. Total: 0.281 s, CPU [user: 0.12 s, system: 0.00721 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.18 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.173 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x)
2025-07-30 11:31:53,265 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.01 s, CPU [user: 1.02 s, system: 0.142 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.79 s [44% getDir2 content (115x), 34% getFile content (807x), 18% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.03 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.394 s [81% Category (96x)] (117x), ObjectMaps: 0.153 s [46% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:31:53,265 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.45 s [40% getDatedRevision (362x), 32% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.03 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.394 s [81% Category (96x)] (118x), ObjectMaps: 0.153 s [46% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:31:56,203 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5962fb14-0a465820-1af03f1a-a46dea3b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.406 s, CPU [user: 0.225 s, system: 0.05 s], Allocated memory: 42.6 MB, transactions: 2, PolarionAuthenticator: 0.337 s [100% authenticate (1x)] (1x)
2025-07-30 11:31:56,555 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5962fd7e-0a465820-1af03f1a-c09af707] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.141 s, CPU [user: 0.0699 s, system: 0.0109 s], Allocated memory: 9.5 MB, transactions: 0, GC: 0.023 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:31:56,575 [ajp-nio-127.0.0.1-8889-exec-6 | cID:5962fda7-0a465820-1af03f1a-613cdadf] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753846316371': Total: 0.119 s, CPU [user: 0.0348 s, system: 0.00512 s], Allocated memory: 2.1 MB, transactions: 0, GC: 0.023 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:31:56,629 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5962fda9-0a465820-1af03f1a-d8056b1a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846316373': Total: 0.171 s, CPU [user: 0.0622 s, system: 0.0101 s], Allocated memory: 7.6 MB, transactions: 1, RepositoryConfigService: 0.0703 s [100% getReadConfiguration (1x)] (1x), GC: 0.023 s [100% G1 Young Generation (1x)] (1x), svn: 0.0156 s [72% testConnection (1x), 28% getFile content (2x)] (4x)
2025-07-30 11:31:56,643 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5962fda7-0a465820-1af03f1a-b577cb2c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846316372': Total: 0.186 s, CPU [user: 0.0376 s, system: 0.00435 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.0853 s [100% getReadConfiguration (1x)] (1x), GC: 0.023 s [100% G1 Young Generation (1x)] (1x)
