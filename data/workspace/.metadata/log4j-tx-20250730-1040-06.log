2025-07-30 10:40:11,927 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0507 s [66% update (144x), 33% query (12x)] (221x), svn: 0.0123 s [56% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-30 10:40:12,045 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0406 s [69% getDir2 content (2x), 26% info (3x)] (6x)
2025-07-30 10:40:12,812 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.762 s, CPU [user: 0.0562 s, system: 0.0813 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0638 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0483 s [79% log2 (5x), 11% testConnection (1x)] (7x)
2025-07-30 10:40:12,812 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.762 s, CPU [user: 0.211 s, system: 0.265 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.117 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:40:12,812 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.761 s, CPU [user: 0.0807 s, system: 0.0931 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.103 s [86% log2 (10x)] (13x), ObjectMaps: 0.0623 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:40:12,812 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.0859 s, system: 0.117 s], Allocated memory: 12.5 MB, transactions: 0, svn: 0.0761 s [80% log2 (10x)] (13x), ObjectMaps: 0.0696 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:40:12,813 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.76 s, CPU [user: 0.261 s, system: 0.327 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.135 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 10:40:12,812 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.762 s, CPU [user: 0.14 s, system: 0.194 s], Allocated memory: 26.1 MB, transactions: 0, svn: 0.103 s [27% log2 (5x), 27% log (1x), 21% info (5x), 14% getLatestRevision (2x)] (18x), ObjectMaps: 0.0768 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:40:12,813 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.525 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.368 s [65% log2 (36x), 13% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 10:40:12,957 [main | u:p] INFO  TXLOGGER - Tx 66164ce889801_0_66164ce889801_0_: finished. Total: 0.118 s, CPU [user: 0.0908 s, system: 0.00668 s], Allocated memory: 21.8 MB
2025-07-30 10:40:13,133 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.28 s [100% getReadConfiguration (48x)] (48x), svn: 0.0929 s [86% info (18x)] (38x)
2025-07-30 10:40:13,633 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.354 s [78% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.262 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.0208 s [100% objectsModified (8x)] (16x)
2025-07-30 10:40:13,881 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.109 s, CPU [user: 0.0281 s, system: 0.00753 s], Allocated memory: 10.7 MB
2025-07-30 10:40:14,025 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.363 s [100% doFinishStartup (1x)] (1x), commit: 0.0839 s [57% Revision (1x), 43% BuildArtifact (1x)] (2x), DB: 0.0481 s [52% update (45x), 21% execute (15x), 17% query (20x)] (122x), Lucene: 0.0445 s [100% refresh (2x)] (2x), resolve: 0.0225 s [83% BuildArtifact (11x)] (13x), SubterraURITable: 0.0219 s [100% addIfNotExistsDB (20x)] (20x)
2025-07-30 10:40:16,701 [main | u:p | u:p] INFO  TXLOGGER - Tx 66164cec3203f_0_66164cec3203f_0_: finished. Total: 0.117 s, CPU [user: 0.0691 s, system: 0.00966 s], Allocated memory: 7.0 MB
2025-07-30 10:40:17,513 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.531 s [89% info (158x)] (170x)
2025-07-30 10:40:18,187 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66164ced23444_0_66164ced23444_0_: finished. Total: 0.636 s, CPU [user: 0.199 s, system: 0.0281 s], Allocated memory: 20.8 MB, resolve: 0.241 s [85% User (2x)] (5x), ObjectMaps: 0.163 s [86% getPrimaryObjectLocation (2x)] (11x), Lucene: 0.0618 s [100% search (1x)] (1x)
2025-07-30 10:40:18,719 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.06 s, CPU [user: 0.00487 s, system: 0.00186 s], Allocated memory: 472.0 kB, transactions: 1
2025-07-30 10:40:18,719 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.06 s, CPU [user: 0.00531 s, system: 0.00171 s], Allocated memory: 489.1 kB, transactions: 1
2025-07-30 10:40:18,720 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 69, notification worker: 0.667 s [97% RevisionActivityCreator (18x)] (54x), resolve: 0.261 s [86% User (3x)] (7x), ObjectMaps: 0.166 s [87% getPrimaryObjectLocation (3x)] (12x), Lucene: 0.0922 s [67% search (1x), 18% refresh (2x)] (4x), persistence listener: 0.0476 s [88% indexRefreshPersistenceListener (9x)] (63x)
2025-07-30 10:40:18,721 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.23 s, CPU [user: 0.196 s, system: 0.0291 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.916 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0817 s [86% buildBaselineSnapshots (1x)] (24x)
2025-07-30 10:40:19,516 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164ced1e840_0_66164ced1e840_0_: finished. Total: 1.99 s, CPU [user: 0.432 s, system: 0.106 s], Allocated memory: 46.7 MB, svn: 1.4 s [53% getDatedRevision (181x), 36% getDir2 content (25x)] (307x), resolve: 0.468 s [100% Category (96x)] (96x), ObjectMaps: 0.186 s [39% getPrimaryObjectProperty (96x), 37% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 10:40:19,806 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164cef2c070_0_66164cef2c070_0_: finished. Total: 0.173 s, CPU [user: 0.0664 s, system: 0.0117 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0984 s [60% getReadUserConfiguration (10x), 40% getReadConfiguration (162x)] (172x), svn: 0.0968 s [68% info (19x), 27% getFile content (15x)] (36x), resolve: 0.0418 s [100% User (9x)] (9x), ObjectMaps: 0.0153 s [58% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 10:40:20,206 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164cef66472_0_66164cef66472_0_: finished. Total: 0.34 s, CPU [user: 0.0994 s, system: 0.0102 s], Allocated memory: 19.9 MB, svn: 0.27 s [54% getDir2 content (17x), 46% getFile content (44x)] (62x), RepositoryConfigService: 0.173 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 10:40:21,406 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164cefbb873_0_66164cefbb873_0_: finished. Total: 1.2 s, CPU [user: 0.491 s, system: 0.0594 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.928 s [96% getReadConfiguration (8682x)] (9021x), svn: 0.669 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-30 10:40:21,523 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164cf0e7874_0_66164cf0e7874_0_: finished. Total: 0.117 s, CPU [user: 0.0232 s, system: 0.00278 s], Allocated memory: 17.8 MB, svn: 0.106 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.02 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 10:40:21,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164cf10d476_0_66164cf10d476_0_: finished. Total: 0.299 s, CPU [user: 0.118 s, system: 0.00785 s], Allocated memory: 384.8 MB, svn: 0.197 s [52% getDir2 content (21x), 48% getFile content (185x)] (207x), RepositoryConfigService: 0.179 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 10:40:21,857 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.33 s, CPU [user: 1.31 s, system: 0.212 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.84 s [38% getDir2 content (115x), 32% getFile content (807x), 26% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.45 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.552 s [85% Category (96x)] (117x)
2025-07-30 10:40:21,857 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.77 s [44% getDatedRevision (362x), 29% getDir2 content (115x), 24% getFile content (807x)] (1325x), RepositoryConfigService: 1.45 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.552 s [85% Category (96x)] (118x), ObjectMaps: 0.215 s [40% getPrimaryObjectProperty (108x), 37% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 10:40:47,186 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.111 s, CPU [user: 0.00203 s, system: 0.00481 s], Allocated memory: 130.6 kB, transactions: 0, PullingJob: 0.106 s [100% collectChanges (1x)] (1x), svn: 0.105 s [100% getLatestRevision (1x)] (1x)
2025-07-30 10:40:56,115 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5934492c-0a465820-5ad0c9bc-bf52e8cb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.518 s, CPU [user: 0.228 s, system: 0.111 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.434 s [100% authenticate (1x)] (1x)
2025-07-30 10:40:56,466 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59344c01-0a465820-5ad0c9bc-d8b27b11] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.145 s, CPU [user: 0.0717 s, system: 0.0151 s], Allocated memory: 9.7 MB, transactions: 0
2025-07-30 10:40:56,466 [ajp-nio-127.0.0.1-8889-exec-5 | cID:59344c19-0a465820-5ad0c9bc-73fedecb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.12 s, CPU [user: 0.013 s, system: 0.00443 s], Allocated memory: 1.3 MB, transactions: 0
2025-07-30 10:40:56,490 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59344c1b-0a465820-5ad0c9bc-17c81be5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753843256285': Total: 0.143 s, CPU [user: 0.0407 s, system: 0.00798 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 10:40:56,527 [ajp-nio-127.0.0.1-8889-exec-8 | cID:59344c1b-0a465820-5ad0c9bc-bca80cdc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753843256287': Total: 0.179 s, CPU [user: 0.0604 s, system: 0.0133 s], Allocated memory: 7.1 MB, transactions: 1, RepositoryConfigService: 0.0783 s [100% getReadConfiguration (1x)] (1x), svn: 0.0177 s [62% testConnection (1x), 37% getFile content (2x)] (4x)
2025-07-30 10:40:56,558 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59344c1a-0a465820-5ad0c9bc-c52673cc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753843256286': Total: 0.21 s, CPU [user: 0.0369 s, system: 0.00568 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.0977 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 10:41:16,154 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.13076171875
