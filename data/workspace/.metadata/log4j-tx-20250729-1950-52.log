2025-07-29 19:50:57,256 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0545 s [60% query (12x), 40% update (144x)] (221x), svn: 0.0146 s [57% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-07-29 19:50:57,367 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0302 s [57% getDir2 content (2x), 37% info (3x)] (6x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.242 s, system: 0.314 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.134 s, system: 0.182 s], Allocated memory: 26.5 MB, transactions: 0, ObjectMaps: 0.0869 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0844 s [31% log2 (5x), 27% log (1x), 24% info (5x)] (18x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.205 s, system: 0.253 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.0791 s, system: 0.113 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.113 s [69% log2 (10x), 23% getLatestRevision (2x)] (13x), ObjectMaps: 0.0546 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.0477 s, system: 0.0617 s], Allocated memory: 6.9 MB, transactions: 0, ObjectMaps: 0.0509 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0449 s [76% log2 (5x), 14% testConnection (1x)] (7x)
2025-07-29 19:50:58,078 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.708 s, CPU [user: 0.0817 s, system: 0.129 s], Allocated memory: 12.3 MB, transactions: 0, ObjectMaps: 0.0891 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0724 s [82% log2 (10x)] (13x)
2025-07-29 19:50:58,079 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.536 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.354 s [62% log2 (36x), 15% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 19:50:58,310 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.197 s [100% getReadConfiguration (48x)] (48x), svn: 0.077 s [84% info (18x)] (38x)
2025-07-29 19:50:58,618 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.244 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.188 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:50:58,848 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.214 s [100% doFinishStartup (1x)] (1x), commit: 0.0413 s [100% Revision (1x)] (1x), Lucene: 0.0345 s [100% refresh (1x)] (1x), DB: 0.0179 s [44% query (1x), 32% update (3x), 15% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0139 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0116 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-29 19:51:01,338 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.303 s [91% info (158x)] (168x)
2025-07-29 19:51:02,163 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.75 s, CPU [user: 0.00542 s, system: 0.00133 s], Allocated memory: 531.4 kB, transactions: 1
2025-07-29 19:51:02,164 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, Lucene: 0.0279 s [88% add (1x)] (2x), Incremental Baseline: 0.0223 s [100% WorkItem (21x)] (21x), notification worker: 0.0204 s [51% RevisionActivityCreator (2x), 19% TestRunActivityCreator (1x), 17% WorkItemActivityCreator (1x)] (6x), resolve: 0.0178 s [84% User (1x)] (3x), persistence listener: 0.0164 s [83% indexRefreshPersistenceListener (1x)] (7x), PullingJob: 0.0104 s [100% collectChanges (1x)] (1x), svn: 0.0103 s [57% testConnection (1x), 43% getLatestRevision (1x)] (2x), ObjectMaps: 0.00322 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.00147 s [79% update (1x), 21% commit (1x)] (2x)
2025-07-29 19:51:02,164 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.829 s, CPU [user: 0.152 s, system: 0.0251 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.675 s [99% getDatedRevision (181x)] (183x)
2025-07-29 19:51:03,102 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615816378040_0_6615816378040_0_: finished. Total: 1.76 s, CPU [user: 0.437 s, system: 0.0937 s], Allocated memory: 54.1 MB, svn: 1.26 s [71% getDatedRevision (181x), 19% getDir2 content (25x)] (307x), resolve: 0.383 s [100% Category (96x)] (96x), ObjectMaps: 0.121 s [46% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (388x)
2025-07-29 19:51:03,322 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615816548848_0_6615816548848_0_: finished. Total: 0.119 s, CPU [user: 0.0552 s, system: 0.00935 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0548 s [53% getReadConfiguration (162x), 47% getReadUserConfiguration (10x)] (172x), svn: 0.0535 s [54% info (19x), 38% getFile content (16x)] (37x), resolve: 0.0374 s [100% User (9x)] (9x), ObjectMaps: 0.0195 s [52% getPrimaryObjectProperty (9x), 30% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 19:51:03,619 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581657144a_0_661581657144a_0_: finished. Total: 0.253 s, CPU [user: 0.0717 s, system: 0.00752 s], Allocated memory: 19.8 MB, svn: 0.207 s [74% getDir2 content (17x), 26% getFile content (44x)] (62x), RepositoryConfigService: 0.0824 s [99% getReadConfiguration (170x)] (192x)
2025-07-29 19:51:05,857 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66158165b0c4b_0_66158165b0c4b_0_: finished. Total: 2.24 s, CPU [user: 0.615 s, system: 0.0739 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.44 s [98% getReadConfiguration (8682x)] (9021x), svn: 1.41 s [52% getFile content (412x), 48% getDir2 content (21x)] (434x)
2025-07-29 19:51:05,959 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66158167e084c_0_66158167e084c_0_: finished. Total: 0.101 s, CPU [user: 0.0207 s, system: 0.00235 s], Allocated memory: 17.7 MB, svn: 0.0921 s [88% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0168 s [98% getReadConfiguration (124x)] (148x)
2025-07-29 19:51:06,256 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661581680104e_0_661581680104e_0_: finished. Total: 0.267 s, CPU [user: 0.109 s, system: 0.00669 s], Allocated memory: 382.3 MB, RepositoryConfigService: 0.172 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.17 s [54% getFile content (185x), 46% getDir2 content (20x)] (206x)
2025-07-29 19:51:06,256 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.91 s, CPU [user: 1.38 s, system: 0.206 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.27 s [39% getDir2 content (114x), 32% getFile content (809x), 27% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.81 s [97% getReadConfiguration (12019x)] (12691x), resolve: 0.478 s [80% Category (96x)] (117x)
2025-07-29 19:51:06,256 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 3.99 s [39% getDatedRevision (362x), 32% getDir2 content (114x), 26% getFile content (809x)] (1328x), RepositoryConfigService: 1.81 s [97% getReadConfiguration (12019x)] (12691x), resolve: 0.478 s [80% Category (96x)] (118x)
2025-07-29 19:51:09,592 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5605adac-7f000001-4f760fd8-bb597c89] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.108 s, CPU [user: 0.0451 s, system: 0.0268 s], Allocated memory: 10.0 MB, transactions: 0, PolarionAuthenticator: 0.0572 s [100% authenticate (1x)] (1x)
