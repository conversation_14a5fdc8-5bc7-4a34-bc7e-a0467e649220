2025-07-30 14:19:01,854 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0789 s [70% update (144x), 29% query (12x)] (221x), svn: 0.0271 s [62% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 14:19:02,032 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.042 s [53% getDir2 content (2x), 39% info (3x)] (6x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.149 s, system: 0.171 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.163 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.0871 s, system: 0.0713 s], Allocated memory: 10.8 MB, transactions: 0, svn: 0.15 s [84% log2 (10x)] (13x), ObjectMaps: 0.0558 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.325 s, system: 0.319 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.141 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.05 s, CPU [user: 0.276 s, system: 0.261 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.161 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.138 s, system: 0.109 s], Allocated memory: 14.7 MB, transactions: 0, svn: 0.216 s [49% log2 (10x), 18% info (5x), 15% getLatestRevision (3x)] (24x), ObjectMaps: 0.0989 s [99% getAllPrimaryObjects (2x)] (14x)
2025-07-30 14:19:03,083 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.04 s, CPU [user: 0.0613 s, system: 0.066 s], Allocated memory: 7.0 MB, transactions: 0, svn: 0.0636 s [51% log2 (5x), 31% getLatestRevision (1x)] (7x)
2025-07-30 14:19:03,083 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.67 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.541 s [63% log2 (36x), 17% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 14:19:03,260 [main | u:p] INFO  TXLOGGER - Tx 66167eff12c01_0_66167eff12c01_0_: finished. Total: 0.143 s, CPU [user: 0.106 s, system: 0.00629 s], Allocated memory: 21.8 MB
2025-07-30 14:19:03,460 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.324 s [100% getReadConfiguration (48x)] (48x), svn: 0.116 s [85% info (18x)] (38x)
2025-07-30 14:19:03,960 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.395 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.295 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 14:19:04,228 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.114 s, CPU [user: 0.0037 s, system: 0.00177 s], Allocated memory: 788.9 kB
2025-07-30 14:19:04,234 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.12 s, CPU [user: 0.0287 s, system: 0.00634 s], Allocated memory: 2.4 MB
2025-07-30 14:19:04,234 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.121 s, CPU [user: 0.0242 s, system: 0.00532 s], Allocated memory: 2.3 MB
2025-07-30 14:19:04,284 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.171 s, CPU [user: 0.0312 s, system: 0.0102 s], Allocated memory: 10.6 MB
2025-07-30 14:19:04,357 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.372 s [100% doFinishStartup (1x)] (1x), commit: 0.0839 s [100% Revision (1x)] (1x), Lucene: 0.0822 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0187 s [100% objectsToInv (1x)] (1x)
2025-07-30 14:19:09,289 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.756 s [89% info (158x)] (170x)
2025-07-30 14:19:09,799 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66167f051fc44_0_66167f051fc44_0_: finished. Total: 0.487 s, CPU [user: 0.149 s, system: 0.0234 s], Allocated memory: 21.5 MB, resolve: 0.234 s [84% User (2x)] (5x), ObjectMaps: 0.106 s [85% getPrimaryObjectLocation (2x)] (11x), GC: 0.073 s [100% G1 Young Generation (1x)] (1x), svn: 0.0477 s [46% getLatestRevision (2x), 20% testConnection (1x), 15% getFile content (2x)] (8x), Lucene: 0.0437 s [100% search (1x)] (1x)
2025-07-30 14:19:10,709 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.28 s, CPU [user: 0.00709 s, system: 0.00156 s], Allocated memory: 316.1 kB, transactions: 1, GC: 0.073 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 14:19:10,712 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.515 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.261 s [82% User (3x)] (7x), ObjectMaps: 0.112 s [86% getPrimaryObjectLocation (3x)] (12x), Lucene: 0.0615 s [71% search (1x), 19% add (1x)] (3x), svn: 0.0549 s [53% getLatestRevision (3x), 17% testConnection (1x), 13% getFile content (2x)] (9x), Incremental Baseline: 0.0441 s [100% WorkItem (22x)] (22x), persistence listener: 0.0259 s [68% indexRefreshPersistenceListener (1x), 15% RevisionActivityCreator (2x)] (7x)
2025-07-30 14:19:10,713 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.43 s, CPU [user: 0.206 s, system: 0.0366 s], Allocated memory: 18.4 MB, transactions: 24, svn: 1.04 s [98% getDatedRevision (181x)] (183x), Lucene: 0.174 s [83% buildBaselineSnapshots (1x)] (24x), GC: 0.073 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 14:19:11,299 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f051c840_0_66167f051c840_0_: finished. Total: 2 s, CPU [user: 0.426 s, system: 0.112 s], Allocated memory: 46.6 MB, svn: 1.19 s [46% getDatedRevision (181x), 37% getDir2 content (25x)] (307x), resolve: 0.65 s [100% Category (96x)] (96x), ObjectMaps: 0.234 s [45% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 14:19:11,637 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f071f847_0_66167f071f847_0_: finished. Total: 0.278 s, CPU [user: 0.0292 s, system: 0.0109 s], Allocated memory: 3.1 MB, resolve: 0.248 s [100% Project (5x)] (5x), svn: 0.202 s [75% log (1x), 20% info (4x)] (10x), ObjectMaps: 0.169 s [93% getPrimaryObjectProperty (4x)] (17x), GlobalHandler: 0.0216 s [73% applyTxChanges (1x), 27% get (5x)] (6x)
2025-07-30 14:19:11,947 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f0765448_0_66167f0765448_0_: finished. Total: 0.309 s, CPU [user: 0.0896 s, system: 0.0181 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.118 s [54% getReadConfiguration (162x), 46% getReadUserConfiguration (10x)] (172x), svn: 0.101 s [61% info (19x), 32% getFile content (15x)] (36x), resolve: 0.0563 s [100% User (9x)] (9x), Lucene: 0.0468 s [100% search (1x)] (1x), ObjectMaps: 0.0238 s [58% getPrimaryObjectProperty (8x), 22% getLastPromoted (8x), 20% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 14:19:12,065 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f07b2c49_0_66167f07b2c49_0_: finished. Total: 0.118 s, CPU [user: 0.0433 s, system: 0.00691 s], Allocated memory: 3.8 MB, RepositoryConfigService: 0.0827 s [95% getReadConfiguration (54x)] (77x), svn: 0.043 s [100% getFile content (12x)] (13x)
2025-07-30 14:19:12,385 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f07d044a_0_66167f07d044a_0_: finished. Total: 0.319 s, CPU [user: 0.0868 s, system: 0.0155 s], Allocated memory: 19.9 MB, svn: 0.252 s [69% getDir2 content (17x), 31% getFile content (44x)] (62x), RepositoryConfigService: 0.117 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 14:19:14,204 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f082044b_0_66167f082044b_0_: finished. Total: 1.82 s, CPU [user: 0.613 s, system: 0.121 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.24 s [93% getReadConfiguration (8682x)] (9021x), svn: 1.03 s [55% getFile content (412x), 45% getDir2 content (21x)] (434x)
2025-07-30 14:19:14,327 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f09e704c_0_66167f09e704c_0_: finished. Total: 0.122 s, CPU [user: 0.027 s, system: 0.00336 s], Allocated memory: 17.8 MB, svn: 0.11 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0223 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 14:19:14,837 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167f0a1684e_0_66167f0a1684e_0_: finished. Total: 0.443 s, CPU [user: 0.173 s, system: 0.0135 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.309 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.271 s [60% getFile content (185x), 40% getDir2 content (21x)] (207x)
2025-07-30 14:19:14,837 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 5.54 s, CPU [user: 1.53 s, system: 0.308 s], Allocated memory: 1.6 GB, transactions: 10, svn: 3.29 s [41% getDir2 content (115x), 33% getFile content (807x), 17% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.95 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.956 s [68% Category (96x), 26% Project (6x)] (117x), ObjectMaps: 0.426 s [65% getPrimaryObjectProperty (108x), 20% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 14:19:14,838 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 4.34 s [36% getDatedRevision (362x), 31% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.95 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.957 s [68% Category (96x), 26% Project (6x)] (118x), ObjectMaps: 0.426 s [65% getPrimaryObjectProperty (108x), 20% getPrimaryObjectLocation (114x)] (442x), Lucene: 0.265 s [54% buildBaselineSnapshots (2x), 32% search (5x)] (54x)
2025-07-30 14:19:20,348 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59fc3cf4-0a465820-74a65d54-d69e6254] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.677 s, CPU [user: 0.289 s, system: 0.118 s], Allocated memory: 43.0 MB, transactions: 2, PolarionAuthenticator: 0.539 s [100% authenticate (1x)] (1x)
2025-07-30 14:19:20,536 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59fc3fe3-0a465820-74a65d54-d3fd6b6a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/lib/bootstrap/css/bootstrap.css': Total: 0.114 s, CPU [user: 0.00299 s, system: 0.00133 s], Allocated memory: 167.1 kB, transactions: 0, PolarionAuthenticator: 0.0331 s [100% authenticate (1x)] (1x), interceptor: 0.0218 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
2025-07-30 14:19:20,710 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59fc4015-0a465820-74a65d54-07820b29] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/synchronizer.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.177 s, CPU [user: 0.0261 s, system: 0.00841 s], Allocated memory: 4.4 MB, transactions: 0, UICustomizationDataProvider: 0.0838 s [100% getUserDataValue (1x)] (1x), LocalUsersDataFileStorage: 0.0189 s [100% readUserData (1x)] (1x)
2025-07-30 14:19:21,154 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59fc420e-0a465820-74a65d54-f768c779] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.178 s, CPU [user: 0.0186 s, system: 0.00553 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 14:19:21,155 [ajp-nio-127.0.0.1-8889-exec-3 | cID:59fc41b0-0a465820-74a65d54-27392657] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.273 s, CPU [user: 0.103 s, system: 0.0271 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 14:19:21,212 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59fc420e-0a465820-74a65d54-00554907] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753856360837': Total: 0.232 s, CPU [user: 0.054 s, system: 0.0112 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 14:19:21,278 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59fc420f-0a465820-74a65d54-0a96dc25 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753856360839] INFO  TXLOGGER - Tx 66167f10a9451_0_66167f10a9451_0_: finished. Total: 0.153 s, CPU [user: 0.0642 s, system: 0.0137 s], Allocated memory: 5.6 MB, svn: 0.0229 s [78% testConnection (1x), 21% getFile content (2x)] (4x)
2025-07-30 14:19:21,286 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59fc420f-0a465820-74a65d54-0a96dc25] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753856360839': Total: 0.309 s, CPU [user: 0.0887 s, system: 0.0208 s], Allocated memory: 8.4 MB, transactions: 1, RepositoryConfigService: 0.154 s [100% getReadConfiguration (1x)] (1x), svn: 0.0229 s [78% testConnection (1x), 21% getFile content (2x)] (4x)
2025-07-30 14:19:21,328 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59fc4210-0a465820-74a65d54-5eccb6ca] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753856360838': Total: 0.352 s, CPU [user: 0.0476 s, system: 0.00905 s], Allocated memory: 5.3 MB, transactions: 1, RepositoryConfigService: 0.173 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 14:19:25,524 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.121 s, CPU [user: 0.0027 s, system: 0.00463 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.0709 s [100% collectChanges (1x)] (1x), svn: 0.0704 s [100% getLatestRevision (1x)] (1x)
2025-07-30 14:19:29,628 [ajp-nio-127.0.0.1-8889-exec-9 | cID:59fc634e-0a465820-74a65d54-c91b658e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753856360840': Total: 0.131 s, CPU [user: 0.0134 s, system: 0.0411 s], Allocated memory: 1.3 MB, transactions: 0, PolarionAuthenticator: 0.0109 s [100% authenticate (1x)] (1x)
2025-07-30 14:19:42,144 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59fc9334-0a465820-74a65d54-e2c48df5 | u:admin | POST:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs] INFO  TXLOGGER - Tx 66167f24f4c53_0_66167f24f4c53_0_: finished. Total: 0.236 s, CPU [user: 0.0465 s, system: 0.0287 s], Allocated memory: 5.4 MB, svn: 0.155 s [92% endActivity (1x)] (5x), PullingJob Listeners: 0.0366 s [93% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0339 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0336 s [99% objectsCreated (1x)] (3x), DB: 0.0127 s [60% update (4x), 30% query (1x)] (10x), SubterraURITable: 0.0124 s [100% addIfNotExistsDB (1x)] (1x), processed revs: [207, 207 (delay 0s)], write: true
2025-07-30 14:19:42,146 [ajp-nio-127.0.0.1-8889-exec-4 | cID:59fc9334-0a465820-74a65d54-e2c48df5] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs': Total: 0.396 s, CPU [user: 0.101 s, system: 0.0843 s], Allocated memory: 10.0 MB, transactions: 1, svn: 0.175 s [81% endActivity (1x)] (8x), PullingJob Listeners: 0.0366 s [93% StorageListener (1x)] (16x), RevisionsPersistenceModule Listeners: 0.0339 s [99% PersistenceEngineListener (1x)] (3x), PersistenceEngineListener: 0.0336 s [99% objectsCreated (1x)] (3x)
2025-07-30 14:32:53,407 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.125 s, CPU [user: 0.00276 s, system: 0.0149 s], Allocated memory: 129.9 kB, transactions: 0, PullingJob: 0.114 s [100% collectChanges (1x)] (1x), svn: 0.113 s [100% getLatestRevision (1x)] (1x)
