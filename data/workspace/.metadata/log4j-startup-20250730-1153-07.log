2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:07,971 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:53:12,086 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:53:12,233 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.147 s. ]
2025-07-30 11:53:12,233 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:53:12,301 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0682 s. ]
2025-07-30 11:53:12,356 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:53:12,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 45 s. ]
2025-07-30 11:53:12,738 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.77 s. ]
2025-07-30 11:53:12,823 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:12,824 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:53:12,866 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 11:53:12,866 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:12,866 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 11:53:12,874 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 11:53:12,879 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 11:53:13,011 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:53:13,115 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:53:13,570 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-07-30 11:53:13,580 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:13,580 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:53:13,787 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:53:13,810 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 11:53:13,845 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:13,845 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:53:13,851 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:53:13,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:53:13,949 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 11:53:13,995 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 11:53:14,031 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 11:53:14,061 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 11:53:14,088 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 11:53:14,131 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:53:14,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:53:14,166 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.36 s. ]
2025-07-30 11:53:14,166 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:14,166 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:53:14,179 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:53:14,179 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:14,179 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:53:14,278 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:53:14,281 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:53:14,394 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.21 s. ]
2025-07-30 11:53:14,394 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:14,394 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:53:14,401 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:53:14,402 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:53:14,402 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:53:16,777 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.38 s. ]
2025-07-30 11:53:16,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:53:16,777 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.81 s. ]
2025-07-30 11:53:16,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
