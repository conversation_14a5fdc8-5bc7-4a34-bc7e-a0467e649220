2025-07-29 19:17:05,303 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0662 s [73% update (144x), 27% query (12x)] (221x), svn: 0.012 s [62% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-29 19:17:05,428 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0386 s [65% getDir2 content (2x), 27% info (3x)] (6x)
2025-07-29 19:17:06,168 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.736 s, CPU [user: 0.235 s, system: 0.367 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.126 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:17:06,168 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.048 s, system: 0.109 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0637 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:17:06,168 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.199 s, system: 0.308 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.125 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:17:06,169 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0805 s, system: 0.127 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.084 s [81% log2 (10x)] (13x), ObjectMaps: 0.0544 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:17:06,169 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.132 s, system: 0.24 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.0868 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0742 s [29% log2 (5x), 26% log (1x), 24% info (5x), 9% getLatestRevision (2x)] (18x)
2025-07-29 19:17:06,169 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0858 s, system: 0.161 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0932 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0648 s [78% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-29 19:17:06,169 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.549 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.296 s [63% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-29 19:17:06,403 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.201 s [100% getReadConfiguration (48x)] (48x), svn: 0.0704 s [83% info (18x)] (38x)
2025-07-29 19:17:06,731 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.258 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.199 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:17:06,957 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.105 s, CPU [user: 0.0236 s, system: 0.00721 s], Allocated memory: 10.5 MB
2025-07-29 19:17:06,988 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.242 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0476 s [100% refresh (1x)] (1x), commit: 0.0388 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0172 s [100% objectsToInv (1x)] (1x)
2025-07-29 19:17:22,744 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.341 s [77% info (158x), 14% getLatestRevision (6x)] (174x), PullingJob: 0.0467 s [100% collectChanges (5x)] (5x)
2025-07-29 19:17:23,697 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.859 s, CPU [user: 0.00724 s, system: 0.00247 s], Allocated memory: 531.1 kB, transactions: 1, GC: 0.046 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 19:17:23,697 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, resolve: 0.0258 s [68% User (1x), 32% Revision (2x)] (3x), Incremental Baseline: 0.0256 s [100% WorkItem (21x)] (21x), notification worker: 0.0188 s [73% RevisionActivityCreator (2x), 15% WorkItemActivityCreator (1x)] (6x), persistence listener: 0.0176 s [75% indexRefreshPersistenceListener (1x), 10% WorkItemActivityCreator (1x)] (7x), Lucene: 0.0154 s [65% add (1x), 35% refresh (1x)] (2x), EHCache: 0.00351 s [99% GET (15x)] (36x), GlobalHandler: 0.00338 s [66% get (3x), 34% applyTxChanges (2x)] (5x), ObjectMaps: 0.00304 s [100% getPrimaryObjectLocation (1x)] (1x), DB: 0.00133 s [54% update (1x), 46% commit (1x)] (2x)
2025-07-29 19:17:23,698 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.964 s, CPU [user: 0.163 s, system: 0.0263 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.771 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0505 s [64% buildBaselineSnapshots (1x), 36% buildBaseline (22x)] (23x)
2025-07-29 19:17:24,131 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661579b02fc42_0_661579b02fc42_0_: finished. Total: 1.38 s, CPU [user: 0.441 s, system: 0.097 s], Allocated memory: 55.5 MB, svn: 0.826 s [49% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.431 s [100% Category (96x)] (96x), ObjectMaps: 0.147 s [39% getPrimaryObjectLocation (96x), 37% getPrimaryObjectProperty (96x), 24% getLastPromoted (96x)] (388x)
2025-07-29 19:17:24,321 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661579b19c048_0_661579b19c048_0_: finished. Total: 0.113 s, CPU [user: 0.0546 s, system: 0.0092 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0499 s [51% getReadUserConfiguration (10x), 49% getReadConfiguration (162x)] (172x), svn: 0.0463 s [58% info (19x), 36% getFile content (16x)] (37x), resolve: 0.0363 s [100% User (9x)] (9x), ObjectMaps: 0.0198 s [42% getPrimaryObjectLocation (9x), 41% getPrimaryObjectProperty (9x)] (37x)
2025-07-29 19:17:24,514 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661579b1c0c4a_0_661579b1c0c4a_0_: finished. Total: 0.159 s, CPU [user: 0.054 s, system: 0.00455 s], Allocated memory: 19.9 MB, svn: 0.121 s [66% getDir2 content (17x), 34% getFile content (44x)] (62x), RepositoryConfigService: 0.0617 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 19:17:25,427 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661579b1e8c4b_0_661579b1e8c4b_0_: finished. Total: 0.912 s, CPU [user: 0.414 s, system: 0.0205 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.732 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.461 s [73% getFile content (412x), 27% getDir2 content (21x)] (434x)
2025-07-29 19:17:25,813 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661579b2ee84e_0_661579b2ee84e_0_: finished. Total: 0.251 s, CPU [user: 0.107 s, system: 0.00618 s], Allocated memory: 384.7 MB, svn: 0.159 s [52% getDir2 content (20x), 47% getFile content (185x)] (206x), RepositoryConfigService: 0.152 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-29 19:17:25,813 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.06 s, CPU [user: 1.16 s, system: 0.149 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.77 s [39% getDir2 content (114x), 35% getFile content (809x), 23% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.06 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.505 s [85% Category (96x)] (117x), ObjectMaps: 0.178 s [39% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 19:17:25,814 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.55 s [46% getDatedRevision (362x), 27% getDir2 content (114x), 25% getFile content (809x)] (1328x), RepositoryConfigService: 1.06 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.506 s [85% Category (96x)] (118x), ObjectMaps: 0.178 s [39% getPrimaryObjectProperty (110x), 38% getPrimaryObjectLocation (116x), 22% getLastPromoted (110x)] (452x)
2025-07-29 19:17:35,976 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55e6f2e1-0a465820-6210d7de-81203702] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.39 s, CPU [user: 0.208 s, system: 0.0527 s], Allocated memory: 42.3 MB, transactions: 2, PolarionAuthenticator: 0.339 s [100% authenticate (1x)] (1x)
2025-07-29 19:17:36,291 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55e6f52a-0a465820-6210d7de-28cbd6e6] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.121 s, CPU [user: 0.0606 s, system: 0.0115 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 19:17:36,312 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55e6f54a-0a465820-6210d7de-27daf72f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753787856142': Total: 0.109 s, CPU [user: 0.0343 s, system: 0.00593 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-29 19:17:36,353 [ajp-nio-127.0.0.1-8889-exec-8 | cID:55e6f54b-0a465820-6210d7de-fe473710] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753787856143': Total: 0.15 s, CPU [user: 0.0486 s, system: 0.0125 s], Allocated memory: 8.2 MB, transactions: 1, RepositoryConfigService: 0.0741 s [100% getReadConfiguration (1x)] (1x), svn: 0.0105 s [82% testConnection (1x)] (3x)
2025-07-29 19:17:36,363 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55e6f54b-0a465820-6210d7de-582f3109] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753787856144': Total: 0.16 s, CPU [user: 0.0213 s, system: 0.0041 s], Allocated memory: 2.7 MB, transactions: 1, RepositoryConfigService: 0.0871 s [100% getReadConfiguration (1x)] (1x)
