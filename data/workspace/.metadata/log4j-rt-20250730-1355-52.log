2025-07-30 13:56:00,073 [Catalina-utility-6] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 13:56:00,647 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 13:56:00,648 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 13:56:00,648 [Catalina-utility-6] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-30 13:56:00,649 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 13:56:00,651 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 13:56:00,652 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 13:56:00,654 [Catalina-utility-6] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-30 13:56:01,501 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 13:56:01,611 [ajp-nio-127.0.0.1-8889-exec-1 | cID:59e6e7b5-0a465820-0e967e4e-20c17b28] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 13:56:01,630 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 13:56:01,630 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
