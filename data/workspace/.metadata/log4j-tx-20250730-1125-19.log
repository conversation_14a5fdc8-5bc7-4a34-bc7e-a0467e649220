2025-07-30 11:25:24,214 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0597 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0111 s [53% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-30 11:25:24,325 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0335 s [58% getDir2 content (2x), 34% info (3x)] (6x)
2025-07-30 11:25:25,156 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.274 s, system: 0.319 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:25:25,156 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.0966 s, system: 0.124 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.131 s [88% log2 (10x)] (13x), ObjectMaps: 0.0852 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:25:25,156 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.229 s, system: 0.264 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.12 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:25:25,157 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.0881 s, system: 0.109 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.156 s [80% log2 (10x)] (13x), ObjectMaps: 0.0523 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:25:25,157 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.0595 s, system: 0.0842 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0874 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0552 s [61% log2 (5x), 27% testConnection (1x)] (7x)
2025-07-30 11:25:25,157 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.828 s, CPU [user: 0.164 s, system: 0.195 s], Allocated memory: 26.4 MB, transactions: 0, ObjectMaps: 0.0887 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0878 s [27% log2 (5x), 27% info (5x), 20% log (1x), 11% testConnection (1x)] (18x)
2025-07-30 11:25:25,157 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.555 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.473 s [69% log2 (36x), 12% testConnection (6x)] (61x)
2025-07-30 11:25:25,374 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.182 s [100% getReadConfiguration (48x)] (48x), svn: 0.0742 s [83% info (18x)] (38x)
2025-07-30 11:25:25,649 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.21 s [78% info (94x), 14% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.164 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:25:25,876 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.124 s, CPU [user: 0.0316 s, system: 0.00924 s], Allocated memory: 10.5 MB, GC: 0.018 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:25:25,921 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.258 s [100% doFinishStartup (1x)] (1x), commit: 0.0888 s [100% Revision (1x)] (1x), Lucene: 0.0328 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0137 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:25:37,291 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.783 s [90% info (158x)] (172x)
2025-07-30 11:25:37,975 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616574d2d045_0_6616574d2d045_0_: finished. Total: 0.643 s, CPU [user: 0.18 s, system: 0.0245 s], Allocated memory: 20.8 MB, resolve: 0.257 s [56% Project (1x), 42% User (2x)] (5x), currentDateQueryExpander: 0.0681 s [100% expand (1x)] (2x), svn: 0.0589 s [56% getLatestRevision (3x), 14% log (1x), 12% info (1x)] (9x), ObjectMaps: 0.0435 s [46% getPrimaryObjectLocation (2x), 44% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 11:25:38,543 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.00699 s, system: 0.00309 s], Allocated memory: 528.8 kB, transactions: 1
2025-07-30 11:25:38,548 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.652 s [100% RevisionActivityCreator (2x)] (6x), resolve: 0.378 s [60% User (3x), 38% Project (1x)] (7x), svn: 0.0694 s [63% getLatestRevision (4x), 12% log (1x), 10% info (1x)] (10x), currentDateQueryExpander: 0.0681 s [100% expand (1x)] (2x), ObjectMaps: 0.0581 s [60% getPrimaryObjectLocation (3x), 33% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0559 s [50% search (1x), 28% add (1x), 21% refresh (1x)] (3x), persistence listener: 0.0495 s [77% indexRefreshPersistenceListener (1x), 9% PlanActivityCreator (1x)] (7x)
2025-07-30 11:25:38,548 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.26 s, CPU [user: 0.188 s, system: 0.0304 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.88 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0687 s [81% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:25:38,942 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616574d28840_0_6616574d28840_0_: finished. Total: 1.63 s, CPU [user: 0.376 s, system: 0.0987 s], Allocated memory: 46.6 MB, svn: 0.957 s [45% getDir2 content (25x), 39% getDatedRevision (181x)] (307x), resolve: 0.513 s [100% Category (96x)] (96x), ObjectMaps: 0.194 s [41% getPrimaryObjectProperty (96x), 37% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 11:25:39,174 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616574ed3048_0_6616574ed3048_0_: finished. Total: 0.153 s, CPU [user: 0.0712 s, system: 0.0139 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.0655 s [50% getReadUserConfiguration (10x), 50% getReadConfiguration (162x)] (172x), svn: 0.0633 s [57% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0446 s [100% User (9x)] (9x), ObjectMaps: 0.0203 s [61% getPrimaryObjectProperty (8x), 25% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:25:39,642 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616574f0fc4a_0_6616574f0fc4a_0_: finished. Total: 0.379 s, CPU [user: 0.101 s, system: 0.0146 s], Allocated memory: 19.9 MB, svn: 0.297 s [72% getDir2 content (17x), 27% getFile content (44x)] (62x), RepositoryConfigService: 0.14 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 11:25:40,652 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616574f6ec4b_0_6616574f6ec4b_0_: finished. Total: 1.01 s, CPU [user: 0.39 s, system: 0.0526 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.773 s [98% getReadConfiguration (8682x)] (9021x), svn: 0.565 s [68% getFile content (412x), 32% getDir2 content (21x)] (434x), GC: 0.06 s [100% G1 Young Generation (4x)] (4x)
2025-07-30 11:25:41,105 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657508bc4e_0_661657508bc4e_0_: finished. Total: 0.322 s, CPU [user: 0.134 s, system: 0.00881 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.204 s [95% getReadConfiguration (2787x)] (3025x), svn: 0.202 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x)
2025-07-30 11:25:41,106 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.8 s, CPU [user: 1.17 s, system: 0.203 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.26 s [45% getDir2 content (115x), 35% getFile content (807x)] (1141x), RepositoryConfigService: 1.27 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.586 s [87% Category (96x)] (117x), ObjectMaps: 0.223 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 11:25:41,106 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.14 s [39% getDatedRevision (362x), 33% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.27 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.587 s [87% Category (96x)] (118x), ObjectMaps: 0.223 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 11:25:44,008 [ajp-nio-127.0.0.1-8889-exec-2 | cID:595d4cee-0a465820-522e238c-fd98e20b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.473 s, CPU [user: 0.235 s, system: 0.0469 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.415 s [100% authenticate (1x)] (1x)
2025-07-30 11:25:44,328 [ajp-nio-127.0.0.1-8889-exec-4 | cID:595d4f8d-0a465820-522e238c-994d8fa3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.123 s, CPU [user: 0.06 s, system: 0.012 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 11:25:44,387 [ajp-nio-127.0.0.1-8889-exec-6 | cID:595d4fb7-0a465820-522e238c-4d4e16ae] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753845944174': Total: 0.14 s, CPU [user: 0.056 s, system: 0.0115 s], Allocated memory: 9.9 MB, transactions: 1, RepositoryConfigService: 0.0722 s [100% getReadConfiguration (1x)] (1x), svn: 0.0102 s [64% testConnection (1x), 36% getFile content (2x)] (4x)
2025-07-30 11:25:44,404 [ajp-nio-127.0.0.1-8889-exec-8 | cID:595d4fba-0a465820-522e238c-639e74d7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753845944173': Total: 0.153 s, CPU [user: 0.0267 s, system: 0.00359 s], Allocated memory: 4.4 MB, transactions: 1, RepositoryConfigService: 0.0886 s [100% getReadConfiguration (1x)] (1x)
