2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:31:05,907 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:05,908 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:31:10,426 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:31:10,577 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.15 s. ]
2025-07-30 10:31:10,577 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:31:10,640 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.063 s. ]
2025-07-30 10:31:10,718 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:31:10,868 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 100 s. ]
2025-07-30 10:31:11,081 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.18 s. ]
2025-07-30 10:31:11,177 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:11,177 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:31:11,206 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 10:31:11,207 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:11,207 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-30 10:31:11,212 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 10:31:11,220 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:31:11,355 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:31:11,458 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:31:11,912 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-30 10:31:11,927 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:11,927 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:31:12,155 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:31:12,165 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 10:31:12,190 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:12,190 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:31:12,194 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:31:12,258 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:31:12,309 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:31:12,330 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:31:12,344 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:31:12,370 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:31:12,402 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:31:12,429 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:31:12,457 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:31:12,457 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-30 10:31:12,457 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:12,457 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:31:12,472 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:31:12,472 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:12,473 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:31:12,593 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:31:12,597 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:31:12,724 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 10:31:12,724 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:12,724 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:31:12,732 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:31:12,732 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:31:12,732 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:31:16,350 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.62 s. ]
2025-07-30 10:31:16,351 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:31:16,351 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.4 s. ]
2025-07-30 10:31:16,351 [main] INFO  com.polarion.platform.startup - ****************************************************************
