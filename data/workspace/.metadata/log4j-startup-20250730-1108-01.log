2025-07-30 11:08:01,347 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:08:01,347 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:08:01,347 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:08:01,347 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:08:01,348 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:08:01,348 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:01,348 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:08:06,467 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:08:06,734 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.267 s. ]
2025-07-30 11:08:06,734 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:08:06,815 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0809 s. ]
2025-07-30 11:08:06,885 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:08:07,028 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 70 s. ]
2025-07-30 11:08:07,255 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.91 s. ]
2025-07-30 11:08:07,366 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:07,366 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:08:07,400 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-07-30 11:08:07,400 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:07,400 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 11:08:07,406 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 11:08:07,420 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:08:07,556 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:08:07,674 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:08:08,159 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.76 s. ]
2025-07-30 11:08:08,174 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:08,174 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:08:08,478 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:08:08,492 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 11:08:08,521 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:08,521 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:08:08,526 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:08:08,581 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:08:08,636 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:08:08,663 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:08:08,689 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:08:08,726 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:08:08,766 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:08:08,806 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:08:08,833 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:08:08,833 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.34 s. ]
2025-07-30 11:08:08,833 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:08,833 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:08:08,847 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:08:08,848 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:08,848 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:08:08,983 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:08:08,990 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:08:09,223 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.38 s. ]
2025-07-30 11:08:09,223 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:09,223 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:08:09,236 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:08:09,236 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:08:09,236 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:08:11,932 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.7 s. ]
2025-07-30 11:08:11,932 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:08:11,932 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.6 s. ]
2025-07-30 11:08:11,932 [main] INFO  com.polarion.platform.startup - ****************************************************************
