2025-07-30 10:34:48,125 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0563 s [69% update (144x), 30% query (12x)] (221x), svn: 0.0154 s [57% getLatestRevision (2x), 36% testConnection (1x)] (4x)
2025-07-30 10:34:48,274 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0389 s [56% getDir2 content (2x), 38% info (3x)] (6x)
2025-07-30 10:34:49,047 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.11 s, system: 0.204 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0721 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 10:34:49,047 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.0469 s, system: 0.0859 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0616 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:34:49,047 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.0937 s, system: 0.111 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.162 s [52% log2 (10x), 16% info (5x), 14% log (1x)] (24x), ObjectMaps: 0.063 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:34:49,047 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.229 s, system: 0.337 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.145 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:34:49,047 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.199 s, system: 0.276 s], Allocated memory: 53.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 10:34:49,048 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.769 s, CPU [user: 0.0801 s, system: 0.128 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.0801 s [61% log2 (10x), 24% testConnection (1x)] (13x), ObjectMaps: 0.0799 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 10:34:49,048 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.543 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.342 s [58% log2 (36x), 13% testConnection (6x), 13% getLatestRevision (9x)] (61x)
2025-07-30 10:34:49,209 [main | u:p] INFO  TXLOGGER - Tx 66164bac63401_0_66164bac63401_0_: finished. Total: 0.106 s, CPU [user: 0.0835 s, system: 0.00401 s], Allocated memory: 21.8 MB
2025-07-30 10:34:49,364 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.247 s [100% getReadConfiguration (48x)] (48x), svn: 0.0887 s [84% info (18x)] (38x)
2025-07-30 10:34:49,657 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.201 s [73% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.168 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 10:34:49,891 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.106 s, CPU [user: 0.0353 s, system: 0.00701 s], Allocated memory: 2.7 MB
2025-07-30 10:34:49,891 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.106 s, CPU [user: 0.0135 s, system: 0.00411 s], Allocated memory: 1.6 MB
2025-07-30 10:34:49,940 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.153 s, CPU [user: 0.0311 s, system: 0.0105 s], Allocated memory: 10.8 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 10:34:50,008 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.334 s [100% doFinishStartup (1x)] (1x), commit: 0.0927 s [100% Revision (1x)] (1x), Lucene: 0.0612 s [100% refresh (1x)] (1x), DB: 0.0173 s [46% query (1x), 37% update (3x)] (8x)
2025-07-30 10:34:53,382 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.438 s [89% info (158x)] (170x)
2025-07-30 10:34:54,509 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.00705 s, system: 0.00155 s], Allocated memory: 531.4 kB, transactions: 1
2025-07-30 10:34:54,510 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, persistence listener: 0.0321 s [82% indexRefreshPersistenceListener (1x)] (7x), resolve: 0.0316 s [88% User (2x)] (4x), Incremental Baseline: 0.0307 s [100% WorkItem (21x)] (21x), notification worker: 0.0196 s [42% RevisionActivityCreator (2x), 26% TestRunActivityCreator (1x), 14% PlanActivityCreator (1x)] (6x), Lucene: 0.00937 s [56% add (1x), 44% refresh (1x)] (2x), ObjectMaps: 0.0088 s [100% getPrimaryObjectLocation (2x)] (2x), EHCache: 0.00322 s [99% GET (16x)] (37x)
2025-07-30 10:34:54,511 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.03 s, CPU [user: 0.193 s, system: 0.0301 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.9 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0846 s [86% buildBaselineSnapshots (1x)] (23x)
2025-07-30 10:34:54,978 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb094040_0_66164bb094040_0_: finished. Total: 1.59 s, CPU [user: 0.442 s, system: 0.1 s], Allocated memory: 54.6 MB, svn: 0.914 s [48% getDatedRevision (181x), 31% getDir2 content (25x), 20% getFile content (98x)] (307x), resolve: 0.607 s [100% Category (96x)] (96x), ObjectMaps: 0.171 s [44% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (388x)
2025-07-30 10:34:55,212 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb234048_0_66164bb234048_0_: finished. Total: 0.155 s, CPU [user: 0.0718 s, system: 0.0117 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0788 s [64% getReadConfiguration (162x), 36% getReadUserConfiguration (10x)] (172x), svn: 0.0695 s [57% info (19x), 38% getFile content (16x)] (37x), resolve: 0.0384 s [100% User (9x)] (9x), ObjectMaps: 0.0184 s [55% getPrimaryObjectProperty (9x), 25% getPrimaryObjectLocation (9x)] (37x)
2025-07-30 10:34:55,543 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb26884a_0_66164bb26884a_0_: finished. Total: 0.276 s, CPU [user: 0.0897 s, system: 0.00915 s], Allocated memory: 19.9 MB, svn: 0.215 s [70% getDir2 content (17x), 30% getFile content (44x)] (62x), RepositoryConfigService: 0.104 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 10:34:56,424 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb2adc4b_0_66164bb2adc4b_0_: finished. Total: 0.881 s, CPU [user: 0.399 s, system: 0.0477 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.693 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.43 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-30 10:34:56,525 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb38a04c_0_66164bb38a04c_0_: finished. Total: 0.101 s, CPU [user: 0.0205 s, system: 0.00242 s], Allocated memory: 17.8 MB, svn: 0.0912 s [84% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0208 s [98% getReadConfiguration (124x)] (148x)
2025-07-30 10:34:57,009 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66164bb3bb04e_0_66164bb3bb04e_0_: finished. Total: 0.389 s, CPU [user: 0.143 s, system: 0.02 s], Allocated memory: 385.2 MB, svn: 0.27 s [64% getDir2 content (20x), 36% getFile content (185x)] (206x), RepositoryConfigService: 0.197 s [96% getReadConfiguration (2787x)] (3025x)
2025-07-30 10:34:57,009 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.62 s, CPU [user: 1.25 s, system: 0.204 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.12 s [42% getDir2 content (114x), 34% getFile content (809x), 21% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.17 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.684 s [89% Category (96x)] (117x), ObjectMaps: 0.202 s [47% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-30 10:34:57,009 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 3.02 s [44% getDatedRevision (362x), 29% getDir2 content (114x), 24% getFile content (809x)] (1328x), RepositoryConfigService: 1.17 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.684 s [89% Category (96x)] (118x), ObjectMaps: 0.202 s [47% getPrimaryObjectProperty (110x), 33% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-30 10:35:13,066 [ajp-nio-127.0.0.1-8889-exec-2 | cID:592f0c4c-0a465820-6d5a43c1-f097707c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753842678842': Total: 0.728 s, CPU [user: 0.334 s, system: 0.102 s], Allocated memory: 53.1 MB, transactions: 3, PolarionAuthenticator: 0.536 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.063 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 10:35:13,326 [ajp-nio-127.0.0.1-8889-exec-3 | cID:592f0fc6-0a465820-6d5a43c1-7d383563] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.102 s, CPU [user: 0.0562 s, system: 0.0113 s], Allocated memory: 3.9 MB, transactions: 0
