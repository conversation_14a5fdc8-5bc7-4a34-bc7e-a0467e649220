2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:41,320 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:25:46,228 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:25:46,436 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.208 s. ]
2025-07-30 10:25:46,437 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:25:46,520 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0834 s. ]
2025-07-30 10:25:46,588 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:25:46,739 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 103 s. ]
2025-07-30 10:25:46,963 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.65 s. ]
2025-07-30 10:25:47,115 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:47,115 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:25:47,153 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.19 s. ]
2025-07-30 10:25:47,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:47,154 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 10:25:47,164 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (1/9)
2025-07-30 10:25:47,185 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:25:47,445 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:25:47,528 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:25:48,165 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.01 s. ]
2025-07-30 10:25:48,222 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:48,222 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:25:48,566 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:25:48,581 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.42 s. ]
2025-07-30 10:25:48,631 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:48,631 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:25:48,637 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:25:48,693 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:25:48,993 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:25:49,052 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:25:49,079 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:25:49,120 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:25:49,152 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:25:49,186 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:25:49,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:25:49,227 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.65 s. ]
2025-07-30 10:25:49,228 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:49,228 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:25:49,245 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:25:49,245 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:49,245 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:25:49,389 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:25:49,392 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:25:49,541 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.3 s. ]
2025-07-30 10:25:49,541 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:49,541 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:25:49,551 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:25:49,551 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:25:49,551 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:25:52,845 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.29 s. ]
2025-07-30 10:25:52,847 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:25:52,847 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.5 s. ]
2025-07-30 10:25:52,847 [main] INFO  com.polarion.platform.startup - ****************************************************************
