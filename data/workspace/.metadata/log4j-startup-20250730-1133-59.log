2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:33:59,294 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:34:03,450 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:34:03,586 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.135 s. ]
2025-07-30 11:34:03,586 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:34:03,650 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0647 s. ]
2025-07-30 11:34:03,721 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:34:03,849 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 56 s. ]
2025-07-30 11:34:04,069 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.78 s. ]
2025-07-30 11:34:04,160 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:04,160 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:34:04,189 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 11:34:04,189 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:04,189 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (5/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (2/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 11:34:04,195 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 11:34:04,201 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:34:04,342 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:34:04,425 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:34:04,983 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.79 s. ]
2025-07-30 11:34:04,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:04,995 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:34:05,208 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:34:05,219 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-30 11:34:05,245 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:05,245 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:34:05,249 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:34:05,314 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:34:05,364 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:34:05,394 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:34:05,426 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:34:05,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:34:05,504 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:34:05,545 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:34:05,574 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:34:05,574 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-07-30 11:34:05,574 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:05,574 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:34:05,589 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:34:05,589 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:05,589 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:34:05,696 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:34:05,699 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:34:05,843 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.25 s. ]
2025-07-30 11:34:05,844 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:05,844 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:34:05,853 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:34:05,853 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:34:05,853 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:34:08,618 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.77 s. ]
2025-07-30 11:34:08,619 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:34:08,619 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.32 s. ]
2025-07-30 11:34:08,619 [main] INFO  com.polarion.platform.startup - ****************************************************************
