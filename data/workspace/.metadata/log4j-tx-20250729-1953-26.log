2025-07-29 19:53:31,267 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0484 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0213 s [60% getLatestRevision (2x), 31% testConnection (1x)] (4x)
2025-07-29 19:53:31,385 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0313 s [61% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.653 s, CPU [user: 0.19 s, system: 0.239 s], Allocated memory: 52.7 MB, transactions: 0, ObjectMaps: 0.11 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.651 s, CPU [user: 0.231 s, system: 0.299 s], Allocated memory: 68.7 MB, transactions: 0, ObjectMaps: 0.122 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.653 s, CPU [user: 0.0834 s, system: 0.126 s], Allocated memory: 12.4 MB, transactions: 0, svn: 0.0823 s [74% log2 (10x), 16% getLatestRevision (2x)] (13x), ObjectMaps: 0.0706 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.653 s, CPU [user: 0.116 s, system: 0.18 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0628 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0522 s [56% log2 (5x), 31% testConnection (1x)] (7x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.653 s, CPU [user: 0.0556 s, system: 0.0857 s], Allocated memory: 6.9 MB, transactions: 0, ObjectMaps: 0.0723 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0461 s [78% log2 (5x), 13% testConnection (1x)] (7x)
2025-07-29 19:53:32,042 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.653 s, CPU [user: 0.093 s, system: 0.0934 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.142 s [61% log2 (10x), 13% info (5x), 9% log (1x)] (24x), ObjectMaps: 0.0362 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:53:32,043 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.474 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.382 s [64% log2 (36x), 16% testConnection (6x), 11% getLatestRevision (9x)] (61x)
2025-07-29 19:53:32,271 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.196 s [100% getReadConfiguration (48x)] (48x), svn: 0.0736 s [85% info (18x)] (38x)
2025-07-29 19:53:32,691 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.347 s [72% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.251 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:53:32,925 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.22 s [100% doFinishStartup (1x)] (1x), commit: 0.0469 s [100% Revision (1x)] (1x), Lucene: 0.0298 s [100% refresh (1x)] (1x), DB: 0.0155 s [43% update (3x), 33% query (1x), 15% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0143 s [100% objectsToInv (1x)] (1x)
