2025-07-30 10:34:42,962 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:34:42,962 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 10:34:42,962 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:34:42,963 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 10:34:42,963 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 10:34:42,963 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:42,963 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 10:34:47,430 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 10:34:47,598 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.167 s. ]
2025-07-30 10:34:47,598 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 10:34:47,653 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0547 s. ]
2025-07-30 10:34:47,760 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 10:34:47,890 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 97 s. ]
2025-07-30 10:34:48,125 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.17 s. ]
2025-07-30 10:34:48,242 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:48,242 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 10:34:48,273 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 10:34:48,274 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:48,274 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 10:34:48,279 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 10:34:48,279 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 10:34:48,279 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 10:34:48,279 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 10:34:48,279 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 10:34:48,282 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 10:34:48,295 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 10:34:48,447 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 10:34:48,560 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 10:34:49,048 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 10:34:49,074 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:49,074 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 10:34:49,348 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 10:34:49,363 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 10:34:49,405 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:49,405 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 10:34:49,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 10:34:49,470 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 10:34:49,518 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 10:34:49,534 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 10:34:49,549 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 10:34:49,573 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 10:34:49,598 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 10:34:49,627 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 10:34:49,657 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 10:34:49,657 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-30 10:34:49,657 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:49,657 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 10:34:49,673 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 10:34:49,673 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:49,673 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 10:34:49,802 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 10:34:49,808 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 10:34:50,008 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.33 s. ]
2025-07-30 10:34:50,009 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:50,009 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 10:34:50,020 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 10:34:50,020 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 10:34:50,020 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 10:34:53,382 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.36 s. ]
2025-07-30 10:34:53,382 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 10:34:53,382 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.4 s. ]
2025-07-30 10:34:53,382 [main] INFO  com.polarion.platform.startup - ****************************************************************
