2025-07-30 17:35:21,513 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:35:21,513 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:35:21,513 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:35:21,513 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:35:21,514 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:35:21,514 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:21,514 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:35:25,663 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:35:25,799 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.136 s. ]
2025-07-30 17:35:25,799 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:35:25,851 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0515 s. ]
2025-07-30 17:35:25,898 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:35:26,006 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:35:26,221 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.71 s. ]
2025-07-30 17:35:26,305 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:26,305 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:35:26,326 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 17:35:26,326 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:26,326 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 17:35:26,331 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 17:35:26,338 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:35:26,478 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:35:26,582 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:35:27,026 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.7 s. ]
2025-07-30 17:35:27,036 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,036 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:35:27,267 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:35:27,279 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 17:35:27,304 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,305 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:35:27,308 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:35:27,368 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:35:27,409 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:35:27,447 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:35:27,489 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:35:27,512 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:35:27,531 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:35:27,565 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:35:27,598 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:35:27,598 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-30 17:35:27,598 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,598 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:35:27,611 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:35:27,611 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,612 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:35:27,721 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:35:27,726 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:35:27,884 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.27 s. ]
2025-07-30 17:35:27,886 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,886 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:35:27,896 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:35:27,896 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:35:27,896 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:35:30,243 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.35 s. ]
2025-07-30 17:35:30,243 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:35:30,243 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 8.73 s. ]
2025-07-30 17:35:30,243 [main] INFO  com.polarion.platform.startup - ****************************************************************
