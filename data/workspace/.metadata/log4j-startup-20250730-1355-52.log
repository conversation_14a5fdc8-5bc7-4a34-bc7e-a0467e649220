2025-07-30 13:55:52,672 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:55:52,672 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:55:52,673 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:55:52,673 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:55:52,673 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:55:52,673 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:52,673 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:55:56,985 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:55:57,119 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.134 s. ]
2025-07-30 13:55:57,120 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:55:57,160 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0404 s. ]
2025-07-30 13:55:57,213 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:55:57,323 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 13:55:57,535 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.87 s. ]
2025-07-30 13:55:57,618 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:57,618 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:55:57,642 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 13:55:57,643 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:57,643 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 13:55:57,647 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 13:55:57,655 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:55:57,777 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:55:57,858 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:55:58,388 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.75 s. ]
2025-07-30 13:55:58,400 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:58,400 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:55:58,630 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:55:58,642 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 13:55:58,666 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:58,666 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:55:58,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:55:58,730 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:55:58,774 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:55:58,807 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:55:58,829 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:55:58,848 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:55:58,867 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:55:58,899 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:55:58,928 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:55:58,929 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-30 13:55:58,929 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:58,929 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:55:58,945 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 13:55:58,945 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:58,945 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:55:59,044 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:55:59,046 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:55:59,147 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.2 s. ]
2025-07-30 13:55:59,148 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:59,148 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:55:59,154 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:55:59,154 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:55:59,154 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:56:01,675 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.52 s. ]
2025-07-30 13:56:01,675 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:56:01,675 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9 s. ]
2025-07-30 13:56:01,675 [main] INFO  com.polarion.platform.startup - ****************************************************************
