2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:46,276 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:51:50,423 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:51:50,565 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-30 17:51:50,565 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:51:50,612 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0469 s. ]
2025-07-30 17:51:50,668 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:51:50,774 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:51:50,982 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.71 s. ]
2025-07-30 17:51:51,058 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:51,058 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:51:51,083 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.1 s. ]
2025-07-30 17:51:51,083 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:51,083 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:51:51,089 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-30 17:51:51,089 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (1/9)
2025-07-30 17:51:51,089 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (3/9)
2025-07-30 17:51:51,090 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 17:51:51,090 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (6/9)
2025-07-30 17:51:51,090 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 17:51:51,095 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:51:51,222 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:51:51,307 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:51:51,756 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.67 s. ]
2025-07-30 17:51:51,767 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:51,767 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:51:51,988 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:51:52,010 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.25 s. ]
2025-07-30 17:51:52,034 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:52,034 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:51:52,037 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:51:52,092 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:51:52,132 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:51:52,167 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:51:52,207 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:51:52,227 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:51:52,240 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:51:52,274 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:51:52,303 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:51:52,303 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.29 s. ]
2025-07-30 17:51:52,303 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:52,303 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:51:52,316 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 17:51:52,317 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:52,317 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:51:52,420 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:51:52,423 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:51:52,543 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 17:51:52,544 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:52,544 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:51:52,553 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:51:52,553 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:51:52,553 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:52:48,670 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 56.12 s. ]
2025-07-30 17:52:48,671 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:52:48,671 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 62.4 s. ]
2025-07-30 17:52:48,671 [main] INFO  com.polarion.platform.startup - ****************************************************************
