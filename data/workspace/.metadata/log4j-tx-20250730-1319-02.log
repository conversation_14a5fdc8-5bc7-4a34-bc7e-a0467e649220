2025-07-30 13:19:08,882 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0702 s [65% update (144x), 34% query (12x)] (221x), svn: 0.0119 s [52% getLatestRevision (2x), 34% testConnection (1x)] (4x)
2025-07-30 13:19:09,033 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0442 s [64% getDir2 content (2x), 28% info (3x)] (6x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.052 s, system: 0.0974 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0671 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.111 s, system: 0.209 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0729 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.105 s, system: 0.14 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.124 s [44% log2 (10x), 18% info (5x), 16% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0788 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.0837 s, system: 0.116 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0997 s [81% log2 (10x)] (13x), ObjectMaps: 0.057 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.243 s, system: 0.342 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.131 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:19:09,805 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.2 s, system: 0.28 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:19:09,806 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.524 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.338 s [63% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 13:19:09,948 [main | u:p] INFO  TXLOGGER - Tx 6616714a02c01_0_6616714a02c01_0_: finished. Total: 0.109 s, CPU [user: 0.0814 s, system: 0.00457 s], Allocated memory: 21.8 MB
2025-07-30 13:19:10,123 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.263 s [100% getReadConfiguration (48x)] (48x), svn: 0.104 s [82% info (18x)] (38x)
2025-07-30 13:19:10,533 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.321 s [74% info (94x), 19% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.231 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:19:10,775 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.102 s, CPU [user: 0.0173 s, system: 0.0039 s], Allocated memory: 2.1 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:19:10,781 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.108 s, CPU [user: 0.0241 s, system: 0.00553 s], Allocated memory: 2.6 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:19:10,822 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.148 s, CPU [user: 0.0298 s, system: 0.00824 s], Allocated memory: 10.7 MB, GC: 0.016 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:19:10,924 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.124 s, CPU [user: 0.0348 s, system: 0.0055 s], Allocated memory: 10.3 MB, commit: 0.123 s [100% Revision (1x)] (1x)
2025-07-30 13:19:10,934 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.382 s [100% doFinishStartup (1x)] (1x), commit: 0.123 s [100% Revision (1x)] (1x), Lucene: 0.056 s [100% refresh (1x)] (1x), DB: 0.0238 s [45% query (1x), 23% execute (1x), 23% update (3x)] (8x)
2025-07-30 13:19:13,881 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.355 s [89% info (158x)] (168x)
2025-07-30 13:19:14,345 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616714df9845_0_6616714df9845_0_: finished. Total: 0.449 s, CPU [user: 0.155 s, system: 0.0249 s], Allocated memory: 22.0 MB, resolve: 0.117 s [63% User (2x), 33% Project (1x)] (5x), Lucene: 0.106 s [100% search (1x)] (1x), GC: 0.074 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0505 s [67% getPrimaryObjectLocation (2x), 28% getPrimaryObjectProperty (2x)] (11x), svn: 0.028 s [34% log (1x), 30% getLatestRevision (2x), 17% testConnection (1x)] (8x)
2025-07-30 13:19:14,966 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.978 s, CPU [user: 0.00708 s, system: 0.00226 s], Allocated memory: 314.9 kB, transactions: 1, GC: 0.074 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:19:14,970 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.467 s [98% RevisionActivityCreator (2x)] (6x), Lucene: 0.154 s [69% search (1x), 23% add (1x)] (3x), resolve: 0.139 s [67% User (3x), 28% Project (1x)] (7x), ObjectMaps: 0.0559 s [70% getPrimaryObjectLocation (3x), 25% getPrimaryObjectProperty (2x)] (12x), svn: 0.0437 s [37% getLatestRevision (3x), 29% testConnection (2x), 21% log (1x)] (10x), Incremental Baseline: 0.0391 s [100% WorkItem (22x)] (22x)
2025-07-30 13:19:14,971 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.09 s, CPU [user: 0.179 s, system: 0.0309 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.781 s [98% getDatedRevision (181x)] (183x), Lucene: 0.139 s [85% buildBaselineSnapshots (1x)] (24x), GC: 0.074 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 13:19:15,486 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616714df8840_0_6616714df8840_0_: finished. Total: 1.59 s, CPU [user: 0.342 s, system: 0.089 s], Allocated memory: 46.6 MB, svn: 0.982 s [49% getDatedRevision (181x), 38% getDir2 content (25x)] (307x), resolve: 0.365 s [100% Category (96x)] (96x), ObjectMaps: 0.128 s [45% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 13:19:15,688 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616714f9d448_0_6616714f9d448_0_: finished. Total: 0.115 s, CPU [user: 0.0604 s, system: 0.00955 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.0531 s [56% getReadUserConfiguration (10x), 44% getReadConfiguration (162x)] (172x), svn: 0.0478 s [56% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0315 s [100% User (9x)] (9x), ObjectMaps: 0.0151 s [63% getPrimaryObjectProperty (8x), 21% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 13:19:16,039 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616714fc7c4a_0_6616714fc7c4a_0_: finished. Total: 0.296 s, CPU [user: 0.0891 s, system: 0.0109 s], Allocated memory: 19.9 MB, svn: 0.236 s [78% getDir2 content (17x), 22% getFile content (44x)] (62x), RepositoryConfigService: 0.0879 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 13:19:16,835 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661671501244b_0_661671501244b_0_: finished. Total: 0.794 s, CPU [user: 0.347 s, system: 0.0229 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.562 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.434 s [58% getFile content (412x), 42% getDir2 content (21x)] (434x)
2025-07-30 13:19:16,940 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66167150d8c4c_0_66167150d8c4c_0_: finished. Total: 0.104 s, CPU [user: 0.0202 s, system: 0.00196 s], Allocated memory: 18.0 MB, svn: 0.0933 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0195 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 13:19:17,315 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616715100c4e_0_6616715100c4e_0_: finished. Total: 0.32 s, CPU [user: 0.136 s, system: 0.0105 s], Allocated memory: 386.8 MB, RepositoryConfigService: 0.205 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.196 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x)
2025-07-30 13:19:17,315 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.43 s, CPU [user: 1.08 s, system: 0.159 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.1 s [46% getDir2 content (115x), 28% getFile content (807x), 23% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.996 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.433 s [84% Category (96x)] (117x)
2025-07-30 13:19:17,316 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.88 s [43% getDatedRevision (362x), 34% getDir2 content (115x), 20% getFile content (807x)] (1325x), RepositoryConfigService: 0.996 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.433 s [84% Category (96x)] (118x), Lucene: 0.189 s [63% buildBaselineSnapshots (2x), 20% search (5x)] (54x), ObjectMaps: 0.156 s [48% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 13:19:25,300 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59c5630b-0a465820-46aa0289-479b24bb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.36 s, CPU [user: 0.21 s, system: 0.0351 s], Allocated memory: 43.8 MB, transactions: 2, PolarionAuthenticator: 0.31 s [100% authenticate (1x)] (1x)
