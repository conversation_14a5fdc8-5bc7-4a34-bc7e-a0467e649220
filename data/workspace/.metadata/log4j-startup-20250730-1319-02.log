2025-07-30 13:19:03,102 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:03,103 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 13:19:08,161 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 13:19:08,335 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.173 s. ]
2025-07-30 13:19:08,335 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 13:19:08,401 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0662 s. ]
2025-07-30 13:19:08,472 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 13:19:08,629 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 13:19:08,882 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.79 s. ]
2025-07-30 13:19:08,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:08,995 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 13:19:09,033 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.15 s. ]
2025-07-30 13:19:09,033 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:09,033 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 13:19:09,042 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (2/9)
2025-07-30 13:19:09,042 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 13:19:09,042 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 13:19:09,042 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 13:19:09,043 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 13:19:09,042 [LowLevelDataService-contextInitializer-5 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-30 13:19:09,053 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 13:19:09,206 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 13:19:09,297 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 13:19:09,806 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 13:19:09,824 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:09,824 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 13:19:10,099 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 13:19:10,122 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.32 s. ]
2025-07-30 13:19:10,163 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:10,163 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 13:19:10,166 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 13:19:10,229 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 13:19:10,286 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 13:19:10,349 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 13:19:10,396 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (5/9)
2025-07-30 13:19:10,419 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 13:19:10,444 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 13:19:10,493 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 13:19:10,533 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 13:19:10,533 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.41 s. ]
2025-07-30 13:19:10,533 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:10,533 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 13:19:10,551 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 13:19:10,551 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:10,551 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 13:19:10,683 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 13:19:10,691 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 13:19:10,933 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.38 s. ]
2025-07-30 13:19:10,934 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:10,934 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 13:19:10,944 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 13:19:10,945 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 13:19:10,945 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 13:19:13,881 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 2.94 s. ]
2025-07-30 13:19:13,881 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 13:19:13,881 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.8 s. ]
2025-07-30 13:19:13,881 [main] INFO  com.polarion.platform.startup - ****************************************************************
