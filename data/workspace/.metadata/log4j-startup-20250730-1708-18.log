2025-07-30 17:08:18,304 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:08:18,304 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:08:18,304 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:08:18,304 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:08:18,305 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:08:18,305 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:18,305 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:08:22,506 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:08:22,648 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.142 s. ]
2025-07-30 17:08:22,649 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:08:22,711 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.062 s. ]
2025-07-30 17:08:22,760 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:08:22,895 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:08:23,114 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.82 s. ]
2025-07-30 17:08:23,223 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:23,223 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:08:23,257 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.14 s. ]
2025-07-30 17:08:23,259 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:23,259 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (3/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 17:08:23,264 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 17:08:23,272 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:08:23,452 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:08:23,557 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:08:24,027 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 17:08:24,038 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,038 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:08:24,271 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:08:24,284 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.26 s. ]
2025-07-30 17:08:24,311 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,311 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:08:24,315 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:08:24,369 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:08:24,413 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:08:24,458 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:08:24,496 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:08:24,513 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:08:24,527 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:08:24,554 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:08:24,580 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:08:24,580 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-07-30 17:08:24,580 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,580 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:08:24,597 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:08:24,598 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,598 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:08:24,701 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:08:24,704 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:08:24,841 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-30 17:08:24,842 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,842 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:08:24,848 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:08:24,848 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:08:24,848 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:08:27,920 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.07 s. ]
2025-07-30 17:08:27,920 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:08:27,920 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 9.62 s. ]
2025-07-30 17:08:27,920 [main] INFO  com.polarion.platform.startup - ****************************************************************
