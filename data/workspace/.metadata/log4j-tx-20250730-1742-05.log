2025-07-30 17:42:10,727 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0644 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0131 s [51% getLatestRevision (2x), 41% testConnection (1x)] (4x)
2025-07-30 17:42:10,857 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0372 s [66% getDir2 content (2x), 29% info (3x)] (6x)
2025-07-30 17:42:11,598 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.194 s, system: 0.26 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:42:11,599 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.11 s, system: 0.187 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0786 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0381 s [81% log2 (5x)] (7x)
2025-07-30 17:42:11,599 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.238 s, system: 0.317 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.119 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:42:11,599 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.738 s, CPU [user: 0.0961 s, system: 0.135 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.145 s [48% log2 (10x), 16% log (1x), 14% info (5x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.085 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:42:11,599 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0487 s, system: 0.0768 s], Allocated memory: 7.0 MB, transactions: 0, ObjectMaps: 0.0615 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0515 s [67% log2 (5x), 17% getLatestRevision (1x)] (7x)
2025-07-30 17:42:11,599 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.737 s, CPU [user: 0.0813 s, system: 0.113 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.121 s [81% log2 (10x)] (13x), ObjectMaps: 0.0451 s [99% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:42:11,599 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.396 s [65% log2 (36x), 12% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 17:42:11,877 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.244 s [100% getReadConfiguration (48x)] (48x), svn: 0.11 s [89% info (18x)] (38x)
2025-07-30 17:42:12,259 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.305 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.231 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:42:12,472 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.1 s, CPU [user: 0.0224 s, system: 0.00669 s], Allocated memory: 10.5 MB, GC: 0.012 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:42:12,508 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.234 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0539 s [100% refresh (1x)] (1x), commit: 0.0406 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0155 s [100% objectsToInv (1x)] (1x), DB: 0.0135 s [38% update (3x), 31% query (1x), 21% execute (1x)] (8x)
2025-07-30 17:42:29,088 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.6 s [86% info (158x)] (174x), PullingJob: 0.0392 s [100% collectChanges (5x)] (5x)
2025-07-30 17:42:29,785 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616ad8efa044_0_6616ad8efa044_0_: finished. Total: 0.688 s, CPU [user: 0.163 s, system: 0.0324 s], Allocated memory: 21.5 MB, resolve: 0.167 s [55% User (2x), 35% Project (1x)] (5x), outlineNumberQueryExpander: 0.138 s [100% expand (1x)] (2x), GC: 0.132 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.0759 s [100% search (1x)] (1x), hasLinkedResourcesQueryExpander: 0.0696 s [100% expand (1x)] (2x), ObjectMaps: 0.0573 s [46% getPrimaryObjectLocation (2x), 44% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 17:42:30,509 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.08 s, CPU [user: 0.00726 s, system: 0.00287 s], Allocated memory: 315.8 kB, transactions: 1
2025-07-30 17:42:30,509 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.717 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.215 s [58% User (3x), 27% Project (1x)] (7x), outlineNumberQueryExpander: 0.138 s [100% expand (1x)] (2x), Lucene: 0.108 s [70% search (1x), 21% add (1x)] (3x), hasLinkedResourcesQueryExpander: 0.0696 s [100% expand (1x)] (2x), ObjectMaps: 0.0695 s [56% getPrimaryObjectLocation (3x), 36% getPrimaryObjectProperty (2x)] (12x)
2025-07-30 17:42:30,510 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.42 s, CPU [user: 0.176 s, system: 0.0313 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.945 s [97% getDatedRevision (181x)] (183x), GC: 0.132 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:42:30,948 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ad8efec45_0_6616ad8efec45_0_: finished. Total: 1.83 s, CPU [user: 0.372 s, system: 0.102 s], Allocated memory: 46.6 MB, svn: 1.08 s [45% getDir2 content (25x), 38% getDatedRevision (181x)] (307x), resolve: 0.639 s [100% Category (96x)] (96x), ObjectMaps: 0.236 s [39% getPrimaryObjectLocation (96x), 38% getPrimaryObjectProperty (96x), 23% getLastPromoted (96x)] (387x), GC: 0.132 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 17:42:31,154 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ad90e1848_0_6616ad90e1848_0_: finished. Total: 0.108 s, CPU [user: 0.0529 s, system: 0.00972 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0485 s [51% getReadConfiguration (162x), 49% getReadUserConfiguration (10x)] (172x), svn: 0.0419 s [54% info (19x), 37% getFile content (15x)] (36x), resolve: 0.0258 s [100% User (9x)] (9x), ObjectMaps: 0.0121 s [60% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 17:42:31,352 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ad910944a_0_6616ad910944a_0_: finished. Total: 0.147 s, CPU [user: 0.0471 s, system: 0.00645 s], Allocated memory: 19.9 MB, svn: 0.115 s [78% getDir2 content (17x), 22% getFile content (44x)] (62x), RepositoryConfigService: 0.0424 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:42:32,043 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ad912e04b_0_6616ad912e04b_0_: finished. Total: 0.691 s, CPU [user: 0.321 s, system: 0.0312 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.54 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.353 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-30 17:42:32,438 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616ad91f784e_0_6616ad91f784e_0_: finished. Total: 0.28 s, CPU [user: 0.114 s, system: 0.00629 s], Allocated memory: 386.8 MB, RepositoryConfigService: 0.187 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.173 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-07-30 17:42:32,438 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.34 s, CPU [user: 0.993 s, system: 0.17 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.93 s [44% getDir2 content (115x), 31% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.89 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.706 s [91% Category (96x)] (117x), ObjectMaps: 0.26 s [41% getPrimaryObjectProperty (108x), 37% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x), GC: 0.171 s [100% G1 Young Generation (6x)] (6x)
2025-07-30 17:42:32,438 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.88 s [46% getDatedRevision (362x), 30% getDir2 content (115x), 21% getFile content (807x)] (1325x), RepositoryConfigService: 0.89 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.707 s [90% Category (96x)] (118x), ObjectMaps: 0.26 s [41% getPrimaryObjectProperty (108x), 37% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 17:47:10,064 [LuceneCommitTask-1] INFO  TXLOGGER - Tx Lucene Commit [activities]: finished. Total: 0.106 s, CPU [user: 0.0164 s, system: 0.022 s], Allocated memory: 19.0 MB, commit: 0.105 s [100% Activities (1x)] (1x)
2025-07-30 17:48:01,016 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.113 s, CPU [user: 0.0031 s, system: 0.0139 s], Allocated memory: 130.5 kB, transactions: 0, PullingJob: 0.0912 s [100% collectChanges (1x)] (1x), svn: 0.0821 s [100% getLatestRevision (1x)] (1x)
2025-07-30 17:49:01,116 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.157 s, CPU [user: 0.0021 s, system: 0.00188 s], Allocated memory: 130.4 kB, transactions: 0, PullingJob: 0.155 s [100% collectChanges (1x)] (1x), svn: 0.155 s [100% getLatestRevision (1x)] (1x)
2025-07-30 17:49:05,704 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.853857421875
2025-07-30 17:49:15,700 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.6462890625
2025-07-30 17:49:25,702 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.438623046875
2025-07-30 17:49:35,700 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.2548828125
2025-07-30 17:49:45,699 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.129443359375
