2025-07-30 11:07:31,972 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0475 s [58% update (144x), 42% query (12x)] (221x), svn: 0.0122 s [57% getLatestRevision (2x), 32% testConnection (1x)] (4x)
2025-07-30 11:07:32,100 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0345 s [64% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 11:07:32,867 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.762 s, CPU [user: 0.262 s, system: 0.314 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.132 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:07:32,867 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.13 s, system: 0.188 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0676 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:07:32,867 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0878 s, system: 0.099 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.119 s [89% log2 (10x)] (13x), ObjectMaps: 0.0475 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:07:32,867 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.218 s, system: 0.256 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.116 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:07:32,868 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.763 s, CPU [user: 0.0916 s, system: 0.126 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.081 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0726 s [68% log2 (10x), 16% getLatestRevision (2x)] (13x)
2025-07-30 11:07:32,868 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0805 s, system: 0.0862 s], Allocated memory: 9.5 MB, transactions: 0, svn: 0.0893 s [30% log2 (5x), 25% info (5x), 22% log (1x), 13% getLatestRevision (2x)] (18x), ObjectMaps: 0.066 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:07:32,869 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.352 s [66% log2 (36x), 12% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 11:07:33,007 [main | u:p] INFO  TXLOGGER - Tx 6616532a27001_0_6616532a27001_0_: finished. Total: 0.114 s, CPU [user: 0.0826 s, system: 0.00519 s], Allocated memory: 21.8 MB
2025-07-30 11:07:33,142 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.237 s [100% getReadConfiguration (48x)] (48x), svn: 0.0812 s [87% info (18x)] (38x)
2025-07-30 11:07:33,478 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.262 s [75% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.202 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:07:33,786 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.162 s, CPU [user: 0.0297 s, system: 0.00884 s], Allocated memory: 10.8 MB, GC: 0.017 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:07:33,857 [main | u:p] INFO  TXLOGGER - Tx Lucene Commit [head]: finished. Total: 0.122 s, CPU [user: 0.0404 s, system: 0.00966 s], Allocated memory: 10.0 MB, commit: 0.119 s [100% Revision (1x)] (1x)
2025-07-30 11:07:33,876 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.381 s [100% doFinishStartup (1x)] (1x), commit: 0.119 s [100% Revision (1x)] (1x), Lucene: 0.048 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0237 s [100% objectsToInv (1x)] (1x)
2025-07-30 11:07:37,887 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.783 s [90% info (158x)] (170x)
2025-07-30 11:07:38,445 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616532f09c45_0_6616532f09c45_0_: finished. Total: 0.549 s, CPU [user: 0.176 s, system: 0.0278 s], Allocated memory: 22.3 MB, resolve: 0.161 s [81% User (2x)] (5x), Lucene: 0.122 s [100% search (1x)] (1x), GC: 0.057 s [100% G1 Young Generation (1x)] (1x), ObjectMaps: 0.0566 s [72% getPrimaryObjectLocation (2x), 20% getPrimaryObjectProperty (2x)] (11x), svn: 0.0338 s [44% getLatestRevision (3x), 22% testConnection (1x), 15% log (1x)] (9x)
2025-07-30 11:07:39,101 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.08 s, CPU [user: 0.0066 s, system: 0.00218 s], Allocated memory: 315.6 kB, transactions: 1, GC: 0.057 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:07:39,102 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.562 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.202 s [84% User (3x)] (7x), Lucene: 0.144 s [84% search (1x)] (3x), ObjectMaps: 0.0669 s [76% getPrimaryObjectLocation (3x), 17% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0388 s [100% WorkItem (22x)] (22x), svn: 0.0338 s [44% getLatestRevision (3x), 22% testConnection (1x), 15% log (1x)] (9x)
2025-07-30 11:07:39,103 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.22 s, CPU [user: 0.198 s, system: 0.0331 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.912 s [92% getDatedRevision (181x)] (183x), Lucene: 0.117 s [87% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:07:39,605 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616532f08c40_0_6616532f08c40_0_: finished. Total: 1.71 s, CPU [user: 0.388 s, system: 0.104 s], Allocated memory: 46.6 MB, svn: 0.964 s [48% getDatedRevision (181x), 37% getDir2 content (25x)] (307x), resolve: 0.468 s [100% Category (96x)] (96x), ObjectMaps: 0.174 s [42% getPrimaryObjectProperty (96x), 35% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 11:07:39,808 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165330c8448_0_66165330c8448_0_: finished. Total: 0.127 s, CPU [user: 0.0625 s, system: 0.00966 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0623 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (162x)] (172x), svn: 0.0592 s [57% info (19x), 34% getFile content (15x)] (36x), resolve: 0.0318 s [100% User (9x)] (9x), ObjectMaps: 0.0143 s [66% getPrimaryObjectProperty (8x), 17% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:07:40,009 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165330f184a_0_66165330f184a_0_: finished. Total: 0.162 s, CPU [user: 0.0555 s, system: 0.00538 s], Allocated memory: 19.9 MB, svn: 0.13 s [82% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0401 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 11:07:40,948 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661653311a44b_0_661653311a44b_0_: finished. Total: 0.939 s, CPU [user: 0.398 s, system: 0.0496 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.699 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.518 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x)
2025-07-30 11:07:41,134 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661653320504c_0_661653320504c_0_: finished. Total: 0.185 s, CPU [user: 0.0457 s, system: 0.00528 s], Allocated memory: 17.8 MB, svn: 0.145 s [61% getDir2 content (18x), 39% getFile content (29x)] (48x), RepositoryConfigService: 0.0863 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 11:07:41,613 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661653323d44e_0_661653323d44e_0_: finished. Total: 0.439 s, CPU [user: 0.166 s, system: 0.0138 s], Allocated memory: 384.8 MB, RepositoryConfigService: 0.294 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.286 s [58% getFile content (185x), 42% getDir2 content (21x)] (207x)
2025-07-30 11:07:41,613 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.72 s, CPU [user: 1.18 s, system: 0.198 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.18 s [41% getDir2 content (115x), 35% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.23 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.532 s [88% Category (96x)] (117x), ObjectMaps: 0.198 s [44% getPrimaryObjectProperty (108x), 34% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:07:41,613 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.1 s [42% getDatedRevision (362x), 29% getDir2 content (115x), 25% getFile content (807x)] (1325x), RepositoryConfigService: 1.23 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.532 s [88% Category (96x)] (118x), ObjectMaps: 0.198 s [44% getPrimaryObjectProperty (108x), 34% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x), Lucene: 0.169 s [60% buildBaselineSnapshots (2x), 27% search (5x)] (54x)
2025-07-30 11:07:44,177 [ajp-nio-127.0.0.1-8889-exec-2 | cID:594cd22e-0a465820-7c20ce5f-d0ddeda8] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.642 s, CPU [user: 0.315 s, system: 0.0834 s], Allocated memory: 42.2 MB, transactions: 2, PolarionAuthenticator: 0.547 s [100% authenticate (1x)] (1x), RepositoryConfigService: 0.0365 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:07:44,842 [ajp-nio-127.0.0.1-8889-exec-8 | cID:594cd69b-0a465820-7c20ce5f-b9b52e8c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.174 s, CPU [user: 0.0897 s, system: 0.0162 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:07:44,843 [ajp-nio-127.0.0.1-8889-exec-9 | cID:594cd6af-0a465820-7c20ce5f-5de1f5ef] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.154 s, CPU [user: 0.0222 s, system: 0.00396 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 11:07:44,887 [ajp-nio-127.0.0.1-8889-exec-1 | cID:594cd6af-0a465820-7c20ce5f-5ab6b8e2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753844864594': Total: 0.199 s, CPU [user: 0.0459 s, system: 0.00773 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 11:07:44,943 [ajp-nio-127.0.0.1-8889-exec-2 | cID:594cd6af-0a465820-7c20ce5f-6df8ebb9 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753844864596] INFO  TXLOGGER - Tx 66165335cb051_0_66165335cb051_0_: finished. Total: 0.131 s, CPU [user: 0.0664 s, system: 0.0118 s], Allocated memory: 7.8 MB, svn: 0.0147 s [100% getFile content (2x)] (3x)
2025-07-30 11:07:44,945 [ajp-nio-127.0.0.1-8889-exec-2 | cID:594cd6af-0a465820-7c20ce5f-6df8ebb9] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753844864596': Total: 0.256 s, CPU [user: 0.0795 s, system: 0.0148 s], Allocated memory: 9.5 MB, transactions: 1, RepositoryConfigService: 0.132 s [100% getReadConfiguration (1x)] (1x), svn: 0.0147 s [100% getFile content (2x)] (3x)
2025-07-30 11:07:44,967 [ajp-nio-127.0.0.1-8889-exec-10 | cID:594cd6af-0a465820-7c20ce5f-dee423ef] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753844864595': Total: 0.28 s, CPU [user: 0.0327 s, system: 0.00335 s], Allocated memory: 4.1 MB, transactions: 1, RepositoryConfigService: 0.154 s [100% getReadConfiguration (1x)] (1x)
