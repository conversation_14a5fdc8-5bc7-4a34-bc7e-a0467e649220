2025-07-30 15:09:47,250 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0716 s [62% update (144x), 38% query (12x)] (221x), svn: 0.0222 s [60% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-30 15:09:47,399 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0396 s [65% getDir2 content (2x), 29% info (3x)] (6x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.886 s, CPU [user: 0.24 s, system: 0.296 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.154 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.886 s, CPU [user: 0.288 s, system: 0.365 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.142 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.886 s, CPU [user: 0.123 s, system: 0.207 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0923 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0495 s [77% log2 (5x), 12% getLatestRevision (1x)] (7x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.886 s, CPU [user: 0.119 s, system: 0.13 s], Allocated memory: 14.5 MB, transactions: 0, svn: 0.167 s [46% log2 (10x), 20% info (5x), 16% log (1x)] (24x), ObjectMaps: 0.0657 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.884 s, CPU [user: 0.0832 s, system: 0.0953 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.103 s [80% log2 (10x)] (13x), ObjectMaps: 0.0522 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 15:09:48,292 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.885 s, CPU [user: 0.0553 s, system: 0.0886 s], Allocated memory: 7.1 MB, transactions: 0, ObjectMaps: 0.0633 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0495 s [65% log2 (5x), 20% getLatestRevision (1x)] (7x)
2025-07-30 15:09:48,293 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.57 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.422 s [62% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 15:09:48,479 [main | u:p] INFO  TXLOGGER - Tx 66168a9ceb001_0_66168a9ceb001_0_: finished. Total: 0.145 s, CPU [user: 0.108 s, system: 0.00872 s], Allocated memory: 21.8 MB
2025-07-30 15:09:48,702 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.352 s [100% getReadConfiguration (48x)] (48x), svn: 0.108 s [87% info (18x)] (38x)
2025-07-30 15:09:49,228 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.388 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.311 s [100% getReadConfiguration (94x)] (94x)
2025-07-30 15:09:49,567 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.121 s, CPU [user: 0.0225 s, system: 0.0086 s], Allocated memory: 10.4 MB
2025-07-30 15:09:49,755 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.487 s [100% doFinishStartup (1x)] (1x), DB: 0.0927 s [41% update (45x), 35% query (20x), 16% execute (15x)] (122x), Lucene: 0.0666 s [100% refresh (2x)] (2x), commit: 0.0582 s [67% Revision (1x), 33% BuildArtifact (1x)] (2x), SubterraURITable: 0.0547 s [100% addIfNotExistsDB (20x)] (20x), resolve: 0.0328 s [72% BuildArtifact (11x), 28% Revision (2x)] (13x)
2025-07-30 15:09:52,520 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.33 s [87% info (158x)] (168x), RepositoryConfigService: 0.0248 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 15:09:52,823 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66168aa106c44_0_66168aa106c44_0_: finished. Total: 0.283 s, CPU [user: 0.137 s, system: 0.018 s], Allocated memory: 20.3 MB, resolve: 0.106 s [71% User (2x), 27% Project (1x)] (5x), ObjectMaps: 0.0465 s [69% getPrimaryObjectLocation (2x), 24% getPrimaryObjectProperty (2x)] (11x), svn: 0.0297 s [40% getLatestRevision (2x), 22% log (1x), 18% testConnection (1x), 11% getFile content (2x)] (8x), Lucene: 0.0219 s [100% search (1x)] (1x), GlobalHandler: 0.0149 s [97% applyTxChanges (1x)] (4x)
2025-07-30 15:09:53,444 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.833 s, CPU [user: 0.00448 s, system: 0.00151 s], Allocated memory: 312.6 kB, transactions: 1
2025-07-30 15:09:53,444 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.833 s, CPU [user: 0.00484 s, system: 0.00161 s], Allocated memory: 498.8 kB, transactions: 1
2025-07-30 15:09:53,445 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 70, notification worker: 0.296 s [98% RevisionActivityCreator (18x)] (54x), resolve: 0.125 s [74% User (3x), 23% Project (1x)] (7x), ObjectMaps: 0.0548 s [74% getPrimaryObjectLocation (3x), 21% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0436 s [50% search (1x), 33% refresh (2x)] (4x), svn: 0.0385 s [42% getLatestRevision (3x), 25% testConnection (2x), 17% log (1x)] (10x), persistence listener: 0.0261 s [45% indexRefreshPersistenceListener (9x), 42% BuildActivityCreator (9x)] (63x), Incremental Baseline: 0.0235 s [100% WorkItem (22x)] (22x), GlobalHandler: 0.0154 s [94% applyTxChanges (2x)] (7x)
2025-07-30 15:09:53,445 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.924 s, CPU [user: 0.159 s, system: 0.0252 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.751 s [99% getDatedRevision (181x)] (183x)
2025-07-30 15:09:53,813 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168aa106840_0_66168aa106840_0_: finished. Total: 1.27 s, CPU [user: 0.323 s, system: 0.0808 s], Allocated memory: 47.1 MB, svn: 0.756 s [46% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.396 s [100% Category (96x)] (96x), ObjectMaps: 0.142 s [44% getPrimaryObjectProperty (96x), 29% getPrimaryObjectLocation (96x), 27% getLastPromoted (96x)] (387x)
2025-07-30 15:09:53,978 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168aa252c70_0_66168aa252c70_0_: finished. Total: 0.111 s, CPU [user: 0.0541 s, system: 0.00878 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.054 s [55% getReadConfiguration (162x), 45% getReadUserConfiguration (10x)] (172x), svn: 0.0481 s [56% info (19x), 36% getFile content (15x)] (36x), resolve: 0.0281 s [100% User (9x)] (9x), ObjectMaps: 0.0133 s [58% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 15:09:54,167 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168aa27a072_0_66168aa27a072_0_: finished. Total: 0.143 s, CPU [user: 0.0488 s, system: 0.00462 s], Allocated memory: 19.9 MB, svn: 0.112 s [77% getDir2 content (17x), 23% getFile content (44x)] (62x), RepositoryConfigService: 0.042 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 15:09:54,907 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168aa29e073_0_66168aa29e073_0_: finished. Total: 0.74 s, CPU [user: 0.337 s, system: 0.0172 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.528 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.409 s [59% getFile content (412x), 41% getDir2 content (21x)] (434x)
2025-07-30 15:09:55,290 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66168aa374876_0_66168aa374876_0_: finished. Total: 0.263 s, CPU [user: 0.104 s, system: 0.0056 s], Allocated memory: 384.3 MB, svn: 0.173 s [55% getDir2 content (21x), 45% getFile content (185x)] (207x), RepositoryConfigService: 0.153 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 15:09:55,290 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.76 s, CPU [user: 0.947 s, system: 0.127 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.64 s [43% getDir2 content (115x), 33% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.855 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.447 s [89% Category (96x)] (117x), ObjectMaps: 0.163 s [46% getPrimaryObjectProperty (108x), 29% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
2025-07-30 15:09:55,290 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.39 s [45% getDatedRevision (362x), 29% getDir2 content (115x), 23% getFile content (807x)] (1324x), RepositoryConfigService: 0.855 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.447 s [89% Category (96x)] (118x), ObjectMaps: 0.163 s [46% getPrimaryObjectProperty (108x), 29% getPrimaryObjectLocation (114x), 25% getLastPromoted (108x)] (442x)
