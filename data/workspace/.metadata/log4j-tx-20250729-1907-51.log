2025-07-29 19:07:56,648 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0465 s [64% update (144x), 35% query (12x)] (221x), svn: 0.0122 s [66% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-07-29 19:07:56,758 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0334 s [53% getDir2 content (2x), 41% info (3x)] (6x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.661 s, CPU [user: 0.209 s, system: 0.321 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.662 s, CPU [user: 0.18 s, system: 0.267 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.102 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.661 s, CPU [user: 0.0714 s, system: 0.11 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0899 s [82% log2 (10x)] (13x), ObjectMaps: 0.0358 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.662 s, CPU [user: 0.102 s, system: 0.205 s], Allocated memory: 24.1 MB, transactions: 0, ObjectMaps: 0.0626 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.662 s, CPU [user: 0.0702 s, system: 0.134 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0626 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0625 s [78% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-29 19:07:57,422 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.661 s, CPU [user: 0.0605 s, system: 0.102 s], Allocated memory: 9.1 MB, transactions: 0, svn: 0.083 s [36% log2 (5x), 26% info (5x), 19% log (1x)] (18x), ObjectMaps: 0.0666 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 19:07:57,422 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.447 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.3 s [64% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-29 19:07:57,646 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.193 s [100% getReadConfiguration (48x)] (48x), svn: 0.0738 s [87% info (18x)] (38x)
2025-07-29 19:07:58,013 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.294 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.226 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 19:07:58,249 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.221 s [100% doFinishStartup (1x)] (1x), commit: 0.0518 s [100% Revision (1x)] (1x), Lucene: 0.0362 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.017 s [100% objectsToInv (1x)] (1x), DB: 0.0131 s [48% update (3x), 23% query (1x), 19% execute (1x)] (8x)
2025-07-29 19:08:00,711 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.477 s [90% info (158x)] (168x)
2025-07-29 19:08:01,659 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.849 s, CPU [user: 0.00572 s, system: 0.00112 s], Allocated memory: 530.2 kB, transactions: 1
2025-07-29 19:08:01,660 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 27, notification worker: 0.0471 s [88% RevisionActivityCreator (2x)] (6x), resolve: 0.0425 s [68% Revision (2x), 32% User (1x)] (3x), persistence listener: 0.0311 s [62% indexRefreshPersistenceListener (1x), 19% WorkItemActivityCreator (1x)] (7x), Incremental Baseline: 0.0202 s [100% WorkItem (21x)] (21x), Lucene: 0.0114 s [76% add (1x), 24% refresh (1x)] (2x), PullingJob: 0.00833 s [100% collectChanges (1x)] (1x), svn: 0.00824 s [63% testConnection (1x), 37% getLatestRevision (1x)] (2x), ObjectMaps: 0.00247 s [100% getPrimaryObjectLocation (1x)] (1x)
2025-07-29 19:08:01,660 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.952 s, CPU [user: 0.176 s, system: 0.0261 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.771 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0479 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (22x)] (23x)
2025-07-29 19:08:01,996 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615778b53c40_0_6615778b53c40_0_: finished. Total: 1.28 s, CPU [user: 0.397 s, system: 0.0891 s], Allocated memory: 54.5 MB, svn: 0.699 s [45% getDatedRevision (181x), 32% getDir2 content (25x), 22% getFile content (98x)] (307x), resolve: 0.523 s [100% Category (96x)] (96x), ObjectMaps: 0.173 s [48% getPrimaryObjectProperty (96x), 32% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (388x)
2025-07-29 19:08:02,169 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615778ca5048_0_6615778ca5048_0_: finished. Total: 0.101 s, CPU [user: 0.0516 s, system: 0.00803 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0437 s [58% getReadUserConfiguration (10x), 42% getReadConfiguration (162x)] (172x), svn: 0.0425 s [55% info (19x), 39% getFile content (16x)] (37x), resolve: 0.0315 s [100% User (9x)] (9x), ObjectMaps: 0.0151 s [42% getPrimaryObjectProperty (9x), 40% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 19:08:02,350 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615778cc644a_0_6615778cc644a_0_: finished. Total: 0.149 s, CPU [user: 0.0608 s, system: 0.00445 s], Allocated memory: 19.8 MB, svn: 0.104 s [60% getDir2 content (17x), 40% getFile content (44x)] (62x), RepositoryConfigService: 0.0682 s [98% getReadConfiguration (170x)] (192x)
2025-07-29 19:08:03,090 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615778ceb84b_0_6615778ceb84b_0_: finished. Total: 0.74 s, CPU [user: 0.366 s, system: 0.0152 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.575 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.375 s [70% getFile content (412x), 30% getDir2 content (21x)] (434x)
2025-07-29 19:08:03,437 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615778dbd44e_0_6615778dbd44e_0_: finished. Total: 0.247 s, CPU [user: 0.105 s, system: 0.00576 s], Allocated memory: 384.7 MB, RepositoryConfigService: 0.159 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.149 s [54% getFile content (185x), 46% getDir2 content (20x)] (206x)
2025-07-29 19:08:03,437 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.72 s, CPU [user: 1.06 s, system: 0.132 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.5 s [39% getFile content (809x), 37% getDir2 content (114x), 21% getDatedRevision (181x)] (1144x), RepositoryConfigService: 0.897 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.594 s [88% Category (96x)] (117x), ObjectMaps: 0.2 s [49% getPrimaryObjectProperty (110x), 31% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 19:08:03,437 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.27 s [47% getDatedRevision (362x), 26% getFile content (809x), 24% getDir2 content (114x)] (1327x), RepositoryConfigService: 0.897 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.594 s [88% Category (96x)] (118x), ObjectMaps: 0.2 s [49% getPrimaryObjectProperty (110x), 31% getPrimaryObjectLocation (116x)] (452x)
2025-07-29 19:08:07,163 [ajp-nio-127.0.0.1-8889-exec-2 | cID:55de4518-0a465820-1fe37814-27e5464c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.355 s, CPU [user: 0.221 s, system: 0.0308 s], Allocated memory: 37.3 MB, transactions: 2, PolarionAuthenticator: 0.308 s [100% authenticate (1x)] (1x)
2025-07-29 19:08:07,466 [ajp-nio-127.0.0.1-8889-exec-4 | cID:55de4743-0a465820-1fe37814-77411aa7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.102 s, CPU [user: 0.0633 s, system: 0.00756 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 19:08:07,511 [ajp-nio-127.0.0.1-8889-exec-7 | cID:55de475b-0a465820-1fe37814-7fc499f7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753787287333': Total: 0.121 s, CPU [user: 0.0438 s, system: 0.00748 s], Allocated memory: 7.5 MB, transactions: 1, RepositoryConfigService: 0.0553 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 19:08:07,526 [ajp-nio-127.0.0.1-8889-exec-6 | cID:55de475b-0a465820-1fe37814-88d30a8e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753787287332': Total: 0.139 s, CPU [user: 0.0266 s, system: 0.00292 s], Allocated memory: 2.7 MB, transactions: 1, RepositoryConfigService: 0.072 s [100% getReadConfiguration (1x)] (1x)
