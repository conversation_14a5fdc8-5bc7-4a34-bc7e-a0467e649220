2025-07-29 22:08:44,346 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0751 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0212 s [68% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-29 22:08:44,493 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0433 s [63% getDir2 content (2x), 29% info (3x)] (6x)
2025-07-29 22:08:45,272 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.772 s, CPU [user: 0.228 s, system: 0.268 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.139 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 22:08:45,272 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.771 s, CPU [user: 0.256 s, system: 0.326 s], Allocated memory: 68.4 MB, transactions: 0, ObjectMaps: 0.121 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-29 22:08:45,272 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.772 s, CPU [user: 0.0564 s, system: 0.0845 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0648 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0547 s [60% log2 (5x), 32% getLatestRevision (1x)] (7x)
2025-07-29 22:08:45,273 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.772 s, CPU [user: 0.116 s, system: 0.126 s], Allocated memory: 14.4 MB, transactions: 0, svn: 0.153 s [51% log2 (10x), 15% info (5x), 11% log (1x), 11% getLatestRevision (3x)] (24x), ObjectMaps: 0.074 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 22:08:45,272 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.772 s, CPU [user: 0.0889 s, system: 0.0932 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.097 s [81% log2 (10x)] (13x), ObjectMaps: 0.0396 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-29 22:08:45,272 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.772 s, CPU [user: 0.149 s, system: 0.204 s], Allocated memory: 24.6 MB, transactions: 0, ObjectMaps: 0.0948 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.044 s [72% log2 (5x), 16% testConnection (1x)] (7x)
2025-07-29 22:08:45,273 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.533 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.403 s [62% log2 (36x), 16% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-29 22:08:45,416 [main | u:p] INFO  TXLOGGER - Tx 6615a0e9c0401_0_6615a0e9c0401_0_: finished. Total: 0.102 s, CPU [user: 0.0791 s, system: 0.00354 s], Allocated memory: 21.8 MB
2025-07-29 22:08:45,591 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.262 s [100% getReadConfiguration (48x)] (48x), svn: 0.113 s [86% info (18x)] (38x)
2025-07-29 22:08:45,961 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.288 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.216 s [100% getReadConfiguration (54x)] (54x)
2025-07-29 22:08:46,222 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.131 s, CPU [user: 0.0287 s, system: 0.00776 s], Allocated memory: 10.5 MB, GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-07-29 22:08:46,291 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.316 s [100% doFinishStartup (1x)] (1x), commit: 0.0638 s [100% Revision (1x)] (1x), DB: 0.0417 s [56% update (3x), 34% query (1x)] (8x), Lucene: 0.0414 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0191 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0182 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-29 22:09:27,781 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.596 s [70% info (158x), 22% getLatestRevision (14x)] (182x), PullingJob: 0.129 s [100% collectChanges (13x)] (13x)
2025-07-29 22:09:28,848 [DBHistoryCreator-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.935 s, CPU [user: 0.00311 s, system: 0.00167 s], Allocated memory: 200.2 kB, transactions: 1
2025-07-29 22:09:28,848 [DBHistoryCreator-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.934 s, CPU [user: 0.00223 s, system: 0.00135 s], Allocated memory: 180.9 kB, transactions: 1
2025-07-29 22:09:28,848 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.936 s, CPU [user: 0.00435 s, system: 0.00164 s], Allocated memory: 191.4 kB, transactions: 1
2025-07-29 22:09:28,848 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.937 s, CPU [user: 0.00332 s, system: 0.00182 s], Allocated memory: 202.5 kB, transactions: 1
2025-07-29 22:09:28,849 [DBHistoryCreator-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.936 s, CPU [user: 0.0044 s, system: 0.00206 s], Allocated memory: 458.8 kB, transactions: 1
2025-07-29 22:09:28,850 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 31, resolve: 0.0627 s [82% User (2x)] (4x), Lucene: 0.0591 s [51% add (1x), 49% refresh (5x)] (6x), persistence listener: 0.0507 s [79% indexRefreshPersistenceListener (1x), 6% WorkItemActivityCreator (1x)] (7x), notification worker: 0.0502 s [51% RevisionActivityCreator (2x), 22% WorkItemActivityCreator (1x), 14% PlanActivityCreator (1x)] (6x), Incremental Baseline: 0.0331 s [100% WorkItem (21x)] (21x), DB: 0.0198 s [57% update (5x), 43% commit (5x)] (10x), ObjectMaps: 0.0108 s [100% getPrimaryObjectLocation (2x)] (2x), EHCache: 0.00764 s [100% GET (16x)] (37x), PullingJob: 0.00537 s [100% collectChanges (1x)] (1x), svn: 0.00527 s [100% getLatestRevision (1x)] (1x)
2025-07-29 22:09:28,851 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.945 s, CPU [user: 0.196 s, system: 0.0322 s], Allocated memory: 18.5 MB, transactions: 23, svn: 0.821 s [97% getDatedRevision (181x)] (183x), Lucene: 0.0589 s [74% buildBaselineSnapshots (1x), 26% buildBaseline (22x)] (23x)
2025-07-29 22:09:29,257 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a1133cc40_0_6615a1133cc40_0_: finished. Total: 1.46 s, CPU [user: 0.487 s, system: 0.103 s], Allocated memory: 54.2 MB, svn: 0.828 s [45% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.536 s [100% Category (96x)] (96x), ObjectMaps: 0.175 s [39% getPrimaryObjectProperty (96x), 38% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (388x)
2025-07-29 22:09:29,557 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a114c3c48_0_6615a114c3c48_0_: finished. Total: 0.197 s, CPU [user: 0.0847 s, system: 0.0167 s], Allocated memory: 8.4 MB, svn: 0.0849 s [53% info (19x), 39% getFile content (16x)] (37x), RepositoryConfigService: 0.0819 s [57% getReadUserConfiguration (10x), 43% getReadConfiguration (162x)] (172x), resolve: 0.0728 s [100% User (9x)] (9x), ObjectMaps: 0.0347 s [51% getPrimaryObjectProperty (9x), 36% getPrimaryObjectLocation (9x)] (37x)
2025-07-29 22:09:29,808 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a1150604a_0_6615a1150604a_0_: finished. Total: 0.184 s, CPU [user: 0.0503 s, system: 0.00578 s], Allocated memory: 19.9 MB, svn: 0.154 s [86% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0372 s [97% getReadConfiguration (170x)] (192x)
2025-07-29 22:09:30,631 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a1153404b_0_6615a1153404b_0_: finished. Total: 0.823 s, CPU [user: 0.368 s, system: 0.0264 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.651 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.436 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x)
2025-07-29 22:09:31,064 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6615a1162244e_0_6615a1162244e_0_: finished. Total: 0.303 s, CPU [user: 0.129 s, system: 0.0077 s], Allocated memory: 384.1 MB, RepositoryConfigService: 0.202 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.186 s [57% getFile content (185x), 43% getDir2 content (20x)] (206x)
2025-07-29 22:09:31,065 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.27 s, CPU [user: 1.23 s, system: 0.176 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.87 s [39% getDir2 content (114x), 37% getFile content (809x), 20% getDatedRevision (181x)] (1144x), RepositoryConfigService: 1.06 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.665 s [81% Category (96x)] (117x), ObjectMaps: 0.227 s [44% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-29 22:09:31,065 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 55, svn: 2.69 s [44% getDatedRevision (362x), 27% getDir2 content (114x), 25% getFile content (809x)] (1327x), RepositoryConfigService: 1.06 s [93% getReadConfiguration (12019x)] (12691x), resolve: 0.665 s [81% Category (96x)] (118x), ObjectMaps: 0.227 s [44% getPrimaryObjectProperty (110x), 36% getPrimaryObjectLocation (116x), 20% getLastPromoted (110x)] (452x)
2025-07-29 22:09:41,447 [ajp-nio-127.0.0.1-8889-exec-2 | cID:56848031-0a465820-71b03e9a-0e7e4751] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.532 s, CPU [user: 0.254 s, system: 0.0872 s], Allocated memory: 42.8 MB, transactions: 2, PolarionAuthenticator: 0.461 s [100% authenticate (1x)] (1x)
2025-07-29 22:09:41,945 [ajp-nio-127.0.0.1-8889-exec-6 | cID:568483ac-0a465820-71b03e9a-cdd001b1] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.14 s, CPU [user: 0.0162 s, system: 0.00448 s], Allocated memory: 2.1 MB, transactions: 0
2025-07-29 22:09:41,945 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5684838f-0a465820-71b03e9a-9bcbc603] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.17 s, CPU [user: 0.0717 s, system: 0.0154 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-29 22:09:41,993 [ajp-nio-127.0.0.1-8889-exec-7 | cID:568483ac-0a465820-71b03e9a-d969f90c] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753798181712': Total: 0.188 s, CPU [user: 0.0438 s, system: 0.00664 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-29 22:09:42,071 [ajp-nio-127.0.0.1-8889-exec-8 | cID:568483ae-0a465820-71b03e9a-7cb1608d | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753798181713] INFO  TXLOGGER - Tx 6615a12107c51_0_6615a12107c51_0_: finished. Total: 0.152 s, CPU [user: 0.0587 s, system: 0.0171 s], Allocated memory: 6.5 MB, svn: 0.0137 s [76% testConnection (1x), 24% getFile content (1x)] (3x)
2025-07-29 22:09:42,087 [ajp-nio-127.0.0.1-8889-exec-8 | cID:568483ae-0a465820-71b03e9a-7cb1608d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753798181713': Total: 0.281 s, CPU [user: 0.0695 s, system: 0.0191 s], Allocated memory: 7.8 MB, transactions: 1, RepositoryConfigService: 0.158 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 22:09:42,141 [ajp-nio-127.0.0.1-8889-exec-5 | cID:568483ac-0a465820-71b03e9a-e9f6d535] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753798181714': Total: 0.335 s, CPU [user: 0.0357 s, system: 0.00422 s], Allocated memory: 2.9 MB, transactions: 1, RepositoryConfigService: 0.218 s [100% getReadConfiguration (1x)] (1x)
2025-07-29 22:09:49,215 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.009521484375
