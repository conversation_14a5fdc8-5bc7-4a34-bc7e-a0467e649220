2025-07-30 11:51:30,865 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0655 s [68% update (144x), 31% query (12x)] (221x), svn: 0.0153 s [59% getLatestRevision (2x), 30% testConnection (1x)] (4x)
2025-07-30 11:51:30,970 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0304 s [66% getDir2 content (2x), 28% info (3x)] (6x)
2025-07-30 11:51:31,678 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0955 s, system: 0.144 s], Allocated memory: 12.2 MB, transactions: 0, ObjectMaps: 0.0897 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0708 s [80% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-07-30 11:51:31,678 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.253 s, system: 0.33 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 11:51:31,678 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0525 s, system: 0.0932 s], Allocated memory: 7.4 MB, transactions: 0, ObjectMaps: 0.0672 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0396 s [71% log2 (5x), 16% getLatestRevision (1x)] (7x)
2025-07-30 11:51:31,678 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.12 s, system: 0.209 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0733 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:51:31,679 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.0995 s, system: 0.12 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.123 s [56% log2 (10x), 14% info (5x), 11% log (1x)] (24x), ObjectMaps: 0.0593 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:51:31,678 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.705 s, CPU [user: 0.203 s, system: 0.27 s], Allocated memory: 52.7 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:51:31,679 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.522 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.299 s [65% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 11:51:31,899 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.191 s [100% getReadConfiguration (48x)] (48x), svn: 0.0739 s [87% info (18x)] (38x)
2025-07-30 11:51:32,203 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.227 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.17 s [100% getReadConfiguration (94x)] (94x), PersistenceEngineListener: 0.0119 s [100% objectsModified (8x)] (16x)
2025-07-30 11:51:32,509 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.277 s [100% doFinishStartup (1x)] (1x), commit: 0.0494 s [73% Revision (1x), 27% BuildArtifact (1x)] (2x), DB: 0.0457 s [45% update (45x), 26% execute (15x), 19% query (20x)] (122x), Lucene: 0.0365 s [100% refresh (2x)] (2x), resolve: 0.03 s [89% BuildArtifact (11x)] (13x), GlobalHandler: 0.0246 s [93% put (13x)] (26x), SubterraURITable: 0.0225 s [100% addIfNotExistsDB (20x)] (20x)
2025-07-30 11:51:34,712 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.323 s [89% info (158x)] (168x)
2025-07-30 11:51:34,716 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 0, notification worker: 0.000451 s [100% BuildActivityCreator (1x)] (1x), EHCache: 0.0000586 s [100% GET (2x)] (2x)
2025-07-30 11:51:34,922 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165d3e0f443_0_66165d3e0f443_0_: finished. Total: 0.205 s, CPU [user: 0.116 s, system: 0.0129 s], Allocated memory: 20.7 MB, resolve: 0.0662 s [62% User (2x), 34% Project (1x)] (5x), ObjectMaps: 0.0221 s [53% getPrimaryObjectLocation (2x), 41% getPrimaryObjectProperty (2x)] (11x), svn: 0.0188 s [38% getLatestRevision (2x), 21% testConnection (1x), 21% log (1x), 14% getFile content (2x)] (8x), Lucene: 0.0179 s [100% search (1x)] (1x)
2025-07-30 11:51:35,453 [DBHistoryCreator-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.673 s, CPU [user: 0.00385 s, system: 0.00121 s], Allocated memory: 491.8 kB, transactions: 1
2025-07-30 11:51:35,453 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.673 s, CPU [user: 0.00433 s, system: 0.00102 s], Allocated memory: 275.4 kB, transactions: 1
2025-07-30 11:51:35,454 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 69, notification worker: 0.218 s [98% RevisionActivityCreator (18x)] (53x), resolve: 0.0785 s [66% User (3x), 29% Project (1x)] (7x), Lucene: 0.0376 s [48% search (1x), 33% add (1x)] (4x), ObjectMaps: 0.0269 s [61% getPrimaryObjectLocation (3x), 34% getPrimaryObjectProperty (2x)] (12x), Incremental Baseline: 0.0235 s [100% WorkItem (22x)] (22x), svn: 0.0188 s [38% getLatestRevision (2x), 21% testConnection (1x), 21% log (1x), 14% getFile content (2x)] (8x), persistence listener: 0.0157 s [82% indexRefreshPersistenceListener (9x)] (63x)
2025-07-30 11:51:35,454 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.742 s, CPU [user: 0.137 s, system: 0.0219 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.6 s [98% getDatedRevision (181x)] (183x), Lucene: 0.041 s [75% buildBaselineSnapshots (1x), 25% buildBaseline (23x)] (24x)
2025-07-30 11:51:35,836 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d3e10446_0_66165d3e10446_0_: finished. Total: 1.11 s, CPU [user: 0.273 s, system: 0.0719 s], Allocated memory: 47.1 MB, svn: 0.679 s [53% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.289 s [100% Category (96x)] (96x), ObjectMaps: 0.102 s [44% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 25% getLastPromoted (96x)] (387x)
2025-07-30 11:51:36,013 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d3f37c70_0_66165d3f37c70_0_: finished. Total: 0.109 s, CPU [user: 0.054 s, system: 0.00905 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0511 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (162x)] (172x), svn: 0.0472 s [57% info (19x), 36% getFile content (15x)] (36x), resolve: 0.0297 s [100% User (9x)] (9x), ObjectMaps: 0.0132 s [55% getPrimaryObjectProperty (8x), 25% getLastPromoted (8x), 20% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:51:36,251 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d3f5e072_0_66165d3f5e072_0_: finished. Total: 0.194 s, CPU [user: 0.0703 s, system: 0.007 s], Allocated memory: 19.9 MB, svn: 0.145 s [62% getDir2 content (17x), 38% getFile content (44x)] (62x), RepositoryConfigService: 0.0858 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 11:51:37,280 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d3f8ec73_0_66165d3f8ec73_0_: finished. Total: 1.03 s, CPU [user: 0.405 s, system: 0.0411 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.799 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.481 s [69% getFile content (412x), 31% getDir2 content (21x)] (434x)
2025-07-30 11:51:37,405 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d4090074_0_66165d4090074_0_: finished. Total: 0.124 s, CPU [user: 0.024 s, system: 0.00362 s], Allocated memory: 18.0 MB, svn: 0.11 s [87% getDir2 content (18x)] (48x), RepositoryConfigService: 0.025 s [93% getReadConfiguration (124x)] (148x)
2025-07-30 11:51:37,929 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d40ba476_0_66165d40ba476_0_: finished. Total: 0.48 s, CPU [user: 0.154 s, system: 0.0196 s], Allocated memory: 387.4 MB, svn: 0.347 s [62% getDir2 content (21x), 38% getFile content (185x)] (207x), RepositoryConfigService: 0.244 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 11:51:37,929 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.21 s, CPU [user: 1.05 s, system: 0.162 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.89 s [43% getDir2 content (115x), 36% getFile content (807x), 19% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.25 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.347 s [83% Category (96x)] (117x)
2025-07-30 11:51:37,930 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.5 s [38% getDatedRevision (362x), 32% getDir2 content (115x), 27% getFile content (807x)] (1326x), RepositoryConfigService: 1.25 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.347 s [83% Category (96x)] (118x)
2025-07-30 11:51:38,568 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59750600-7f000001-1bf093f5-cb6a366b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.327 s, CPU [user: 0.195 s, system: 0.0324 s], Allocated memory: 42.7 MB, transactions: 2, PolarionAuthenticator: 0.291 s [100% authenticate (1x)] (1x)
2025-07-30 11:51:38,834 [ajp-nio-127.0.0.1-8889-exec-4 | cID:597507e4-7f000001-1bf093f5-3eacfcbe] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.11 s, CPU [user: 0.0627 s, system: 0.0072 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:51:38,851 [ajp-nio-127.0.0.1-8889-exec-6 | cID:597507fb-7f000001-1bf093f5-191eac8f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753847498682': Total: 0.103 s, CPU [user: 0.0282 s, system: 0.0042 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 11:51:38,915 [ajp-nio-127.0.0.1-8889-exec-5 | cID:597507fb-7f000001-1bf093f5-02a2874e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753847498684': Total: 0.167 s, CPU [user: 0.0566 s, system: 0.00835 s], Allocated memory: 7.1 MB, transactions: 1, RepositoryConfigService: 0.0822 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:51:38,943 [ajp-nio-127.0.0.1-8889-exec-8 | cID:597507fc-7f000001-1bf093f5-fd62be17] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753847498683': Total: 0.194 s, CPU [user: 0.0434 s, system: 0.00481 s], Allocated memory: 4.8 MB, transactions: 1, RepositoryConfigService: 0.108 s [100% getReadConfiguration (1x)] (1x)
