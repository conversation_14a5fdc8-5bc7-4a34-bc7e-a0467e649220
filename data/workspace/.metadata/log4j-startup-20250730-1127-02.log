2025-07-30 11:27:03,044 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:27:03,044 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:27:03,044 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:27:03,044 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:27:03,045 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:27:03,045 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:03,045 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:27:07,544 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:27:07,723 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.179 s. ]
2025-07-30 11:27:07,723 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:27:07,771 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0485 s. ]
2025-07-30 11:27:07,836 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:27:07,973 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 60 s. ]
2025-07-30 11:27:08,191 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.15 s. ]
2025-07-30 11:27:08,280 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:08,280 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:27:08,305 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 11:27:08,305 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:08,305 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (1/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (4/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (3/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (5/9)
2025-07-30 11:27:08,310 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (6/9)
2025-07-30 11:27:08,318 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:27:08,463 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:27:08,539 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:27:09,013 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-30 11:27:09,025 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,025 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:27:09,232 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:27:09,247 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 11:27:09,273 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,273 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:27:09,276 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:27:09,327 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:27:09,394 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:27:09,430 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:27:09,456 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:27:09,495 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:27:09,539 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:27:09,590 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:27:09,638 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:27:09,638 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.39 s. ]
2025-07-30 11:27:09,638 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,638 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:27:09,652 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 11:27:09,653 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,653 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:27:09,766 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:27:09,771 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:27:09,909 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 11:27:09,909 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,909 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:27:09,916 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:27:09,916 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:27:09,916 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:27:22,948 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 13.03 s. ]
2025-07-30 11:27:22,948 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:27:22,948 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 19.9 s. ]
2025-07-30 11:27:22,948 [main] INFO  com.polarion.platform.startup - ****************************************************************
