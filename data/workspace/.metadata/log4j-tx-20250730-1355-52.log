2025-07-30 13:55:57,535 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0427 s [68% update (144x), 32% query (12x)] (221x), svn: 0.0169 s [69% getLatestRevision (2x), 22% testConnection (1x)] (4x)
2025-07-30 13:55:57,642 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.028 s [61% getDir2 content (2x), 33% info (3x)] (6x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.742 s, CPU [user: 0.104 s, system: 0.206 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0658 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.255 s, system: 0.348 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.159 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.742 s, CPU [user: 0.191 s, system: 0.278 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.115 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.0497 s, system: 0.0987 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0748 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.742 s, CPU [user: 0.0902 s, system: 0.143 s], Allocated memory: 14.6 MB, transactions: 0, svn: 0.1 s [46% log2 (10x), 19% info (5x), 13% log (1x), 12% getLatestRevision (3x)] (24x), ObjectMaps: 0.0815 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:55:58,388 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.741 s, CPU [user: 0.0749 s, system: 0.121 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0771 s [84% log2 (10x)] (13x), ObjectMaps: 0.0559 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 13:55:58,389 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.552 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.292 s [64% log2 (36x), 13% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 13:55:58,518 [main | u:p] INFO  TXLOGGER - Tx 661679b6d2c01_0_661679b6d2c01_0_: finished. Total: 0.106 s, CPU [user: 0.075 s, system: 0.00359 s], Allocated memory: 21.8 MB
2025-07-30 13:55:58,642 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.218 s [100% getReadConfiguration (48x)] (48x), svn: 0.0742 s [85% info (18x)] (38x)
2025-07-30 13:55:58,929 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.222 s [77% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.161 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 13:55:59,148 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.202 s [100% doFinishStartup (1x)] (1x), commit: 0.0412 s [100% Revision (1x)] (1x), Lucene: 0.0341 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0128 s [100% objectsToInv (1x)] (1x), DB: 0.0108 s [39% update (3x), 33% query (1x), 15% execute (1x)] (8x)
2025-07-30 13:56:01,675 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.354 s [90% info (158x)] (168x)
2025-07-30 13:56:01,916 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661679ba04442_0_661679ba04442_0_: finished. Total: 0.234 s, CPU [user: 0.121 s, system: 0.0172 s], Allocated memory: 20.3 MB, resolve: 0.0715 s [57% User (2x), 36% Project (1x)] (5x), Lucene: 0.0244 s [100% search (1x)] (1x), svn: 0.0226 s [33% getLatestRevision (2x), 31% testConnection (1x), 18% log (1x)] (8x), ObjectMaps: 0.0159 s [48% getPrimaryObjectProperty (2x), 43% getPrimaryObjectLocation (2x)] (11x)
2025-07-30 13:56:02,533 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.767 s, CPU [user: 0.0064 s, system: 0.00121 s], Allocated memory: 316.2 kB, transactions: 1
2025-07-30 13:56:02,533 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.259 s [94% RevisionActivityCreator (2x)] (6x), resolve: 0.0885 s [61% User (3x), 29% Project (1x)] (7x), Lucene: 0.0457 s [53% search (1x), 38% add (1x)] (3x), svn: 0.0333 s [41% testConnection (2x), 35% getLatestRevision (3x), 13% log (1x)] (10x), Incremental Baseline: 0.0249 s [100% WorkItem (22x)] (22x), ObjectMaps: 0.0201 s [55% getPrimaryObjectLocation (3x), 38% getPrimaryObjectProperty (2x)] (12x), persistence listener: 0.0188 s [84% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 13:56:02,534 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.861 s, CPU [user: 0.166 s, system: 0.0256 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.686 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0488 s [77% buildBaselineSnapshots (1x), 23% buildBaseline (23x)] (24x)
2025-07-30 13:56:02,891 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679ba05045_0_661679ba05045_0_: finished. Total: 1.21 s, CPU [user: 0.318 s, system: 0.0802 s], Allocated memory: 47.1 MB, svn: 0.7 s [48% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.362 s [100% Category (96x)] (96x), ObjectMaps: 0.126 s [44% getPrimaryObjectProperty (96x), 31% getPrimaryObjectLocation (96x), 24% getLastPromoted (96x)] (387x)
2025-07-30 13:56:03,079 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679bb44448_0_661679bb44448_0_: finished. Total: 0.118 s, CPU [user: 0.0591 s, system: 0.00987 s], Allocated memory: 8.1 MB, RepositoryConfigService: 0.0542 s [54% getReadUserConfiguration (10x), 46% getReadConfiguration (162x)] (172x), svn: 0.0498 s [57% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0355 s [100% User (9x)] (9x), ObjectMaps: 0.0183 s [57% getPrimaryObjectProperty (8x), 21% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 13:56:03,334 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679bb6c84a_0_661679bb6c84a_0_: finished. Total: 0.211 s, CPU [user: 0.0727 s, system: 0.00655 s], Allocated memory: 19.9 MB, svn: 0.163 s [66% getDir2 content (17x), 34% getFile content (44x)] (62x), RepositoryConfigService: 0.0858 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 13:56:04,183 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679bba184b_0_661679bba184b_0_: finished. Total: 0.849 s, CPU [user: 0.374 s, system: 0.0276 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.584 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.483 s [56% getFile content (412x), 44% getDir2 content (21x)] (434x)
2025-07-30 13:56:05,557 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661679bc9604e_0_661679bc9604e_0_: finished. Total: 1.24 s, CPU [user: 0.326 s, system: 0.12 s], Allocated memory: 385.4 MB, svn: 0.749 s [58% getDir2 content (21x), 42% getFile content (185x)] (207x), RepositoryConfigService: 0.739 s [97% getReadConfiguration (2787x)] (3025x)
2025-07-30 13:56:05,558 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.88 s, CPU [user: 1.24 s, system: 0.256 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.3 s [48% getDir2 content (115x), 35% getFile content (807x)] (1141x), RepositoryConfigService: 1.53 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.429 s [84% Category (96x)] (117x)
2025-07-30 13:56:05,559 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.99 s [37% getDir2 content (115x), 34% getDatedRevision (362x), 27% getFile content (807x)] (1325x), RepositoryConfigService: 1.53 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.43 s [84% Category (96x)] (118x), ObjectMaps: 0.158 s [47% getPrimaryObjectProperty (108x), 30% getPrimaryObjectLocation (114x), 23% getLastPromoted (108x)] (442x)
2025-07-30 13:56:07,003 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59e6fb6d-0a465820-0e967e4e-c1b0eafc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.364 s, CPU [user: 0.212 s, system: 0.0451 s], Allocated memory: 43.1 MB, transactions: 2, PolarionAuthenticator: 0.324 s [100% authenticate (1x)] (1x)
2025-07-30 13:56:07,363 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59e6fdde-0a465820-0e967e4e-5f62df92] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.1 s, CPU [user: 0.0715 s, system: 0.0089 s], Allocated memory: 9.9 MB, transactions: 0
2025-07-30 13:56:07,489 [ajp-nio-127.0.0.1-8889-exec-10 | cID:59e6fe5c-0a465820-0e967e4e-65684248] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753854967234': Total: 0.102 s, CPU [user: 0.0621 s, system: 0.00932 s], Allocated memory: 6.6 MB, transactions: 1, RepositoryConfigService: 0.0879 s [100% getReadConfiguration (1x)] (1x), svn: 0.0137 s [55% getFile content (2x), 44% testConnection (1x)] (4x)
2025-07-30 13:56:07,526 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59e6fe5c-0a465820-0e967e4e-29b10987] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753854967233': Total: 0.138 s, CPU [user: 0.0411 s, system: 0.00331 s], Allocated memory: 4.2 MB, transactions: 1, RepositoryConfigService: 0.111 s [100% getReadConfiguration (1x)] (1x)
