2025-07-30 19:12:43,574 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:43,575 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 19:12:47,871 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 19:12:48,014 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.143 s. ]
2025-07-30 19:12:48,014 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 19:12:48,079 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0657 s. ]
2025-07-30 19:12:48,134 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 19:12:48,245 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 18 s. ]
2025-07-30 19:12:48,475 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.9 s. ]
2025-07-30 19:12:48,566 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:48,566 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 19:12:48,592 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.12 s. ]
2025-07-30 19:12:48,592 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:48,592 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 19:12:48,597 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 19:12:48,602 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 19:12:48,739 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-07-30 19:12:48,833 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-07-30 19:12:49,366 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 19:12:49,377 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:49,377 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 19:12:49,621 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 19:12:49,632 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.27 s. ]
2025-07-30 19:12:49,658 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:49,658 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 19:12:49,661 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 19:12:49,714 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 19:12:49,753 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-07-30 19:12:49,794 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 19:12:49,812 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-07-30 19:12:49,827 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-07-30 19:12:49,850 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 19:12:49,878 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-07-30 19:12:49,910 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-07-30 19:12:49,910 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.28 s. ]
2025-07-30 19:12:49,910 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:49,910 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 19:12:49,927 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 19:12:49,928 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:49,928 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 19:12:50,029 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 19:12:50,032 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 19:12:50,162 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.23 s. ]
2025-07-30 19:12:50,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:50,163 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 19:12:50,170 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 19:12:50,170 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:12:50,170 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 19:12:59,719 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 9.55 s. ]
2025-07-30 19:12:59,722 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:12:59,723 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 16.1 s. ]
2025-07-30 19:12:59,723 [main] INFO  com.polarion.platform.startup - ****************************************************************
