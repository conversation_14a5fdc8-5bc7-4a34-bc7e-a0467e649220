2025-07-30 17:30:22,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:30:22,777 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 17:30:22,777 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:30:22,777 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 17:30:22,778 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 17:30:22,778 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:22,778 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 17:30:27,973 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 17:30:28,172 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.199 s. ]
2025-07-30 17:30:28,172 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 17:30:28,256 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0841 s. ]
2025-07-30 17:30:28,323 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 17:30:28,488 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 11 s. ]
2025-07-30 17:30:28,734 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.96 s. ]
2025-07-30 17:30:28,874 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:28,874 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 17:30:28,909 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-07-30 17:30:28,910 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:28,910 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 17:30:28,916 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-30 17:30:28,917 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 17:30:28,916 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 17:30:28,916 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 17:30:28,916 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 17:30:28,917 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (6/9)
2025-07-30 17:30:28,956 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 17:30:29,288 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 17:30:29,373 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 17:30:29,949 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 1.04 s. ]
2025-07-30 17:30:29,965 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:29,965 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 17:30:30,268 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 17:30:30,286 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.34 s. ]
2025-07-30 17:30:30,341 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:30,341 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 17:30:30,351 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 17:30:30,415 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 17:30:30,478 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 17:30:30,546 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 17:30:30,616 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 17:30:30,656 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 17:30:30,692 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 17:30:30,749 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 17:30:30,822 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 17:30:30,822 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.54 s. ]
2025-07-30 17:30:30,822 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:30,822 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 17:30:30,844 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 17:30:30,844 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:30,844 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 17:30:31,064 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 17:30:31,069 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 17:30:31,276 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.43 s. ]
2025-07-30 17:30:31,276 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:31,276 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 17:30:31,285 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 17:30:31,285 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 17:30:31,285 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 17:30:34,660 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.37 s. ]
2025-07-30 17:30:34,660 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 17:30:34,660 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.9 s. ]
2025-07-30 17:30:34,660 [main] INFO  com.polarion.platform.startup - ****************************************************************
