2025-07-30 17:55:35,621 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0532 s [67% update (144x), 33% query (12x)] (221x), svn: 0.0271 s [58% getLatestRevision (2x), 29% testConnection (1x)] (4x)
2025-07-30 17:55:35,748 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0341 s [59% getDir2 content (2x), 32% info (3x)] (6x)
2025-07-30 17:55:36,784 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.0938 s, system: 0.129 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.104 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0975 s [79% log2 (10x), 15% getLatestRevision (2x)] (13x)
2025-07-30 17:55:36,785 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.249 s, system: 0.303 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.194 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0858 s [72% getLatestRevision (1x), 19% log2 (1x)] (3x)
2025-07-30 17:55:36,785 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.139 s, system: 0.215 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.187 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0681 s [72% log2 (5x), 19% getLatestRevision (1x)] (7x)
2025-07-30 17:55:36,784 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.0913 s, system: 0.1 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.114 s [84% log2 (10x)] (13x), ObjectMaps: 0.0558 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:55:36,784 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.0555 s, system: 0.0844 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.07 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0553 s [69% log2 (5x), 18% getLatestRevision (1x)] (7x)
2025-07-30 17:55:36,785 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 1.03 s, CPU [user: 0.325 s, system: 0.367 s], Allocated memory: 72.7 MB, transactions: 0, ObjectMaps: 0.141 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.113 s [32% log2 (5x), 23% info (5x), 20% log (1x), 15% getLatestRevision (2x)] (18x)
2025-07-30 17:55:36,786 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.752 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.534 s [59% log2 (36x), 24% getLatestRevision (9x)] (61x)
2025-07-30 17:55:36,938 [main | u:p] INFO  TXLOGGER - Tx 6616b0903e001_0_6616b0903e001_0_: finished. Total: 0.111 s, CPU [user: 0.0884 s, system: 0.0049 s], Allocated memory: 21.8 MB
2025-07-30 17:55:37,113 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.268 s [100% getReadConfiguration (48x)] (48x), svn: 0.103 s [82% info (18x)] (38x)
2025-07-30 17:55:37,565 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.35 s [74% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.265 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:55:37,858 [PolarionDocIdCreator-2] INFO  TXLOGGER - Tx fillBloomFilter [User]: finished. Total: 0.163 s, CPU [user: 0.0301 s, system: 0.00732 s], Allocated memory: 2.2 MB
2025-07-30 17:55:37,877 [PolarionDocIdCreator-5] INFO  TXLOGGER - Tx fillBloomFilter [WikiPage]: finished. Total: 0.183 s, CPU [user: 0.0325 s, system: 0.00697 s], Allocated memory: 2.5 MB
2025-07-30 17:55:37,877 [PolarionDocIdCreator-3] INFO  TXLOGGER - Tx fillBloomFilter [Category]: finished. Total: 0.183 s, CPU [user: 0.00513 s, system: 0.0024 s], Allocated memory: 840.0 kB
2025-07-30 17:55:37,898 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [WorkItem]: finished. Total: 0.138 s, CPU [user: 0.0309 s, system: 0.0136 s], Allocated memory: 13.0 MB
2025-07-30 17:55:37,922 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.227 s, CPU [user: 0.0348 s, system: 0.0121 s], Allocated memory: 10.5 MB
2025-07-30 17:55:37,954 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.373 s [100% doFinishStartup (1x)] (1x), Lucene: 0.131 s [100% refresh (1x)] (1x), commit: 0.053 s [100% Revision (1x)] (1x), SubterraURITable: 0.0195 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-30 17:56:01,547 [main | u:p | u:p] INFO  TXLOGGER - Tx 6616b0a84203f_0_6616b0a84203f_0_: finished. Total: 0.13 s, CPU [user: 0.074 s, system: 0.014 s], Allocated memory: 7.5 MB, GlobalHandler: 0.0152 s [96% applyTxChanges (1x)] (4x)
2025-07-30 17:56:02,376 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.537 s [72% info (158x), 19% getLatestRevision (9x)] (177x), PullingJob: 0.0844 s [100% collectChanges (8x)] (8x)
2025-07-30 17:56:02,843 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616b0a937845_0_6616b0a937845_0_: finished. Total: 0.445 s, CPU [user: 0.16 s, system: 0.0302 s], Allocated memory: 20.3 MB, resolve: 0.198 s [69% User (2x), 29% Project (1x)] (5x), ObjectMaps: 0.092 s [69% getPrimaryObjectLocation (2x), 20% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0539 s [100% search (1x)] (1x), svn: 0.0344 s [31% testConnection (1x), 24% getLatestRevision (2x), 19% log (1x), 19% getFile content (2x)] (8x)
2025-07-30 17:56:03,477 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.02 s, CPU [user: 0.00752 s, system: 0.00261 s], Allocated memory: 315.7 kB, transactions: 1
2025-07-30 17:56:03,481 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.458 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.214 s [70% User (3x), 27% Project (1x)] (7x), ObjectMaps: 0.0951 s [70% getPrimaryObjectLocation (3x), 19% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0844 s [64% search (1x), 28% add (1x)] (3x), Incremental Baseline: 0.0512 s [100% WorkItem (22x)] (22x), svn: 0.0344 s [31% testConnection (1x), 24% getLatestRevision (2x), 19% log (1x), 19% getFile content (2x)] (8x), persistence listener: 0.0281 s [86% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 17:56:03,482 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.11 s, CPU [user: 0.172 s, system: 0.0322 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.91 s [98% getDatedRevision (181x)] (183x)
2025-07-30 17:56:04,225 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b0a934840_0_6616b0a934840_0_: finished. Total: 1.84 s, CPU [user: 0.421 s, system: 0.12 s], Allocated memory: 46.6 MB, svn: 1.21 s [59% getDatedRevision (181x), 28% getDir2 content (25x)] (307x), resolve: 0.53 s [100% Category (96x)] (96x), ObjectMaps: 0.204 s [41% getPrimaryObjectProperty (96x), 37% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (387x)
2025-07-30 17:56:04,430 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b0ab12848_0_6616b0ab12848_0_: finished. Total: 0.131 s, CPU [user: 0.0633 s, system: 0.0135 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0611 s [52% getReadUserConfiguration (10x), 48% getReadConfiguration (162x)] (172x), svn: 0.0545 s [55% info (19x), 35% getFile content (15x)] (36x), resolve: 0.0388 s [100% User (9x)] (9x), ObjectMaps: 0.0171 s [57% getPrimaryObjectProperty (8x), 22% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 17:56:04,693 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b0ab3e04a_0_6616b0ab3e04a_0_: finished. Total: 0.221 s, CPU [user: 0.0678 s, system: 0.0136 s], Allocated memory: 19.9 MB, svn: 0.163 s [62% getDir2 content (17x), 38% getFile content (44x)] (62x), RepositoryConfigService: 0.101 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 17:56:05,665 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b0ab7544b_0_6616b0ab7544b_0_: finished. Total: 0.971 s, CPU [user: 0.386 s, system: 0.0506 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.66 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.55 s [55% getFile content (412x), 45% getDir2 content (21x)] (434x)
2025-07-30 17:56:06,050 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b0ac8804e_0_6616b0ac8804e_0_: finished. Total: 0.258 s, CPU [user: 0.107 s, system: 0.00719 s], Allocated memory: 384.9 MB, RepositoryConfigService: 0.169 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.162 s [55% getFile content (185x), 45% getDir2 content (21x)] (207x)
2025-07-30 17:56:06,051 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.67 s, CPU [user: 1.13 s, system: 0.221 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.29 s [38% getDir2 content (115x), 31% getDatedRevision (181x), 29% getFile content (807x)] (1141x), RepositoryConfigService: 1.06 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.597 s [89% Category (96x)] (117x), ObjectMaps: 0.23 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 17:56:06,051 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.21 s [50% getDatedRevision (362x), 27% getDir2 content (115x), 20% getFile content (807x)] (1325x), RepositoryConfigService: 1.06 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.598 s [89% Category (96x)] (118x), ObjectMaps: 0.23 s [43% getPrimaryObjectProperty (108x), 36% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
