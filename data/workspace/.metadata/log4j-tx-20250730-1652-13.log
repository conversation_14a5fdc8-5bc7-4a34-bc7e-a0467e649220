2025-07-30 16:52:19,557 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0547 s [57% update (144x), 42% query (12x)] (221x), svn: 0.013 s [45% getLatestRevision (2x), 43% testConnection (1x)] (4x)
2025-07-30 16:52:19,683 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0403 s [66% getDir2 content (2x), 27% info (3x)] (6x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.0473 s, system: 0.0905 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0628 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.104 s, system: 0.198 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0636 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.0796 s, system: 0.133 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0786 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0709 s [75% log2 (10x), 14% getLatestRevision (2x)] (13x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.0777 s, system: 0.105 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0824 s [80% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0541 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.731 s, CPU [user: 0.263 s, system: 0.322 s], Allocated memory: 73.0 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.0925 s [29% info (5x), 22% log (1x), 20% log2 (5x), 15% getLatestRevision (2x)] (18x)
2025-07-30 16:52:20,419 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.732 s, CPU [user: 0.198 s, system: 0.267 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 16:52:20,420 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.487 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.323 s [59% log2 (36x), 15% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 16:52:20,650 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.195 s [100% getReadConfiguration (48x)] (48x), svn: 0.0805 s [82% info (18x)] (38x)
2025-07-30 16:52:21,053 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.33 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.231 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 16:52:21,304 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.237 s [100% doFinishStartup (1x)] (1x), commit: 0.0538 s [100% Revision (1x)] (1x), Lucene: 0.0384 s [100% refresh (1x)] (1x), DB: 0.0152 s [40% query (1x), 37% update (3x), 12% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0151 s [100% objectsToInv (1x)] (1x)
2025-07-30 16:52:24,926 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.495 s [90% info (158x)] (170x)
2025-07-30 16:52:25,227 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a2193b045_0_6616a2193b045_0_: finished. Total: 0.286 s, CPU [user: 0.147 s, system: 0.0183 s], Allocated memory: 20.3 MB, resolve: 0.0928 s [62% User (2x), 34% Project (1x)] (5x), ObjectMaps: 0.0326 s [47% getPrimaryObjectProperty (2x), 40% getPrimaryObjectLocation (2x)] (11x), svn: 0.032 s [36% getLatestRevision (2x), 30% log (1x), 19% testConnection (1x)] (8x), Lucene: 0.0206 s [100% search (1x)] (1x)
2025-07-30 16:52:26,125 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.09 s, CPU [user: 0.00651 s, system: 0.00184 s], Allocated memory: 316.1 kB, transactions: 1
2025-07-30 16:52:26,126 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.299 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.112 s [65% User (3x), 28% Project (1x)] (7x), Incremental Baseline: 0.0585 s [100% WorkItem (22x)] (22x), Full Baseline: 0.0515 s [100% WorkItem (1x)] (1x), Lucene: 0.0482 s [51% add (1x), 43% search (1x)] (3x), ObjectMaps: 0.0355 s [45% getPrimaryObjectLocation (3x), 43% getPrimaryObjectProperty (2x)] (12x), svn: 0.032 s [36% getLatestRevision (2x), 30% log (1x), 19% testConnection (1x)] (8x), persistence listener: 0.0217 s [84% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 16:52:26,127 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.2 s, CPU [user: 0.205 s, system: 0.0329 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.971 s [99% getDatedRevision (181x)] (183x), Lucene: 0.0767 s [83% buildBaselineSnapshots (1x)] (24x)
2025-07-30 16:52:26,693 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21939840_0_6616a21939840_0_: finished. Total: 1.76 s, CPU [user: 0.426 s, system: 0.107 s], Allocated memory: 47.1 MB, svn: 0.943 s [54% getDatedRevision (181x), 29% getDir2 content (25x)] (307x), resolve: 0.515 s [100% Category (96x)] (96x), ObjectMaps: 0.195 s [47% getPrimaryObjectProperty (96x), 30% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 16:52:26,980 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21b11848_0_6616a21b11848_0_: finished. Total: 0.157 s, CPU [user: 0.072 s, system: 0.0142 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0822 s [52% getReadConfiguration (162x), 48% getReadUserConfiguration (10x)] (172x), svn: 0.0701 s [54% info (19x), 40% getFile content (15x)] (36x), resolve: 0.04 s [100% User (9x)] (9x), ObjectMaps: 0.0181 s [56% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x), 21% getLastPromoted (8x)] (32x)
2025-07-30 16:52:27,559 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21b44c4a_0_6616a21b44c4a_0_: finished. Total: 0.53 s, CPU [user: 0.084 s, system: 0.0163 s], Allocated memory: 19.9 MB, svn: 0.317 s [52% getDir2 content (17x), 48% getFile content (44x)] (62x), RepositoryConfigService: 0.317 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 16:52:28,743 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21bca04b_0_6616a21bca04b_0_: finished. Total: 1.18 s, CPU [user: 0.456 s, system: 0.0498 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.879 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.604 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x)
2025-07-30 16:52:29,041 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21cf1c4c_0_6616a21cf1c4c_0_: finished. Total: 0.298 s, CPU [user: 0.0502 s, system: 0.0121 s], Allocated memory: 18.0 MB, svn: 0.27 s [83% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0646 s [96% getReadConfiguration (124x)] (148x)
2025-07-30 16:52:29,436 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a21d4ac4e_0_6616a21d4ac4e_0_: finished. Total: 0.336 s, CPU [user: 0.139 s, system: 0.0153 s], Allocated memory: 386.8 MB, RepositoryConfigService: 0.224 s [97% getReadConfiguration (2787x)] (3025x), svn: 0.207 s [56% getFile content (185x), 44% getDir2 content (21x)] (207x)
2025-07-30 16:52:29,436 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.5 s, CPU [user: 1.31 s, system: 0.23 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.54 s [41% getDir2 content (115x), 36% getFile content (807x), 20% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.63 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.611 s [84% Category (96x)] (117x), ObjectMaps: 0.235 s [48% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 16:52:29,436 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 3.56 s [41% getDatedRevision (362x), 29% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 1.63 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.612 s [84% Category (96x)] (118x), ObjectMaps: 0.235 s [48% getPrimaryObjectProperty (108x), 31% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 16:52:34,059 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.132373046875
2025-07-30 16:52:44,060 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.077978515625
2025-07-30 16:52:46,591 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a88b73b-7f000001-206bd519-9d4d93b0] INFO  TXLOGGER - Summary for 'servlet /polarion/': Total: 0.578 s, CPU [user: 0.257 s, system: 0.09 s], Allocated memory: 37.2 MB, transactions: 2, PolarionAuthenticator: 0.554 s [100% authenticate (1x)] (1x)
2025-07-30 16:52:46,966 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a88ba4b-7f000001-206bd519-359a0dfc] INFO  TXLOGGER - Summary for 'servlet /polarion/internal-login/internal_login.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.163 s, CPU [user: 0.00106 s, system: 0.000484 s], Allocated memory: 28.1 kB, transactions: 0, GC: 0.114 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 16:52:47,072 [ajp-nio-127.0.0.1-8889-exec-3 | cID:5a88ba2f-7f000001-206bd519-6cbdd805] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/gwt/polarion/polarion.css?buildId=20220419-1528-22_R1-be3adceb': Total: 0.299 s, CPU [user: 0.022 s, system: 0.00688 s], Allocated memory: 1.0 MB, transactions: 0, UICustomizationDataProvider: 0.212 s [100% getUserDataValue (1x)] (1x), GC: 0.114 s [100% G1 Young Generation (1x)] (1x), LocalUsersDataFileStorage: 0.0255 s [100% readUserData (1x)] (1x)
2025-07-30 16:52:49,323 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a88c328-7f000001-206bd519-ae78b62f | u:admin] INFO  TXLOGGER - Tx 6616a230e1051_0_6616a230e1051_0_: finished. Total: 0.166 s, CPU [user: 0.067 s, system: 0.0299 s], Allocated memory: 5.4 MB, svn: 0.0533 s [30% getDir2 content (1x), 21% testConnection (1x), 18% log (1x), 14% getLatestRevision (1x)] (8x), RepositoryConfigService: 0.0415 s [100% getReadConfiguration (10x)] (15x), resolve: 0.0377 s [100% Project (4x)] (5x), ObjectMaps: 0.022 s [60% getPrimaryObjectProperty (1x), 17% getPrimaryObjectLocations (1x), 15% getPrimaryObjectLocation (1x)] (6x)
2025-07-30 16:52:49,565 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a88c328-7f000001-206bd519-ae78b62f] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.5 s, CPU [user: 0.223 s, system: 0.0696 s], Allocated memory: 67.3 MB, transactions: 1, RPC: 0.3 s [43% writeResponse (1x), 37% encodeResponse (1x)] (4x), PortalDataService: 0.167 s [100% getInitData (1x)] (1x), svn: 0.0533 s [30% getDir2 content (1x), 21% testConnection (1x), 18% log (1x), 14% getLatestRevision (1x)] (8x), RepositoryConfigService: 0.0415 s [100% getReadConfiguration (10x)] (15x), resolve: 0.0377 s [100% Project (4x)] (5x)
2025-07-30 16:52:49,895 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a88c552-7f000001-206bd519-bc4e7048 | u:admin] INFO  TXLOGGER - Tx 6616a23156c52_0_6616a23156c52_0_: finished. Total: 0.267 s, CPU [user: 0.116 s, system: 0.0302 s], Allocated memory: 15.7 MB, RepositoryConfigService: 0.0643 s [94% getReadConfiguration (13x)] (18x), svn: 0.0435 s [32% getFile content (6x), 30% getDir2 content (1x), 25% info (4x)] (13x)
2025-07-30 16:52:49,908 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a88c552-7f000001-206bd519-bc4e7048] INFO  TXLOGGER - Summary for 'servlet /polarion/gwt/com.polarion.UI/PortalDataService': Total: 0.289 s, CPU [user: 0.132 s, system: 0.0349 s], Allocated memory: 18.9 MB, transactions: 1, PortalDataService: 0.268 s [100% requestPortalSite (1x)] (1x), RepositoryConfigService: 0.0643 s [94% getReadConfiguration (13x)] (18x), svn: 0.0435 s [32% getFile content (6x), 30% getDir2 content (1x), 25% info (4x)] (13x), RPC: 0.0183 s [63% encodeResponse (1x), 30% decodeRequest (1x)] (4x)
2025-07-30 16:52:50,344 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a88c7bd-7f000001-206bd519-065045c2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.107 s, CPU [user: 0.0487 s, system: 0.0128 s], Allocated memory: 3.1 MB, transactions: 0
2025-07-30 16:52:50,954 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a88c9c1-7f000001-206bd519-25e5a074] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.199 s, CPU [user: 0.0896 s, system: 0.0278 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 16:52:50,994 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5a88ca3f-7f000001-206bd519-d5a3a681] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753865570695': Total: 0.112 s, CPU [user: 0.0476 s, system: 0.0091 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 16:52:51,114 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a88ca4b-7f000001-206bd519-8340572a | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865570697] INFO  TXLOGGER - Tx 6616a2329a85c_0_6616a2329a85c_0_: finished. Total: 0.191 s, CPU [user: 0.0777 s, system: 0.0186 s], Allocated memory: 5.4 MB, svn: 0.0193 s [68% testConnection (1x), 32% getFile content (2x)] (4x)
2025-07-30 16:52:51,138 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a88ca4b-7f000001-206bd519-8340572a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865570697': Total: 0.247 s, CPU [user: 0.0981 s, system: 0.0255 s], Allocated memory: 7.3 MB, transactions: 1, RepositoryConfigService: 0.192 s [100% getReadConfiguration (1x)] (1x), svn: 0.0193 s [68% testConnection (1x), 32% getFile content (2x)] (4x)
2025-07-30 16:52:51,214 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a88ca46-7f000001-206bd519-3f70e0bd] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753865570696': Total: 0.328 s, CPU [user: 0.07 s, system: 0.0127 s], Allocated memory: 5.1 MB, transactions: 1, RepositoryConfigService: 0.242 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 16:53:13,132 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a8920b5-7f000001-206bd519-6beff5f0] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753865570698': Total: 0.116 s, CPU [user: 0.0122 s, system: 0.0479 s], Allocated memory: 1.3 MB, transactions: 0, PolarionAuthenticator: 0.0143 s [100% authenticate (1x)] (1x), resolve: 0.0123 s [100% Project (1x)] (1x)
2025-07-30 16:57:04,063 [pool-2-thread-1] INFO  TXLOGGER - Core average load: 1.02001953125
