2025-07-29 19:08:22,678 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:08:22,679 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 19:08:22,679 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:08:22,679 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 19:08:22,679 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 19:08:22,679 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:22,680 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 19:08:27,065 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 19:08:27,218 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.153 s. ]
2025-07-29 19:08:27,218 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 19:08:27,269 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0502 s. ]
2025-07-29 19:08:27,413 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 19:08:27,535 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 98 s. ]
2025-07-29 19:08:27,794 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.12 s. ]
2025-07-29 19:08:27,943 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:27,943 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 19:08:27,970 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.18 s. ]
2025-07-29 19:08:27,970 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:27,970 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (2/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (6/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (5/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-29 19:08:27,975 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-29 19:08:27,982 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 19:08:28,127 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 19:08:28,225 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 19:08:28,662 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.69 s. ]
2025-07-29 19:08:28,673 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:28,673 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 19:08:28,887 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 19:08:28,900 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 19:08:28,925 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:28,926 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 19:08:28,931 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 19:08:28,985 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 19:08:29,035 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 19:08:29,059 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 19:08:29,083 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 19:08:29,119 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 19:08:29,151 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 19:08:29,184 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 19:08:29,215 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 19:08:29,215 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.32 s. ]
2025-07-29 19:08:29,215 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:29,215 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 19:08:29,227 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 19:08:29,228 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:29,228 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 19:08:29,354 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 19:08:29,356 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 19:08:29,485 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-29 19:08:29,486 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:29,486 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 19:08:29,492 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 19:08:29,492 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 19:08:29,492 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-29 19:08:46,135 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 16.64 s. ]
2025-07-29 19:08:46,136 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 19:08:46,136 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 23.5 s. ]
2025-07-29 19:08:46,136 [main] INFO  com.polarion.platform.startup - ****************************************************************
