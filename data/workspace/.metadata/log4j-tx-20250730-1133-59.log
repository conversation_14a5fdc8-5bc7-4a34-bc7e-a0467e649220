2025-07-30 11:34:04,070 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0642 s [73% update (144x), 27% query (12x)] (221x), svn: 0.0229 s [53% getLatestRevision (2x), 35% testConnection (1x)] (4x)
2025-07-30 11:34:04,189 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0348 s [61% getDir2 content (2x), 31% info (3x)] (6x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.11 s, system: 0.192 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0838 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.192 s, system: 0.26 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.155 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.0726 s, system: 0.086 s], Allocated memory: 11.0 MB, transactions: 0, svn: 0.0913 s [84% log2 (10x)] (13x), ObjectMaps: 0.0439 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.0752 s, system: 0.119 s], Allocated memory: 12.1 MB, transactions: 0, svn: 0.084 s [76% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0602 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.0459 s, system: 0.0811 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0514 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0418 s [80% log2 (5x)] (7x)
2025-07-30 11:34:04,982 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.789 s, CPU [user: 0.28 s, system: 0.328 s], Allocated memory: 72.7 MB, transactions: 0, ObjectMaps: 0.139 s [100% getAllPrimaryObjects (1x)] (12x), svn: 0.0935 s [34% log2 (5x), 25% log (1x), 21% info (5x), 9% getLatestRevision (2x)] (18x)
2025-07-30 11:34:04,983 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.534 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.363 s [64% log2 (36x), 15% getLatestRevision (9x), 8% testConnection (6x)] (61x)
2025-07-30 11:34:05,220 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.2 s [100% getReadConfiguration (48x)] (48x), svn: 0.0726 s [85% info (18x)] (38x)
2025-07-30 11:34:05,574 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.277 s [77% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.214 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:34:05,805 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.116 s, CPU [user: 0.0236 s, system: 0.00672 s], Allocated memory: 10.5 MB, GC: 0.015 s [100% G1 Young Generation (1x)] (1x)
2025-07-30 11:34:05,843 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.254 s [100% doFinishStartup (1x)] (1x), Lucene: 0.0648 s [100% refresh (1x)] (1x), commit: 0.0408 s [100% Revision (1x)] (1x), derivedLinkedRevisionsContributor: 0.0181 s [100% objectsToInv (1x)] (1x), DB: 0.0152 s [39% update (3x), 27% query (1x), 23% execute (1x)] (8x)
2025-07-30 11:34:08,618 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.459 s [92% info (158x)] (168x)
2025-07-30 11:34:09,028 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661659407d044_0_661659407d044_0_: finished. Total: 0.4 s, CPU [user: 0.158 s, system: 0.0216 s], Allocated memory: 22.3 MB, resolve: 0.084 s [58% User (2x), 39% Project (1x)] (5x), currentDateQueryExpander: 0.0537 s [100% expand (1x)] (2x), GC: 0.049 s [100% G1 Young Generation (1x)] (1x), Lucene: 0.039 s [100% search (1x)] (1x), svn: 0.0329 s [38% getLatestRevision (3x), 25% testConnection (1x), 16% log (1x), 13% getFile content (2x)] (9x), hasLinkedResourcesQueryExpander: 0.0278 s [100% expand (1x)] (2x), ObjectMaps: 0.0242 s [48% getPrimaryObjectLocation (2x), 40% getPrimaryObjectProperty (2x)] (11x)
2025-07-30 11:34:09,662 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59650410-0a465820-4fcbf387-c8003e40] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.491 s, CPU [user: 0.292 s, system: 0.034 s], Allocated memory: 38.2 MB, transactions: 2, PolarionAuthenticator: 0.425 s [100% authenticate (1x)] (1x)
2025-07-30 11:34:09,713 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.879 s, CPU [user: 0.00666 s, system: 0.00138 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 11:34:09,714 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 30, PolarionAuthenticator: 0.425 s [100% authenticate (1x)] (1x), notification worker: 0.409 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.124 s [70% User (4x), 27% Project (1x)] (8x), Lucene: 0.0569 s [69% search (1x), 18% refresh (1x)] (3x), currentDateQueryExpander: 0.0537 s [100% expand (1x)] (2x), svn: 0.0537 s [44% testConnection (3x), 31% getLatestRevision (4x), 10% getFile content (3x)] (14x), Incremental Baseline: 0.0438 s [100% WorkItem (22x)] (22x), ObjectMaps: 0.0339 s [63% getPrimaryObjectLocation (3x), 28% getPrimaryObjectProperty (2x)] (12x), hasLinkedResourcesQueryExpander: 0.0278 s [100% expand (1x)] (2x), persistence listener: 0.0225 s [80% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 11:34:09,714 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.1 s, CPU [user: 0.184 s, system: 0.0286 s], Allocated memory: 18.7 MB, transactions: 24, svn: 0.758 s [98% getDatedRevision (181x)] (183x), Lucene: 0.059 s [71% buildBaselineSnapshots (1x), 29% buildBaseline (23x)] (24x)
2025-07-30 11:34:10,249 [ajp-nio-127.0.0.1-8889-exec-4 | cID:5965071d-0a465820-4fcbf387-86e5de6d] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.297 s, CPU [user: 0.105 s, system: 0.013 s], Allocated memory: 9.6 MB, transactions: 0
2025-07-30 11:34:10,249 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5965076b-0a465820-4fcbf387-ded31d8a] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.219 s, CPU [user: 0.0198 s, system: 0.00359 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 11:34:10,274 [ajp-nio-127.0.0.1-8889-exec-7 | cID:59650773-0a465820-4fcbf387-1e053a6b] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753846449866': Total: 0.227 s, CPU [user: 0.0393 s, system: 0.00455 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 11:34:10,334 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59650770-0a465820-4fcbf387-96a945e3 | u:admin | GET:/polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846449867] INFO  TXLOGGER - Tx 6616594209448_0_6616594209448_0_: finished. Total: 0.121 s, CPU [user: 0.0537 s, system: 0.007 s], Allocated memory: 5.1 MB, svn: 0.0234 s [58% testConnection (1x), 30% info (2x)] (6x)
2025-07-30 11:34:10,363 [ajp-nio-127.0.0.1-8889-exec-6 | cID:59650770-0a465820-4fcbf387-96a945e3] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846449867': Total: 0.316 s, CPU [user: 0.0779 s, system: 0.0104 s], Allocated memory: 7.2 MB, transactions: 1, RepositoryConfigService: 0.122 s [100% getReadConfiguration (1x)] (1x), svn: 0.0234 s [58% testConnection (1x), 30% info (2x)] (6x)
2025-07-30 11:34:10,367 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5965077a-0a465820-4fcbf387-42654b9e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846449868': Total: 0.319 s, CPU [user: 0.0336 s, system: 0.0044 s], Allocated memory: 3.4 MB, transactions: 1, RepositoryConfigService: 0.149 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 11:34:10,637 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661659407d042_0_661659407d042_0_: finished. Total: 2.01 s, CPU [user: 0.422 s, system: 0.109 s], Allocated memory: 47.1 MB, svn: 1.37 s [64% getDatedRevision (181x), 26% getDir2 content (25x)] (307x), resolve: 0.42 s [100% Category (96x)] (96x), ObjectMaps: 0.146 s [41% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 26% getLastPromoted (96x)] (387x)
2025-07-30 11:34:10,848 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661659428944c_0_661659428944c_0_: finished. Total: 0.122 s, CPU [user: 0.0527 s, system: 0.0117 s], Allocated memory: 8.3 MB, RepositoryConfigService: 0.0607 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (162x)] (172x), svn: 0.0539 s [54% info (19x), 39% getFile content (15x)] (36x), resolve: 0.031 s [100% User (9x)] (9x), ObjectMaps: 0.0148 s [55% getPrimaryObjectProperty (8x), 23% getPrimaryObjectLocation (8x), 22% getLastPromoted (8x)] (32x)
2025-07-30 11:34:10,975 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165942a804d_0_66165942a804d_0_: finished. Total: 0.127 s, CPU [user: 0.0377 s, system: 0.00621 s], Allocated memory: 5.2 MB, svn: 0.093 s [73% info (29x), 26% getFile content (12x)] (42x), RepositoryConfigService: 0.0615 s [64% getReadConfiguration (54x), 36% getExistingPrefixes (9x)] (77x)
2025-07-30 11:34:11,144 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165942c7c4e_0_66165942c7c4e_0_: finished. Total: 0.169 s, CPU [user: 0.0404 s, system: 0.008 s], Allocated memory: 20.8 MB, svn: 0.137 s [53% getDir2 content (13x), 32% info (35x)] (93x), RepositoryConfigService: 0.0522 s [72% getReadConfiguration (170x), 28% getExistingPrefixes (11x)] (192x)
2025-07-30 11:34:12,295 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165942f204f_0_66165942f204f_0_: finished. Total: 1.15 s, CPU [user: 0.429 s, system: 0.0565 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.965 s [61% getReadConfiguration (8682x), 39% getExistingPrefixes (129x)] (9021x), svn: 0.708 s [51% info (226x), 34% getFile content (412x)] (656x)
2025-07-30 11:34:12,399 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616594411c50_0_6616594411c50_0_: finished. Total: 0.103 s, CPU [user: 0.0212 s, system: 0.00214 s], Allocated memory: 19.0 MB, svn: 0.094 s [51% getDir2 content (14x), 38% info (37x)] (81x), RepositoryConfigService: 0.0269 s [60% getReadConfiguration (124x), 40% getExistingPrefixes (12x)] (148x)
2025-07-30 11:34:12,811 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616594435852_0_6616594435852_0_: finished. Total: 0.373 s, CPU [user: 0.14 s, system: 0.00925 s], Allocated memory: 393.3 MB, RepositoryConfigService: 0.272 s [54% getReadConfiguration (2787x), 46% getExistingPrefixes (89x)] (3025x), svn: 0.267 s [48% info (152x), 27% getFile content (185x), 25% getDir2 content (17x)] (355x)
2025-07-30 11:34:12,812 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.19 s, CPU [user: 1.19 s, system: 0.214 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.8 s [31% getDatedRevision (181x), 25% info (539x), 24% getDir2 content (96x)] (1637x), RepositoryConfigService: 1.47 s [60% getReadConfiguration (12019x), 38% getExistingPrefixes (259x)] (12691x), resolve: 0.496 s [85% Category (96x)] (117x)
2025-07-30 11:34:12,812 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 59, svn: 3.58 s [45% getDatedRevision (362x), 20% info (541x), 19% getDir2 content (96x)] (1827x), RepositoryConfigService: 1.74 s [66% getReadConfiguration (12021x), 32% getExistingPrefixes (259x)] (12693x), resolve: 0.501 s [84% Category (96x)] (121x)
2025-07-30 11:36:29,871 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5966ca84-0a465820-4fcbf387-495ab30e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846565456': Total: 24.4 s, CPU [user: 0.00577 s, system: 0.00432 s], Allocated memory: 588.4 kB, transactions: 0
2025-07-30 11:37:27,608 [ajp-nio-127.0.0.1-8889-exec-2 | cID:59680089-0a465820-4fcbf387-bb9bd35e] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846565457': Total: 2.72 s, CPU [user: 0.00962 s, system: 0.0407 s], Allocated memory: 561.3 kB, transactions: 0
