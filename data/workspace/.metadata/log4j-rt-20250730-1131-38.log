2025-07-30 11:31:46,375 [Catalina-utility-3] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 11:31:47,187 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 11:31:47,190 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:31:47,190 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:31:47,192 [Catalina-utility-3] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[93]')
2025-07-30 11:31:47,196 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 11:31:47,197 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[87]')
2025-07-30 11:31:47,197 [Catalina-utility-3] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[89]')
2025-07-30 11:31:50,092 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 11:31:50,203 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5962e526-0a465820-1af03f1a-034b9738] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 11:31:50,220 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 11:31:50,221 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
