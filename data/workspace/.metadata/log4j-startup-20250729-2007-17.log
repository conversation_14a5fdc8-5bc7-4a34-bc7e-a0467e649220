2025-07-29 20:07:17,676 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 20:07:17,676 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-29 20:07:17,676 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-29 20:07:17,676 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-29 20:07:17,677 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-29 20:07:17,677 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:17,677 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-29 20:07:22,115 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-29 20:07:22,265 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.15 s. ]
2025-07-29 20:07:22,265 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-29 20:07:22,329 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0642 s. ]
2025-07-29 20:07:22,391 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-29 20:07:22,524 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 81 s. ]
2025-07-29 20:07:22,748 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.08 s. ]
2025-07-29 20:07:22,831 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:22,831 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-29 20:07:22,853 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-29 20:07:22,854 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:22,854 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (2/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (5/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (4/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (3/9)
2025-07-29 20:07:22,859 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (6/9)
2025-07-29 20:07:22,865 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-29 20:07:23,015 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-29 20:07:23,134 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-29 20:07:23,567 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.71 s. ]
2025-07-29 20:07:23,578 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:23,578 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-29 20:07:23,796 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-29 20:07:23,807 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.24 s. ]
2025-07-29 20:07:23,830 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:23,830 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-29 20:07:23,833 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-29 20:07:23,881 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-29 20:07:23,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-29 20:07:23,968 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-29 20:07:23,990 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-29 20:07:24,031 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-29 20:07:24,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-29 20:07:24,078 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-29 20:07:24,105 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-29 20:07:24,105 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.3 s. ]
2025-07-29 20:07:24,105 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:24,105 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-29 20:07:24,119 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-29 20:07:24,119 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:24,119 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-29 20:07:24,221 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-29 20:07:24,224 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-29 20:07:24,342 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.22 s. ]
2025-07-29 20:07:24,342 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:24,342 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-29 20:07:24,349 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-29 20:07:24,349 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-29 20:07:24,349 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
