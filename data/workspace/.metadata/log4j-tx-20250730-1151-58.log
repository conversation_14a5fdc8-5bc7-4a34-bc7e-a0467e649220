2025-07-30 11:52:03,315 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0441 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0136 s [50% testConnection (1x), 34% getLatestRevision (2x)] (4x)
2025-07-30 11:52:03,425 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0286 s [59% getDir2 content (2x), 29% info (3x)] (6x)
2025-07-30 11:52:04,143 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.103 s, system: 0.219 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0732 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:52:04,144 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.716 s, CPU [user: 0.182 s, system: 0.289 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.118 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:52:04,144 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.242 s, system: 0.35 s], Allocated memory: 72.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (10x), svn: 0.068 s [29% info (5x), 25% log (1x), 23% log2 (5x), 10% getLatestRevision (2x)] (18x)
2025-07-30 11:52:04,144 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.0437 s, system: 0.101 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0713 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:52:04,144 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.716 s, CPU [user: 0.0762 s, system: 0.153 s], Allocated memory: 12.1 MB, transactions: 0, ObjectMaps: 0.0787 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0664 s [79% log2 (10x), 13% getLatestRevision (2x)] (13x)
2025-07-30 11:52:04,144 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.715 s, CPU [user: 0.0717 s, system: 0.122 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0881 s [77% log2 (10x), 17% getLatestRevision (2x)] (13x), ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 11:52:04,145 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.526 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.296 s [63% log2 (36x), 14% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 11:52:04,380 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.202 s [100% getReadConfiguration (48x)] (48x), svn: 0.0768 s [82% info (18x)] (38x)
2025-07-30 11:52:04,719 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.269 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.199 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:52:04,928 [PolarionDocIdCreator-6] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.104 s, CPU [user: 0.0267 s, system: 0.00681 s], Allocated memory: 10.5 MB
2025-07-30 11:52:04,965 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.23 s [100% doFinishStartup (1x)] (1x), commit: 0.0554 s [100% Revision (1x)] (1x), Lucene: 0.0437 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0138 s [100% objectsToInv (1x)] (1x), DB: 0.0118 s [39% update (3x), 23% query (1x), 20% execute (1x)] (8x)
2025-07-30 11:52:07,557 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.431 s [92% info (158x)] (168x)
2025-07-30 11:52:07,845 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 66165d5e24445_0_66165d5e24445_0_: finished. Total: 0.276 s, CPU [user: 0.12 s, system: 0.015 s], Allocated memory: 20.9 MB, resolve: 0.065 s [61% User (2x), 37% Project (1x)] (5x), Lucene: 0.0317 s [100% search (1x)] (1x), GC: 0.03 s [100% G1 Young Generation (1x)] (1x), svn: 0.0251 s [31% getLatestRevision (2x), 25% log (1x), 25% testConnection (1x)] (8x), ObjectMaps: 0.0215 s [50% getPrimaryObjectLocation (2x), 43% getPrimaryObjectProperty (2x)] (11x), hasLinkedResourcesQueryExpander: 0.0205 s [100% expand (1x)] (2x)
2025-07-30 11:52:08,391 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.697 s, CPU [user: 0.00581 s, system: 0.00153 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 11:52:08,391 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 29, notification worker: 0.287 s [98% RevisionActivityCreator (2x)] (6x), resolve: 0.0875 s [69% User (3x), 27% Project (1x)] (7x), Lucene: 0.0448 s [71% search (1x), 29% refresh (1x)] (2x), ObjectMaps: 0.0342 s [68% getPrimaryObjectLocation (3x), 27% getPrimaryObjectProperty (2x)] (12x), svn: 0.0306 s [35% getLatestRevision (3x), 29% testConnection (2x), 21% log (1x)] (10x), hasLinkedResourcesQueryExpander: 0.0205 s [100% expand (1x)] (2x), Incremental Baseline: 0.0189 s [100% WorkItem (22x)] (22x), persistence listener: 0.0175 s [85% indexRefreshPersistenceListener (1x)] (7x)
2025-07-30 11:52:08,392 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.834 s, CPU [user: 0.139 s, system: 0.0214 s], Allocated memory: 18.2 MB, transactions: 23, svn: 0.623 s [99% getDatedRevision (181x)] (183x)
2025-07-30 11:52:08,693 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d5e22840_0_66165d5e22840_0_: finished. Total: 1.13 s, CPU [user: 0.276 s, system: 0.0702 s], Allocated memory: 46.6 MB, svn: 0.671 s [42% getDatedRevision (181x), 41% getDir2 content (25x)] (307x), resolve: 0.36 s [100% Category (96x)] (96x), ObjectMaps: 0.151 s [40% getPrimaryObjectProperty (96x), 37% getLastPromoted (96x), 22% getPrimaryObjectLocation (96x)] (387x)
2025-07-30 11:52:09,191 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d5f6d04a_0_66165d5f6d04a_0_: finished. Total: 0.307 s, CPU [user: 0.0888 s, system: 0.00901 s], Allocated memory: 19.9 MB, svn: 0.15 s [50% getFile content (44x), 48% getDir2 content (17x)] (62x), RepositoryConfigService: 0.132 s [97% getReadConfiguration (170x)] (192x)
2025-07-30 11:52:09,989 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d5fb9c4b_0_66165d5fb9c4b_0_: finished. Total: 0.797 s, CPU [user: 0.337 s, system: 0.0219 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.572 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.45 s [60% getFile content (412x), 40% getDir2 content (21x)] (434x)
2025-07-30 11:52:10,341 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 66165d609f44e_0_66165d609f44e_0_: finished. Total: 0.232 s, CPU [user: 0.0977 s, system: 0.00564 s], Allocated memory: 384.3 MB, RepositoryConfigService: 0.15 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.146 s [53% getFile content (185x), 47% getDir2 content (21x)] (207x)
2025-07-30 11:52:10,341 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.78 s, CPU [user: 0.916 s, system: 0.123 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.6 s [43% getDir2 content (115x), 36% getFile content (807x), 18% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.953 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.422 s [85% Category (96x)] (117x), ObjectMaps: 0.178 s [42% getPrimaryObjectProperty (108x), 35% getLastPromoted (108x), 23% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 11:52:10,341 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 56, svn: 2.22 s [40% getDatedRevision (362x), 31% getDir2 content (115x), 26% getFile content (807x)] (1324x), RepositoryConfigService: 0.953 s [95% getReadConfiguration (12019x)] (12691x), resolve: 0.422 s [85% Category (96x)] (118x), ObjectMaps: 0.178 s [42% getPrimaryObjectProperty (108x), 35% getLastPromoted (108x), 23% getPrimaryObjectLocation (114x)] (442x)
2025-07-30 11:52:43,533 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5976047b-7f000001-19044e11-04fc7fa7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.141 s, CPU [user: 0.0405 s, system: 0.0489 s], Allocated memory: 18.5 MB, transactions: 0, PolarionAuthenticator: 0.0885 s [100% authenticate (1x)] (1x)
