2025-07-30 18:17:08,612 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0694 s [55% update (144x), 45% query (12x)] (221x), svn: 0.00954 s [49% getLatestRevision (2x), 36% testConnection (1x)] (4x)
2025-07-30 18:17:08,794 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0366 s [63% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-30 18:17:09,676 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.874 s, CPU [user: 0.254 s, system: 0.277 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.144 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:17:09,676 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.875 s, CPU [user: 0.0943 s, system: 0.115 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.1 s [85% log2 (10x)] (13x), ObjectMaps: 0.0687 s [99% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:17:09,676 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.875 s, CPU [user: 0.0641 s, system: 0.0796 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0713 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:17:09,677 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.875 s, CPU [user: 0.139 s, system: 0.136 s], Allocated memory: 14.6 MB, transactions: 0, svn: 0.139 s [35% log2 (10x), 26% info (5x), 16% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0984 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 18:17:09,676 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.874 s, CPU [user: 0.143 s, system: 0.201 s], Allocated memory: 24.0 MB, transactions: 0, ObjectMaps: 0.0873 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 18:17:09,676 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.874 s, CPU [user: 0.301 s, system: 0.341 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.144 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 18:17:09,679 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.613 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.356 s [59% log2 (36x), 13% getLatestRevision (9x), 10% info (5x)] (61x)
2025-07-30 18:17:09,816 [main | u:p] INFO  TXLOGGER - Tx 6616b57ed3801_0_6616b57ed3801_0_: finished. Total: 0.104 s, CPU [user: 0.081 s, system: 0.00632 s], Allocated memory: 21.8 MB
2025-07-30 18:17:09,943 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.221 s [100% getReadConfiguration (48x)] (48x), svn: 0.077 s [87% info (18x)] (38x)
2025-07-30 18:17:10,235 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.227 s [76% info (94x), 16% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.174 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 18:17:10,456 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.206 s [100% doFinishStartup (1x)] (1x), commit: 0.0407 s [100% Revision (1x)] (1x), Lucene: 0.0368 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0136 s [100% objectsToInv (1x)] (1x)
2025-07-30 18:17:50,973 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.44 s [70% info (158x), 22% getLatestRevision (14x)] (182x), PullingJob: 0.0975 s [100% collectChanges (13x)] (13x)
2025-07-30 18:17:51,069 [Persistence Events Processing Thread | u:p] INFO  TXLOGGER - Summary: Total: 0.114 s, CPU [user: 0.019 s, system: 0.0056 s], Allocated memory: 2.3 MB, transactions: 0, persistence listener: 0.112 s [95% indexRefreshPersistenceListener (1x)] (7x), processed revs: [207 (delay 14288s)]
2025-07-30 18:17:51,337 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616b5a736c43_0_6616b5a736c43_0_: finished. Total: 0.27 s, CPU [user: 0.143 s, system: 0.0215 s], Allocated memory: 20.3 MB, resolve: 0.0933 s [59% User (2x), 38% Project (1x)] (5x), ObjectMaps: 0.0339 s [51% getPrimaryObjectLocation (2x), 39% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.024 s [100% search (1x)] (1x), svn: 0.0207 s [33% getLatestRevision (2x), 27% log (1x), 23% testConnection (1x)] (8x), GlobalHandler: 0.0136 s [92% applyTxChanges (1x)] (4x)
2025-07-30 18:17:51,849 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.719 s, CPU [user: 0.00566 s, system: 0.00194 s], Allocated memory: 316.0 kB, transactions: 1
2025-07-30 18:17:51,849 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.278 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.116 s [65% User (3x), 31% Project (1x)] (7x), persistence listener: 0.112 s [95% indexRefreshPersistenceListener (1x)] (7x), ObjectMaps: 0.0426 s [61% getPrimaryObjectLocation (3x), 31% getPrimaryObjectProperty (2x)] (12x), Lucene: 0.0285 s [84% search (1x)] (2x), svn: 0.0207 s [33% getLatestRevision (2x), 27% log (1x), 23% testConnection (1x)] (8x), Incremental Baseline: 0.0198 s [100% WorkItem (22x)] (22x), GlobalHandler: 0.014 s [91% applyTxChanges (2x)] (7x)
2025-07-30 18:17:51,850 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.877 s, CPU [user: 0.145 s, system: 0.0255 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.638 s [99% getDatedRevision (181x)] (183x)
2025-07-30 18:17:52,202 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a726440_0_6616b5a726440_0_: finished. Total: 1.2 s, CPU [user: 0.308 s, system: 0.0873 s], Allocated memory: 46.6 MB, svn: 0.67 s [49% getDatedRevision (181x), 33% getDir2 content (25x)] (307x), resolve: 0.38 s [100% Category (96x)] (96x), ObjectMaps: 0.142 s [44% getPrimaryObjectProperty (96x), 34% getPrimaryObjectLocation (96x), 22% getLastPromoted (96x)] (387x)
2025-07-30 18:17:52,389 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a864848_0_6616b5a864848_0_: finished. Total: 0.114 s, CPU [user: 0.0559 s, system: 0.00984 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0514 s [53% getReadUserConfiguration (10x), 47% getReadConfiguration (162x)] (172x), svn: 0.0465 s [57% info (19x), 36% getFile content (15x)] (36x), resolve: 0.0325 s [100% User (9x)] (9x), ObjectMaps: 0.0137 s [54% getPrimaryObjectProperty (8x), 27% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 18:17:52,564 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a88b04a_0_6616b5a88b04a_0_: finished. Total: 0.136 s, CPU [user: 0.0451 s, system: 0.00611 s], Allocated memory: 19.9 MB, svn: 0.101 s [72% getDir2 content (17x), 28% getFile content (44x)] (62x), RepositoryConfigService: 0.0486 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 18:17:53,340 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a8ad04b_0_6616b5a8ad04b_0_: finished. Total: 0.776 s, CPU [user: 0.351 s, system: 0.0307 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.625 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.389 s [74% getFile content (412x), 26% getDir2 content (21x)] (434x), GC: 0.045 s [100% G1 Young Generation (3x)] (3x)
2025-07-30 18:17:53,462 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a96f44c_0_6616b5a96f44c_0_: finished. Total: 0.121 s, CPU [user: 0.0213 s, system: 0.00257 s], Allocated memory: 18.0 MB, svn: 0.112 s [90% getDir2 content (18x)] (48x), RepositoryConfigService: 0.0174 s [97% getReadConfiguration (124x)] (148x)
2025-07-30 18:17:53,749 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616b5a99784e_0_6616b5a99784e_0_: finished. Total: 0.247 s, CPU [user: 0.104 s, system: 0.00567 s], Allocated memory: 387.4 MB, RepositoryConfigService: 0.158 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.156 s [52% getFile content (185x), 48% getDir2 content (21x)] (207x)
2025-07-30 18:17:53,750 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.75 s, CPU [user: 0.949 s, system: 0.152 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.55 s [39% getDir2 content (115x), 36% getFile content (807x), 21% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.95 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.441 s [86% Category (96x)] (117x), ObjectMaps: 0.165 s [46% getPrimaryObjectProperty (108x), 33% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 18:17:53,750 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.2 s [44% getDatedRevision (362x), 27% getDir2 content (115x), 26% getFile content (807x)] (1325x), RepositoryConfigService: 0.95 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.441 s [86% Category (96x)] (118x), ObjectMaps: 0.165 s [46% getPrimaryObjectProperty (108x), 33% getPrimaryObjectLocation (114x), 21% getLastPromoted (108x)] (442x)
2025-07-30 18:21:18,179 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5ad6beb5-7f000001-643b9a92-1b916005] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 198 s, CPU [user: 0.368 s, system: 0.146 s], Allocated memory: 56.8 MB, transactions: 2, PolarionAuthenticator: 194 s [100% authenticate (1x)] (1x), interceptor: 194 s [100% IAuthenticatorManager.getAuthenticators (2x)] (2x)
