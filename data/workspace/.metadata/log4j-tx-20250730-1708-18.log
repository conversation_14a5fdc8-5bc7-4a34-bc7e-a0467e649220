2025-07-30 17:08:23,115 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0516 s [63% update (144x), 37% query (12x)] (221x), svn: 0.0207 s [63% getLatestRevision (2x), 25% testConnection (1x)] (4x)
2025-07-30 17:08:23,257 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0392 s [62% getDir2 content (2x), 30% info (3x)] (6x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0927 s, system: 0.115 s], Allocated memory: 13.1 MB, transactions: 0, svn: 0.0886 s [76% log2 (10x), 15% getLatestRevision (2x)] (13x), ObjectMaps: 0.0691 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.266 s, system: 0.307 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.13 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.764 s, CPU [user: 0.0872 s, system: 0.086 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.123 s [85% log2 (10x)] (13x), ObjectMaps: 0.046 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.765 s, CPU [user: 0.0774 s, system: 0.0636 s], Allocated memory: 8.5 MB, transactions: 0, svn: 0.107 s [39% log2 (5x), 21% log (1x), 18% getLatestRevision (2x), 15% info (5x)] (18x), ObjectMaps: 0.0615 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.765 s, CPU [user: 0.127 s, system: 0.17 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0751 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:08:24,027 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.765 s, CPU [user: 0.219 s, system: 0.245 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.115 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:08:24,028 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.497 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.394 s [67% log2 (36x), 13% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 17:08:24,284 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.223 s [100% getReadConfiguration (48x)] (48x), svn: 0.0788 s [84% info (18x)] (38x)
2025-07-30 17:08:24,580 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.225 s [73% info (94x), 20% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.165 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:08:24,794 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.101 s, CPU [user: 0.026 s, system: 0.00634 s], Allocated memory: 10.7 MB
2025-07-30 17:08:24,841 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.243 s [100% doFinishStartup (1x)] (1x), commit: 0.0591 s [100% Revision (1x)] (1x), Lucene: 0.04 s [100% refresh (1x)] (1x), DB: 0.0137 s [38% query (1x), 32% update (3x), 21% execute (1x)] (8x), derivedLinkedRevisionsContributor: 0.0136 s [100% objectsToInv (1x)] (1x)
2025-07-30 17:08:27,920 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.472 s [89% info (158x)] (170x)
2025-07-30 17:08:28,334 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616a5c5a6c45_0_6616a5c5a6c45_0_: finished. Total: 0.403 s, CPU [user: 0.159 s, system: 0.0202 s], Allocated memory: 20.3 MB, resolve: 0.144 s [53% Project (1x), 45% User (2x)] (5x), svn: 0.0361 s [42% getLatestRevision (2x), 16% info (1x), 16% testConnection (1x), 15% log (1x)] (8x), ObjectMaps: 0.0297 s [52% getPrimaryObjectLocation (2x), 36% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0238 s [100% search (1x)] (1x)
2025-07-30 17:08:29,177 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.13 s, CPU [user: 0.0073 s, system: 0.0019 s], Allocated memory: 315.7 kB, transactions: 1
2025-07-30 17:08:29,177 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.415 s [99% RevisionActivityCreator (2x)] (6x), resolve: 0.185 s [56% User (3x), 41% Project (1x)] (7x), Lucene: 0.0714 s [55% add (1x), 33% search (1x)] (3x), ObjectMaps: 0.0386 s [63% getPrimaryObjectLocation (3x), 28% getPrimaryObjectProperty (2x)] (12x), svn: 0.0361 s [42% getLatestRevision (2x), 16% info (1x), 16% testConnection (1x), 15% log (1x)] (8x), Incremental Baseline: 0.0292 s [100% WorkItem (22x)] (22x)
2025-07-30 17:08:29,178 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.26 s, CPU [user: 0.196 s, system: 0.0321 s], Allocated memory: 18.4 MB, transactions: 24, svn: 1.03 s [99% getDatedRevision (181x)] (183x)
2025-07-30 17:08:29,739 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5c5a6040_0_6616a5c5a6040_0_: finished. Total: 1.81 s, CPU [user: 0.416 s, system: 0.105 s], Allocated memory: 47.1 MB, svn: 1.05 s [50% getDatedRevision (181x), 35% getDir2 content (25x)] (307x), resolve: 0.509 s [100% Category (96x)] (96x), ObjectMaps: 0.203 s [38% getPrimaryObjectProperty (96x), 33% getPrimaryObjectLocation (96x), 29% getLastPromoted (96x)] (387x)
2025-07-30 17:08:29,959 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5c783448_0_6616a5c783448_0_: finished. Total: 0.122 s, CPU [user: 0.0608 s, system: 0.00996 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0599 s [56% getReadConfiguration (162x), 44% getReadUserConfiguration (10x)] (172x), svn: 0.0522 s [58% info (19x), 36% getFile content (15x)] (36x), resolve: 0.0337 s [100% User (9x)] (9x), ObjectMaps: 0.0169 s [54% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x), 23% getLastPromoted (8x)] (32x)
2025-07-30 17:08:30,145 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5c7ac84a_0_6616a5c7ac84a_0_: finished. Total: 0.142 s, CPU [user: 0.0486 s, system: 0.00457 s], Allocated memory: 19.9 MB, svn: 0.114 s [83% getDir2 content (17x)] (62x), RepositoryConfigService: 0.0339 s [98% getReadConfiguration (170x)] (192x)
2025-07-30 17:08:30,899 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5c7d044b_0_6616a5c7d044b_0_: finished. Total: 0.754 s, CPU [user: 0.352 s, system: 0.0179 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.564 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.41 s [66% getFile content (412x), 34% getDir2 content (21x)] (434x)
2025-07-30 17:08:31,637 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616a5c8aa44e_0_6616a5c8aa44e_0_: finished. Total: 0.619 s, CPU [user: 0.177 s, system: 0.0221 s], Allocated memory: 391.3 MB, RepositoryConfigService: 0.49 s [90% getReadConfiguration (2787x)] (3025x), svn: 0.293 s [72% getFile content (185x), 28% getDir2 content (21x)] (207x)
2025-07-30 17:08:31,641 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 3.71 s, CPU [user: 1.14 s, system: 0.174 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.09 s [38% getDir2 content (115x), 34% getFile content (807x), 25% getDatedRevision (181x)] (1141x), RepositoryConfigService: 1.21 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.587 s [87% Category (96x)] (117x), ObjectMaps: 0.235 s [40% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 27% getLastPromoted (108x)] (442x)
2025-07-30 17:08:31,642 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.12 s [50% getDatedRevision (362x), 25% getDir2 content (115x), 22% getFile content (807x)] (1325x), RepositoryConfigService: 1.21 s [92% getReadConfiguration (12019x)] (12691x), resolve: 0.588 s [87% Category (96x)] (118x), ObjectMaps: 0.235 s [40% getPrimaryObjectProperty (108x), 32% getPrimaryObjectLocation (114x), 27% getLastPromoted (108x)] (442x)
2025-07-30 17:08:34,041 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5a972c62-7f000001-2007a050-78ca2109] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.534 s, CPU [user: 0.258 s, system: 0.0654 s], Allocated memory: 43.0 MB, transactions: 2, PolarionAuthenticator: 0.488 s [100% authenticate (1x)] (1x)
2025-07-30 17:08:34,554 [ajp-nio-127.0.0.1-8889-exec-8 | cID:5a972fbb-7f000001-2007a050-250be4b7] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.191 s, CPU [user: 0.0829 s, system: 0.0217 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 17:08:34,554 [ajp-nio-127.0.0.1-8889-exec-10 | cID:5a972fed-7f000001-2007a050-3d3e4616] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/canModifyConfiguration': Total: 0.141 s, CPU [user: 0.0184 s, system: 0.00438 s], Allocated memory: 1.4 MB, transactions: 0
2025-07-30 17:08:34,573 [ajp-nio-127.0.0.1-8889-exec-9 | cID:5a972ff5-7f000001-2007a050-27089385] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753866514299': Total: 0.152 s, CPU [user: 0.0336 s, system: 0.00557 s], Allocated memory: 2.0 MB, transactions: 0
2025-07-30 17:08:34,629 [ajp-nio-127.0.0.1-8889-exec-7 | cID:5a972ff5-7f000001-2007a050-122a8bbc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753866514300': Total: 0.207 s, CPU [user: 0.0703 s, system: 0.0122 s], Allocated memory: 8.9 MB, transactions: 1, RepositoryConfigService: 0.0713 s [100% getReadConfiguration (1x)] (1x), svn: 0.014 s [76% testConnection (1x), 24% getFile content (2x)] (4x)
2025-07-30 17:08:34,632 [ajp-nio-127.0.0.1-8889-exec-5 | cID:5a972ffb-7f000001-2007a050-001fef9f] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753866514301': Total: 0.205 s, CPU [user: 0.0283 s, system: 0.00526 s], Allocated memory: 4.5 MB, transactions: 1, RepositoryConfigService: 0.0888 s [100% getReadConfiguration (1x)] (1x)
2025-07-30 17:09:19,041 [PullingJob-default] INFO  TXLOGGER - Summary for 'PullingJob': Total: 0.132 s, CPU [user: 0.0023 s, system: 0.00193 s], Allocated memory: 130.8 kB, transactions: 0, PullingJob: 0.131 s [100% collectChanges (1x)] (1x), svn: 0.131 s [100% getLatestRevision (1x)] (1x), GC: 0.118 s [100% G1 Young Generation (1x)] (1x)
