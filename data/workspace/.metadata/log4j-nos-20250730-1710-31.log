2025-07-30 17:10:44,779 [Thread-41] INFO  NotificationService - 
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService -   .   ____          _            __ _ _
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService -  /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService - ( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService -  \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService -   '  |____| .__|_| |_|_| |_\__, | / / / /
2025-07-30 17:10:44,780 [Thread-41] INFO  NotificationService -  =========|_|==============|___/=/_/_/_/
2025-07-30 17:10:44,781 [Thread-41] INFO  NotificationService -  :: Spring Boot ::                (v2.6.6)
2025-07-30 17:10:44,781 [Thread-41] INFO  NotificationService - 
2025-07-30 17:10:44,948 [Thread-41] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Starting Application using Java 11.0.27 on zhangwentiandeMac-mini-2.local with PID 78457 (/opt/polarion/polarion/plugins/com.polarion.platform_3.22.1/services/notification-service/NotificationService.jar started by zhangwentian in /private/var/folders/z_/shw6wc7d7ps_fjvv781t4gt80000gn/T/polarionSpringServerSubprocess9661612339573554744.tmp)
2025-07-30 17:10:44,948 [Thread-41] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - No active profile set, falling back to 1 default profile: "default"
2025-07-30 17:10:45,684 [Thread-41] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 40608 (http)
2025-07-30 17:10:45,695 [Thread-41] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-40608"]
2025-07-30 17:10:45,695 [Thread-41] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-07-30 17:10:45,695 [Thread-41] INFO  NotificationService - [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-07-30 17:10:45,751 [Thread-41] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-07-30 17:10:45,751 [Thread-41] INFO  NotificationService - [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 735 ms
2025-07-30 17:10:45,770 [Thread-41] INFO  NotificationService - [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing AtmosphereFramework
2025-07-30 17:10:45,961 [Thread-41] INFO  NotificationService - [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-40608"]
2025-07-30 17:10:45,996 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using org.atmosphere.cpr.DefaultAnnotationProcessor for processing annotation
2025-07-30 17:10:45,997 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.DefaultAnnotationProcessor - AnnotationProcessor class org.atmosphere.cpr.DefaultAnnotationProcessor$BytecodeBasedAnnotationProcessor being used
2025-07-30 17:10:46,010 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AnnotationHandler - Found Annotation in class com.siemens.polarion.service.notification.NotificationService being scanned: interface org.atmosphere.config.service.ManagedService
2025-07-30 17:10:46,014 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.AtmosphereResourceLifecycleInterceptor
2025-07-30 17:10:46,014 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.client.TrackMessageSizeInterceptor
2025-07-30 17:10:46,015 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.interceptor.SuspendTrackerInterceptor
2025-07-30 17:10:46,015 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class org.atmosphere.config.managed.ManagedServiceInterceptor
2025-07-30 17:10:46,020 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.annotation.AnnotationUtil - Adding class com.siemens.polarion.service.notification.JwtVerificationInterceptor
2025-07-30 17:10:46,025 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.util.ForkJoinPool - Using ForkJoinPool  java.util.concurrent.ForkJoinPool. Set the org.atmosphere.cpr.broadcaster.maxAsyncWriteThreads to -1 to fully use its power.
2025-07-30 17:10:46,027 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler mapped to context-path /notification and Broadcaster Class org.atmosphere.cpr.DefaultBroadcaster
2025-07-30 17:10:46,027 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor [Atmosphere LifeCycle,  Track Message Size Interceptor using |, UUID Tracking Interceptor, @ManagedService Interceptor, @Service Event Listeners, com.siemens.polarion.service.notification.JwtVerificationInterceptor] mapped to AtmosphereHandler org.atmosphere.config.managed.ManagedAtmosphereHandler
2025-07-30 17:10:46,039 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Auto detecting WebSocketHandler in /WEB-INF/classes/
2025-07-30 17:10:46,039 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed WebSocketProtocol org.atmosphere.websocket.protocol.SimpleHttpProtocol 
2025-07-30 17:10:46,043 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.container.JSR356AsyncSupport - JSR 356 Mapping path /notification
2025-07-30 17:10:46,048 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installing Default AtmosphereInterceptors
2025-07-30 17:10:46,049 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CorsInterceptor : CORS Interceptor Support
2025-07-30 17:10:46,049 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.CacheHeadersInterceptor : Default Response's Headers Interceptor
2025-07-30 17:10:46,050 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.PaddingAtmosphereInterceptor : Browser Padding Interceptor Support
2025-07-30 17:10:46,050 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.AndroidAtmosphereInterceptor : Android Interceptor Support
2025-07-30 17:10:46,051 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.HeartbeatInterceptor : Heartbeat Interceptor Support
2025-07-30 17:10:46,051 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.SSEAtmosphereInterceptor : SSE Interceptor Support
2025-07-30 17:10:46,052 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JSONPAtmosphereInterceptor : JSONP Interceptor Support
2025-07-30 17:10:46,052 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.JavaScriptProtocol : Atmosphere JavaScript Protocol
2025-07-30 17:10:46,053 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor : org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor
2025-07-30 17:10:46,053 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.OnDisconnectInterceptor : Browser disconnection detection
2025-07-30 17:10:46,053 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 	org.atmosphere.interceptor.IdleResourceInterceptor : org.atmosphere.interceptor.IdleResourceInterceptor
2025-07-30 17:10:46,053 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Set org.atmosphere.cpr.AtmosphereInterceptor.disableDefaults to disable them.
2025-07-30 17:10:46,053 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor CORS Interceptor Support with priority FIRST_BEFORE_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Default Response's Headers Interceptor with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser Padding Interceptor Support with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Android Interceptor Support with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.interceptor.HeartbeatInterceptor - HeartbeatInterceptor configured with padding value 'X', client frequency 30 seconds and server frequency 120 seconds
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Heartbeat Interceptor Support with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor SSE Interceptor Support with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor JSONP Interceptor Support with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Atmosphere JavaScript Protocol with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.WebSocketMessageSuspendInterceptor with priority AFTER_DEFAULT 
2025-07-30 17:10:46,054 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor Browser disconnection detection with priority AFTER_DEFAULT 
2025-07-30 17:10:46,055 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Installed AtmosphereInterceptor org.atmosphere.interceptor.IdleResourceInterceptor with priority BEFORE_DEFAULT 
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using EndpointMapper class org.atmosphere.util.DefaultEndpointMapper
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterCache: org.atmosphere.cache.UUIDBroadcasterCache
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Default Broadcaster Class: org.atmosphere.cpr.DefaultBroadcaster
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Shared List Resources: false
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Broadcaster Polling Wait Time 100
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Shared ExecutorService supported: true
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Messaging ExecutorService Pool Size unavailable - Not instance of ThreadPoolExecutor
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Async I/O Thread Pool Size: 200
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using BroadcasterFactory: org.atmosphere.cpr.DefaultBroadcasterFactory
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using AtmosphereResurceFactory: org.atmosphere.cpr.DefaultAtmosphereResourceFactory
2025-07-30 17:10:46,056 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Using WebSocketProcessor: org.atmosphere.websocket.DefaultWebSocketProcessor
2025-07-30 17:10:46,062 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Invoke AtmosphereInterceptor on WebSocket message true
2025-07-30 17:10:46,062 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - HttpSession supported: false
2025-07-30 17:10:46,063 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using Spring Web ObjectFactory for dependency injection and object creation
2025-07-30 17:10:46,063 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere is using async support: org.atmosphere.container.JSR356AsyncSupport running under container: Apache Tomcat/9.0.60 using javax.servlet/3.0 and jsr356/WebSocket API
2025-07-30 17:10:46,066 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - Atmosphere Framework 2.6.4 started.
2025-07-30 17:10:46,066 [Thread-41] INFO  NotificationService - [main] INFO  org.atmosphere.cpr.AtmosphereFramework - 
2025-07-30 17:10:46,066 [Thread-41] INFO  NotificationService - 
2025-07-30 17:10:46,067 [Thread-41] INFO  NotificationService - 	For Atmosphere Framework Commercial Support, visit 
2025-07-30 17:10:46,067 [Thread-41] INFO  NotificationService - 	http://www.async-io.org/ or send an <NAME_EMAIL>
2025-07-30 17:10:46,067 [Thread-41] INFO  NotificationService - 
2025-07-30 17:10:46,070 [Thread-41] INFO  NotificationService - [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 40608 (http) with context path ''
2025-07-30 17:10:46,077 [Thread-41] INFO  NotificationService - [main] INFO  c.s.polarion.service.notification.Application - Started Application in 1.963 seconds (JVM running for 2.536)
2025-07-30 17:10:53,520 [Thread-37] INFO  NotificationService - Notification service was started successfully.
