2025-07-30 11:17:30,008 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:30,009 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 11:17:34,632 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 11:17:35,028 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.396 s. ]
2025-07-30 11:17:35,029 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 11:17:35,089 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0602 s. ]
2025-07-30 11:17:35,155 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 11:17:35,280 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 65 s. ]
2025-07-30 11:17:35,494 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.49 s. ]
2025-07-30 11:17:35,634 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:35,634 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 11:17:35,663 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.17 s. ]
2025-07-30 11:17:35,664 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:35,664 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-4 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (5/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (3/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-3 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-5 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (4/9)
2025-07-30 11:17:35,680 [LowLevelDataService-contextInitializer-6 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 11:17:35,690 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 11:17:35,861 [LowLevelDataService-contextInitializer-4 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 11:17:35,944 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 11:17:36,506 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.84 s. ]
2025-07-30 11:17:36,521 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:36,521 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 11:17:36,811 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 11:17:36,831 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 11:17:36,861 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:36,861 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 11:17:36,866 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 11:17:36,948 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 11:17:37,022 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (3/9)
2025-07-30 11:17:37,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (4/9)
2025-07-30 11:17:37,079 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (5/9)
2025-07-30 11:17:37,126 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (6/9)
2025-07-30 11:17:37,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 11:17:37,221 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 11:17:37,275 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 11:17:37,275 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.44 s. ]
2025-07-30 11:17:37,275 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:37,275 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 11:17:37,290 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 11:17:37,290 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:37,290 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 11:17:37,405 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 11:17:37,407 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 11:17:37,555 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.26 s. ]
2025-07-30 11:17:37,555 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:37,555 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 11:17:37,562 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 11:17:37,562 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 11:17:37,562 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 11:17:44,631 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 7.07 s. ]
2025-07-30 11:17:44,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 11:17:44,632 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 14.6 s. ]
2025-07-30 11:17:44,632 [main] INFO  com.polarion.platform.startup - ****************************************************************
