2025-07-30 17:51:50,983 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0506 s [69% update (144x), 31% query (12x)] (221x), svn: 0.0101 s [51% getLatestRevision (2x), 37% testConnection (1x)] (4x)
2025-07-30 17:51:51,083 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0297 s [64% getDir2 content (2x), 29% info (3x)] (6x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.669 s, CPU [user: 0.175 s, system: 0.255 s], Allocated memory: 53.1 MB, transactions: 0, ObjectMaps: 0.105 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.22 s, system: 0.323 s], Allocated memory: 70.1 MB, transactions: 0, ObjectMaps: 0.124 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0964 s, system: 0.192 s], Allocated memory: 23.8 MB, transactions: 0, ObjectMaps: 0.0624 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0346 s [74% log2 (5x), 17% testConnection (1x)] (7x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.669 s, CPU [user: 0.0867 s, system: 0.133 s], Allocated memory: 14.7 MB, transactions: 0, svn: 0.112 s [47% log2 (10x), 16% info (5x), 14% getLatestRevision (3x), 13% log (1x)] (24x), ObjectMaps: 0.0752 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0688 s, system: 0.104 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0901 s [83% log2 (10x)] (13x)
2025-07-30 17:51:51,756 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.668 s, CPU [user: 0.0429 s, system: 0.0931 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.0726 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0363 s [69% log2 (5x), 19% testConnection (1x)] (7x)
2025-07-30 17:51:51,756 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.469 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.31 s [64% log2 (36x), 13% getLatestRevision (9x), 11% testConnection (6x)] (61x)
2025-07-30 17:51:52,010 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.211 s [100% getReadConfiguration (48x)] (48x), svn: 0.0946 s [76% info (18x), 15% log2 (1x)] (38x)
2025-07-30 17:51:52,303 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.228 s [73% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.169 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:51:52,543 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.226 s [100% doFinishStartup (1x)] (1x), commit: 0.0543 s [100% Revision (1x)] (1x), Lucene: 0.0345 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0134 s [100% objectsToInv (1x)] (1x), DB: 0.013 s [35% query (1x), 34% update (3x), 23% execute (1x)] (8x)
2025-07-30 17:52:48,671 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.472 s [64% info (158x), 30% getLatestRevision (19x)] (187x), PullingJob: 0.14 s [100% collectChanges (18x)] (18x)
2025-07-30 17:52:48,680 [Worker-0: Attachment Indexer | u:p | job: polarion.jobs.attachment.indexer] INFO  TXLOGGER - Summary after AttachmentIndexer: transactions: 4, persistence listener: 0.0126 s [65% indexRefreshPersistenceListener (1x), 23% WorkItemActivityCreator (1x)] (7x), notification worker: 0.0125 s [48% RevisionActivityCreator (1x), 24% WorkItemActivityCreator (1x), 10% TestRunActivityCreator (1x)] (5x), resolve: 0.0038 s [100% Revision (1x)] (1x), EHCache: 0.00241 s [100% GET (10x)] (10x)
2025-07-30 17:52:48,952 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 6616afec08444_0_6616afec08444_0_: finished. Total: 0.278 s, CPU [user: 0.135 s, system: 0.0236 s], Allocated memory: 20.3 MB, resolve: 0.0929 s [60% User (2x), 36% Project (1x)] (5x), Lucene: 0.0366 s [100% search (1x)] (1x), ObjectMaps: 0.0283 s [55% getPrimaryObjectLocation (2x), 33% getPrimaryObjectProperty (2x)] (11x), svn: 0.0257 s [42% getLatestRevision (2x), 18% getFile content (2x), 17% testConnection (1x), 12% log (1x)] (8x)
2025-07-30 17:52:49,636 [Activities-Bulk-Publisher] INFO  TXLOGGER - Summary: Total: 0.126 s, CPU [user: 0.00886 s, system: 0.00566 s], Allocated memory: 1.8 MB, transactions: 0, Lucene: 0.126 s [100% add (1x)] (1x)
2025-07-30 17:52:49,832 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 1.08 s, CPU [user: 0.00635 s, system: 0.00286 s], Allocated memory: 315.9 kB, transactions: 1
2025-07-30 17:52:49,833 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 24, notification worker: 0.28 s [100% RevisionActivityCreator (1x)] (1x), Lucene: 0.166 s [76% add (1x), 22% search (1x)] (3x), resolve: 0.107 s [65% User (3x), 31% Project (1x)] (6x), Incremental Baseline: 0.0845 s [100% WorkItem (22x)] (22x), svn: 0.0364 s [59% getLatestRevision (3x), 12% getFile content (2x), 12% testConnection (1x)] (9x), ObjectMaps: 0.0315 s [59% getPrimaryObjectLocation (3x), 29% getPrimaryObjectProperty (2x)] (12x)
2025-07-30 17:52:49,833 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 1.17 s, CPU [user: 0.17 s, system: 0.0307 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.939 s [98% getDatedRevision (181x)] (183x), Lucene: 0.0933 s [65% buildBaseline (23x), 35% buildBaselineSnapshots (1x)] (24x)
2025-07-30 17:52:50,252 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afec0a445_0_6616afec0a445_0_: finished. Total: 1.57 s, CPU [user: 0.324 s, system: 0.0915 s], Allocated memory: 47.1 MB, svn: 0.779 s [51% getDatedRevision (181x), 32% getDir2 content (25x)] (307x), resolve: 0.408 s [100% Category (96x)] (96x), ObjectMaps: 0.135 s [43% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 21% getLastPromoted (96x)] (387x), RepositoryConfigService: 0.0823 s [100% getReadConfiguration (2x)] (2x)
2025-07-30 17:52:50,433 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afeda5848_0_6616afeda5848_0_: finished. Total: 0.106 s, CPU [user: 0.0513 s, system: 0.00857 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.053 s [55% getReadConfiguration (162x), 45% getReadUserConfiguration (10x)] (172x), svn: 0.0484 s [60% info (19x), 33% getFile content (15x)] (36x), resolve: 0.0266 s [100% User (9x)] (9x), ObjectMaps: 0.0126 s [57% getPrimaryObjectProperty (8x), 24% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 17:52:50,668 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afedcbc4a_0_6616afedcbc4a_0_: finished. Total: 0.19 s, CPU [user: 0.0608 s, system: 0.00711 s], Allocated memory: 19.9 MB, svn: 0.139 s [57% getDir2 content (17x), 43% getFile content (44x)] (62x), RepositoryConfigService: 0.092 s [99% getReadConfiguration (170x)] (192x)
2025-07-30 17:52:52,145 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afedfb44b_0_6616afedfb44b_0_: finished. Total: 1.48 s, CPU [user: 0.553 s, system: 0.0613 s], Allocated memory: 1.1 GB, RepositoryConfigService: 1.22 s [74% getReadConfiguration (8682x), 26% getExistingPrefixes (129x)] (9021x), svn: 0.773 s [47% getFile content (412x), 36% info (99x)] (533x)
2025-07-30 17:52:52,260 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afef6c44e_0_6616afef6c44e_0_: finished. Total: 0.114 s, CPU [user: 0.0227 s, system: 0.00293 s], Allocated memory: 19.2 MB, svn: 0.103 s [50% getDir2 content (14x), 40% info (37x)] (81x), RepositoryConfigService: 0.0317 s [54% getReadConfiguration (124x), 46% getExistingPrefixes (12x)] (148x)
2025-07-30 17:52:52,705 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 6616afef94450_0_6616afef94450_0_: finished. Total: 0.4 s, CPU [user: 0.14 s, system: 0.0112 s], Allocated memory: 394.2 MB, RepositoryConfigService: 0.29 s [55% getReadConfiguration (2787x), 45% getExistingPrefixes (89x)] (3025x), svn: 0.287 s [48% info (152x), 27% getFile content (185x), 24% getDir2 content (17x)] (355x)
2025-07-30 17:52:52,705 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 4.03 s, CPU [user: 1.22 s, system: 0.194 s], Allocated memory: 1.6 GB, transactions: 10, svn: 2.22 s [31% getFile content (807x), 27% getDir2 content (104x), 23% info (347x)] (1453x), RepositoryConfigService: 1.81 s [73% getReadConfiguration (12019x), 26% getExistingPrefixes (259x)] (12691x), resolve: 0.465 s [88% Category (96x)] (117x)
2025-07-30 17:52:52,705 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 3.16 s [42% getDatedRevision (362x), 21% getFile content (807x), 19% getDir2 content (104x)] (1637x), RepositoryConfigService: 1.81 s [73% getReadConfiguration (12019x), 26% getExistingPrefixes (259x)] (12691x), resolve: 0.466 s [87% Category (96x)] (118x), ObjectMaps: 0.159 s [45% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x), 20% getLastPromoted (108x)] (442x)
2025-07-30 17:53:22,176 [ajp-nio-127.0.0.1-8889-exec-2 | cID:5abfb95c-7f000001-4023e834-d2160edb] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections/test': Total: 31.1 s, CPU [user: 0.85 s, system: 0.0826 s], Allocated memory: 56.6 MB, transactions: 2
