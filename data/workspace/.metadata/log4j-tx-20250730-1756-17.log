2025-07-30 17:56:22,717 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0592 s [71% update (144x), 28% query (12x)] (221x), svn: 0.0144 s [61% getLatestRevision (2x), 26% testConnection (1x)] (4x)
2025-07-30 17:56:22,903 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.106 s [87% getDir2 content (2x)] (6x)
2025-07-30 17:56:23,887 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.974 s, CPU [user: 0.244 s, system: 0.288 s], Allocated memory: 53.0 MB, transactions: 0, ObjectMaps: 0.145 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:56:23,888 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.974 s, CPU [user: 0.0645 s, system: 0.0869 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.077 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:56:23,888 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.974 s, CPU [user: 0.284 s, system: 0.346 s], Allocated memory: 70.3 MB, transactions: 0, ObjectMaps: 0.136 s [100% getAllPrimaryObjects (1x)] (10x)
2025-07-30 17:56:23,888 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.975 s, CPU [user: 0.0912 s, system: 0.124 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.17 s [86% log2 (10x)] (13x), ObjectMaps: 0.0535 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:56:23,888 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.975 s, CPU [user: 0.132 s, system: 0.2 s], Allocated memory: 23.7 MB, transactions: 0, ObjectMaps: 0.0667 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 17:56:23,888 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.975 s, CPU [user: 0.141 s, system: 0.14 s], Allocated memory: 14.8 MB, transactions: 0, svn: 0.159 s [32% log2 (10x), 22% info (5x), 19% log (1x), 13% getLatestRevision (3x)] (24x), ObjectMaps: 0.0889 s [100% getAllPrimaryObjects (2x)] (14x)
2025-07-30 17:56:23,888 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.568 s [100% getAllPrimaryObjects (8x)] (59x), svn: 0.437 s [62% log2 (36x), 11% getLatestRevision (9x), 10% testConnection (6x)] (61x)
2025-07-30 17:56:24,019 [main | u:p] INFO  TXLOGGER - Tx 6616b0be3b401_0_6616b0be3b401_0_: finished. Total: 0.101 s, CPU [user: 0.0793 s, system: 0.00389 s], Allocated memory: 21.8 MB
2025-07-30 17:56:24,182 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.247 s [100% getReadConfiguration (48x)] (48x), svn: 0.104 s [85% info (18x)] (38x)
2025-07-30 17:56:24,540 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.279 s [75% info (94x), 18% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.2 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 17:56:24,784 [PolarionDocIdCreator-4] INFO  TXLOGGER - Tx fillBloomFilter [RichPageAttachment]: finished. Total: 0.106 s, CPU [user: 0.0345 s, system: 0.0091 s], Allocated memory: 11.2 MB
2025-07-30 17:56:24,837 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.276 s [100% doFinishStartup (1x)] (1x), commit: 0.064 s [100% Revision (1x)] (1x), Lucene: 0.0406 s [100% refresh (1x)] (1x), derivedLinkedRevisionsContributor: 0.0199 s [100% objectsToInv (1x)] (1x), DB: 0.015 s [46% update (3x), 39% query (1x)] (8x)
