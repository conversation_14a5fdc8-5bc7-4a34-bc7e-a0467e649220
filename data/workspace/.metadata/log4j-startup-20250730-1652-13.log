2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:14,061 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 16:52:18,859 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 16:52:19,060 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.201 s. ]
2025-07-30 16:52:19,060 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 16:52:19,111 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.0506 s. ]
2025-07-30 16:52:19,171 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 16:52:19,334 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 12 s. ]
2025-07-30 16:52:19,556 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.5 s. ]
2025-07-30 16:52:19,647 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:19,647 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 16:52:19,683 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.13 s. ]
2025-07-30 16:52:19,683 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:19,683 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 16:52:19,690 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 16:52:19,690 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (2/9)
2025-07-30 16:52:19,690 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (3/9)
2025-07-30 16:52:19,690 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (4/9)
2025-07-30 16:52:19,690 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 16:52:19,691 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 16:52:19,702 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 16:52:19,869 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 16:52:19,966 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 16:52:20,420 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.74 s. ]
2025-07-30 16:52:20,431 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:20,431 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 16:52:20,636 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 16:52:20,650 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.23 s. ]
2025-07-30 16:52:20,674 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:20,674 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 16:52:20,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 16:52:20,745 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 16:52:20,798 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 16:52:20,847 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 16:52:20,903 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 16:52:20,938 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 16:52:20,964 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 16:52:21,013 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 16:52:21,053 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 16:52:21,053 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.4 s. ]
2025-07-30 16:52:21,053 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:21,053 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 16:52:21,067 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 16:52:21,067 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:21,067 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 16:52:21,172 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 16:52:21,174 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 16:52:21,304 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.24 s. ]
2025-07-30 16:52:21,305 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:21,305 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 16:52:21,312 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 16:52:21,312 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 16:52:21,312 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 16:52:24,926 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.61 s. ]
2025-07-30 16:52:24,926 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 16:52:24,927 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 10.9 s. ]
2025-07-30 16:52:24,927 [main] INFO  com.polarion.platform.startup - ****************************************************************
