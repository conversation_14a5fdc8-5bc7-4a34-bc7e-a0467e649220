2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 19:10:56,390 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:10:56,391 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 19:11:00,585 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 19:11:00,744 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.159 s. ]
2025-07-30 19:11:00,744 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 19:11:00,840 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.095 s. ]
2025-07-30 19:11:00,892 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 19:11:01,009 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 19 s. ]
2025-07-30 19:11:01,227 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 4.85 s. ]
2025-07-30 19:11:01,314 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:01,314 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 19:11:01,338 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.11 s. ]
2025-07-30 19:11:01,338 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:01,338 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 19:11:01,343 [LowLevelDataService-contextInitializer-3 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (2/9)
2025-07-30 19:11:01,343 [LowLevelDataService-contextInitializer-5 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 19:11:01,343 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (1/9)
2025-07-30 19:11:01,343 [LowLevelDataService-contextInitializer-4 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (4/9)
2025-07-30 19:11:01,344 [LowLevelDataService-contextInitializer-6 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (5/9)
2025-07-30 19:11:01,344 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (6/9)
2025-07-30 19:11:01,350 [LowLevelDataService-contextInitializer-1 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (7/9)
2025-07-30 19:11:01,477 [LowLevelDataService-contextInitializer-5 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (8/9)
2025-07-30 19:11:01,564 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (9/9)
2025-07-30 19:11:02,110 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.77 s. ]
2025-07-30 19:11:02,121 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:02,121 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 19:11:02,319 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 19:11:02,331 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.22 s. ]
2025-07-30 19:11:02,354 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:02,354 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 19:11:02,358 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 19:11:02,427 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 19:11:02,467 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (3/9)
2025-07-30 19:11:02,514 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (4/9)
2025-07-30 19:11:02,539 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (5/9)
2025-07-30 19:11:02,561 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (6/9)
2025-07-30 19:11:02,595 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (7/9)
2025-07-30 19:11:02,635 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (8/9)
2025-07-30 19:11:02,677 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (9/9)
2025-07-30 19:11:02,678 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.35 s. ]
2025-07-30 19:11:02,678 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:02,678 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 19:11:02,691 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.01 s. ]
2025-07-30 19:11:02,705 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:02,705 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 19:11:02,795 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 11 objects in index: BuildArtifact
2025-07-30 19:11:02,817 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 11 from 11 objects were refreshed (100%)
2025-07-30 19:11:02,930 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 19:11:02,930 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 19:11:03,009 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.32 s. ]
2025-07-30 19:11:03,010 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:03,010 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 19:11:03,018 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 19:11:03,018 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 19:11:03,018 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 19:11:09,338 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 6.32 s. ]
2025-07-30 19:11:09,338 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 19:11:09,338 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 12.9 s. ]
2025-07-30 19:11:09,339 [main] INFO  com.polarion.platform.startup - ****************************************************************
