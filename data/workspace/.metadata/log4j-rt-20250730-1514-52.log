2025-07-30 15:15:00,627 [Catalina-utility-1] INFO  com.siemens.polarion.rt.config.RtAppConfig - RT server config is created
2025-07-30 15:15:01,302 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Initialization of RT parsers...
2025-07-30 15:15:01,304 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.java (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 15:15:01,304 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.xml (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 15:15:01,306 [Catalina-utility-1] INFO  com.siemens.polarion.rt.parsers.RtParserProviderImpl - Found parser com.siemens.polarion.rt.parsers.c (contributed by 'com.siemens.polarion.rt.parsers.c[92]')
2025-07-30 15:15:01,456 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Initialization of RT collectors...
2025-07-30 15:15:01,457 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for subversion (contributed by 'com.siemens.polarion.rt[86]')
2025-07-30 15:15:01,457 [Catalina-utility-1] INFO  com.siemens.polarion.rt.collectors.RtCollectorProviderImpl - Found collector for git (contributed by 'com.siemens.polarion.rt.collectors.git[88]')
2025-07-30 15:15:02,102 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt configuration local mediator is started
2025-07-30 15:15:02,228 [ajp-nio-127.0.0.1-8889-exec-1 | cID:5a2f3db8-7f000001-583e921d-a22b7bf7] INFO  com.siemens.polarion.rt.dataprovider.controller.RtDataProviderController - RT server is notified to update all configurations.
2025-07-30 15:15:02,247 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtNotifier - RT server was successfully notified of configuration change.
2025-07-30 15:15:02,247 [main | u:p] INFO  com.siemens.polarion.rt.communication.connection.RtCommunicationStartup - Rt notification service is started
