2025-07-30 11:27:08,191 [main | u:p] INFO  TXLOGGER - Summary for 'Platform startup': transactions: 0, DB: 0.0583 s [65% update (144x), 35% query (12x)] (221x), svn: 0.0158 s [63% getLatestRevision (2x), 28% testConnection (1x)] (4x)
2025-07-30 11:27:08,305 [main | u:p] INFO  TXLOGGER - Summary for 'Context recognition': transactions: 0, svn: 0.0299 s [57% getDir2 content (2x), 36% info (3x)] (6x)
2025-07-30 11:27:09,012 [LowLevelDataService-contextInitializer-4 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.0742 s, system: 0.111 s], Allocated memory: 10.7 MB, transactions: 0, svn: 0.0851 s [81% log2 (10x)] (13x)
2025-07-30 11:27:09,012 [LowLevelDataService-contextInitializer-2 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.193 s, system: 0.258 s], Allocated memory: 52.8 MB, transactions: 0, ObjectMaps: 0.109 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:27:09,012 [LowLevelDataService-contextInitializer-6 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.238 s, system: 0.315 s], Allocated memory: 70.4 MB, transactions: 0, ObjectMaps: 0.123 s [100% getAllPrimaryObjects (1x)] (12x)
2025-07-30 11:27:09,013 [LowLevelDataService-contextInitializer-5 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.081 s, system: 0.142 s], Allocated memory: 12.4 MB, transactions: 0, ObjectMaps: 0.0877 s [100% getAllPrimaryObjects (2x)] (14x), svn: 0.0771 s [76% log2 (10x), 19% getLatestRevision (2x)] (13x)
2025-07-30 11:27:09,013 [LowLevelDataService-contextInitializer-3 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.0487 s, system: 0.0915 s], Allocated memory: 6.8 MB, transactions: 0, ObjectMaps: 0.08 s [100% getAllPrimaryObjects (1x)] (7x), svn: 0.0499 s [83% log2 (5x)] (7x)
2025-07-30 11:27:09,013 [LowLevelDataService-contextInitializer-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.704 s, CPU [user: 0.125 s, system: 0.192 s], Allocated memory: 26.4 MB, transactions: 0, svn: 0.0865 s [26% info (5x), 25% log2 (5x), 23% log (1x), 15% getLatestRevision (2x)] (18x), ObjectMaps: 0.0753 s [100% getAllPrimaryObjects (1x)] (7x)
2025-07-30 11:27:09,013 [main | u:p] INFO  TXLOGGER - Summary for 'Context initialization': transactions: 0, ObjectMaps: 0.509 s [100% getAllPrimaryObjects (8x)] (61x), svn: 0.336 s [63% log2 (36x), 14% getLatestRevision (9x), 9% testConnection (6x)] (61x)
2025-07-30 11:27:09,247 [main | u:p] INFO  TXLOGGER - Summary for 'Revisions processing': transactions: 16, RepositoryConfigService: 0.197 s [100% getReadConfiguration (48x)] (48x), svn: 0.077 s [82% info (18x)] (38x)
2025-07-30 11:27:09,638 [main | u:p] INFO  TXLOGGER - Summary for 'Build artifacts recognition': transactions: 45, svn: 0.316 s [76% info (94x), 17% getLatestRevision (16x)] (166x), RepositoryConfigService: 0.236 s [100% getReadConfiguration (54x)] (54x)
2025-07-30 11:27:09,909 [main | u:p] INFO  TXLOGGER - Summary for 'Data indexing': transactions: 29, PersistenceEngineListener: 0.256 s [100% doFinishStartup (1x)] (1x), commit: 0.0427 s [100% Revision (1x)] (1x), Lucene: 0.0422 s [100% refresh (1x)] (1x), DB: 0.0315 s [56% update (3x), 25% query (1x)] (8x), derivedLinkedRevisionsContributor: 0.017 s [100% objectsToInv (1x)] (1x), SubterraURITable: 0.0147 s [100% addIfNotExistsDB (1x)] (1x)
2025-07-30 11:27:22,948 [main] INFO  TXLOGGER - Summary for 'Polarion startup': transactions: 2, svn: 0.569 s [82% info (158x)] (173x), PullingJob: 0.0647 s [100% collectChanges (4x)] (4x)
2025-07-30 11:27:23,361 [Notification-Worker-6 | u:p] INFO  TXLOGGER - Tx 661657b453c42_0_661657b453c42_0_: finished. Total: 0.401 s, CPU [user: 0.146 s, system: 0.023 s], Allocated memory: 21.0 MB, resolve: 0.121 s [72% User (2x), 22% Project (1x)] (5x), ObjectMaps: 0.0556 s [75% getPrimaryObjectLocation (2x), 18% getPrimaryObjectProperty (2x)] (11x), Lucene: 0.0323 s [100% search (1x)] (1x), svn: 0.0295 s [47% getLatestRevision (3x), 22% testConnection (1x), 16% log (1x)] (9x)
2025-07-30 11:27:23,837 [DBHistoryCreator-1 | u:p] INFO  TXLOGGER - Summary: Total: 0.77 s, CPU [user: 0.0058 s, system: 0.00219 s], Allocated memory: 315.5 kB, transactions: 1
2025-07-30 11:27:23,837 [Worker-1: DB History Creator | u:p | job: polarion.jobs.db.history] INFO  TXLOGGER - Summary after DBHistoryCreator: transactions: 28, notification worker: 0.434 s [97% RevisionActivityCreator (2x)] (6x), resolve: 0.176 s [77% User (4x), 15% Project (1x)] (8x), ObjectMaps: 0.0635 s [78% getPrimaryObjectLocation (4x), 16% getPrimaryObjectProperty (2x)] (13x), Lucene: 0.0581 s [56% search (1x), 37% add (1x)] (3x), persistence listener: 0.0321 s [82% indexRefreshPersistenceListener (1x)] (7x), svn: 0.0295 s [47% getLatestRevision (3x), 22% testConnection (1x), 16% log (1x)] (9x)
2025-07-30 11:27:23,838 [Worker-1: DB History Creator | u:p] INFO  TXLOGGER - Summary for 'job: polarion.jobs.db.history': Total: 0.776 s, CPU [user: 0.152 s, system: 0.0256 s], Allocated memory: 18.4 MB, transactions: 24, svn: 0.68 s [98% getDatedRevision (181x)] (183x), Lucene: 0.054 s [81% buildBaselineSnapshots (1x)] (24x)
2025-07-30 11:27:24,260 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657b454045_0_661657b454045_0_: finished. Total: 1.3 s, CPU [user: 0.325 s, system: 0.0851 s], Allocated memory: 47.2 MB, svn: 0.781 s [50% getDatedRevision (181x), 33% getDir2 content (25x)] (307x), resolve: 0.396 s [100% Category (96x)] (96x), ObjectMaps: 0.139 s [41% getPrimaryObjectProperty (96x), 36% getPrimaryObjectLocation (96x), 23% getLastPromoted (96x)] (387x)
2025-07-30 11:27:24,460 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657b5ad848_0_661657b5ad848_0_: finished. Total: 0.117 s, CPU [user: 0.0579 s, system: 0.00899 s], Allocated memory: 8.0 MB, RepositoryConfigService: 0.0537 s [50% getReadUserConfiguration (10x), 50% getReadConfiguration (162x)] (172x), svn: 0.0506 s [60% info (19x), 33% getFile content (15x)] (36x), resolve: 0.0353 s [100% User (9x)] (9x), ObjectMaps: 0.0131 s [53% getPrimaryObjectProperty (8x), 28% getPrimaryObjectLocation (8x)] (32x)
2025-07-30 11:27:24,751 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657b5d604a_0_661657b5d604a_0_: finished. Total: 0.247 s, CPU [user: 0.0799 s, system: 0.00867 s], Allocated memory: 19.9 MB, svn: 0.191 s [72% getDir2 content (17x), 28% getFile content (44x)] (62x), RepositoryConfigService: 0.0854 s [97% getReadConfiguration (170x)] (192x)
2025-07-30 11:27:25,464 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657b613c4b_0_661657b613c4b_0_: finished. Total: 0.713 s, CPU [user: 0.329 s, system: 0.0215 s], Allocated memory: 1.1 GB, RepositoryConfigService: 0.532 s [97% getReadConfiguration (8682x)] (9021x), svn: 0.376 s [65% getFile content (412x), 35% getDir2 content (21x)] (434x)
2025-07-30 11:27:25,902 [PreLoadDataService | u:p] INFO  TXLOGGER - Tx 661657b6e104e_0_661657b6e104e_0_: finished. Total: 0.33 s, CPU [user: 0.126 s, system: 0.00771 s], Allocated memory: 387.0 MB, RepositoryConfigService: 0.21 s [96% getReadConfiguration (2787x)] (3025x), svn: 0.204 s [51% getFile content (185x), 49% getDir2 content (21x)] (207x)
2025-07-30 11:27:25,902 [PreLoadDataService] INFO  TXLOGGER - Summary for 'PreloadDataService': Total: 2.95 s, CPU [user: 1 s, system: 0.144 s], Allocated memory: 1.6 GB, transactions: 10, svn: 1.74 s [41% getDir2 content (115x), 33% getFile content (807x), 23% getDatedRevision (181x)] (1141x), RepositoryConfigService: 0.944 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.464 s [85% Category (96x)] (117x), ObjectMaps: 0.162 s [43% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:27:25,902 [PreLoadDataService] INFO  TXLOGGER - Summary after preload: transactions: 57, svn: 2.42 s [44% getDatedRevision (362x), 29% getDir2 content (115x), 24% getFile content (807x)] (1325x), RepositoryConfigService: 0.944 s [94% getReadConfiguration (12019x)] (12691x), resolve: 0.464 s [85% Category (96x)] (118x), ObjectMaps: 0.162 s [43% getPrimaryObjectProperty (108x), 35% getPrimaryObjectLocation (114x), 22% getLastPromoted (108x)] (442x)
2025-07-30 11:27:29,035 [ajp-nio-127.0.0.1-8889-exec-2 | cID:595ee768-0a465820-63c9df77-a0e33f91] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/': Total: 0.418 s, CPU [user: 0.236 s, system: 0.0384 s], Allocated memory: 42.6 MB, transactions: 2, PolarionAuthenticator: 0.367 s [100% authenticate (1x)] (1x)
2025-07-30 11:27:29,341 [ajp-nio-127.0.0.1-8889-exec-4 | cID:595ee9c6-0a465820-63c9df77-acce50dc] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/global/canModifyConfiguration': Total: 0.117 s, CPU [user: 0.0598 s, system: 0.00851 s], Allocated memory: 9.5 MB, transactions: 0
2025-07-30 11:27:29,361 [ajp-nio-127.0.0.1-8889-exec-6 | cID:595ee9d9-0a465820-63c9df77-b2504022] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/proxy_contributions?_=1753846049189': Total: 0.12 s, CPU [user: 0.029 s, system: 0.00394 s], Allocated memory: 1.9 MB, transactions: 0
2025-07-30 11:27:29,405 [ajp-nio-127.0.0.1-8889-exec-8 | cID:595ee9db-0a465820-63c9df77-4e20f1e2] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/connections?_=1753846049191': Total: 0.161 s, CPU [user: 0.053 s, system: 0.00819 s], Allocated memory: 9.4 MB, transactions: 1, RepositoryConfigService: 0.0781 s [100% getReadConfiguration (1x)] (1x), svn: 0.0126 s [70% testConnection (1x), 29% getFile content (2x)] (4x)
2025-07-30 11:27:29,424 [ajp-nio-127.0.0.1-8889-exec-7 | cID:595ee9d9-0a465820-63c9df77-74891596] INFO  TXLOGGER - Summary for 'servlet /polarion/synchronizer/rest/projects/WBSdev/sync_pairs?_=1753846049190': Total: 0.182 s, CPU [user: 0.0252 s, system: 0.00282 s], Allocated memory: 4.4 MB, transactions: 1, RepositoryConfigService: 0.0971 s [100% getReadConfiguration (1x)] (1x)
