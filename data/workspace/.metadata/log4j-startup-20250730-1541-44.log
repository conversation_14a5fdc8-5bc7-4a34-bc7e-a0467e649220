2025-07-30 15:41:44,963 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:41:44,963 [main] INFO  com.polarion.platform.startup -                 Polarion starting, please wait!                 
2025-07-30 15:41:44,964 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:41:44,964 [main] INFO  com.polarion.platform.startup - JVM version: 11.0.27+6-LTS
2025-07-30 15:41:44,964 [main] INFO  com.polarion.platform.startup - Server start mode: normal
2025-07-30 15:41:44,964 [main] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:44,964 [main] INFO  com.polarion.platform.startup - Platform startup (Phase: 1/9)
2025-07-30 15:41:49,902 [main] INFO  com.polarion.platform.startup - Opening historical database...
2025-07-30 15:41:50,324 [main] INFO  com.polarion.platform.startup - Opening of historical database finished [ TIME 0.422 s. ]
2025-07-30 15:41:50,324 [main] INFO  com.polarion.platform.startup - Opening head database...
2025-07-30 15:41:50,435 [main] INFO  com.polarion.platform.startup - Opening of head database finished [ TIME 0.11 s. ]
2025-07-30 15:41:50,519 [main] INFO  com.polarion.platform.startup - Current authentication configuration source is xml file: /opt/polarion/data/authentication/authentication.xml
2025-07-30 15:41:50,666 [main | u:p] INFO  com.polarion.platform.startup -  -- Polarion startup estimation:  [ TIME 10 s. ]
2025-07-30 15:41:50,898 [main | u:p] INFO  com.polarion.platform.startup - Platform startup ... took  [ TIME 5.94 s. ]
2025-07-30 15:41:50,995 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:50,995 [main | u:p] INFO  com.polarion.platform.startup - Context recognition (Phase: 2/9)
2025-07-30 15:41:51,110 [main | u:p] INFO  com.polarion.platform.startup - Context recognition ... took  [ TIME 0.21 s. ]
2025-07-30 15:41:51,111 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:51,111 [main | u:p] INFO  com.polarion.platform.startup - Context initialization (Phase: 3/9)
2025-07-30 15:41:51,163 [LowLevelDataService-contextInitializer-5 | u:p | context: WBS] INFO  com.polarion.platform.startup -  -- Initializing context: WBS (1/9)
2025-07-30 15:41:51,163 [LowLevelDataService-contextInitializer-2 | u:p | cluster: default] INFO  com.polarion.platform.startup -  -- Initializing cluster: default (2/9)
2025-07-30 15:41:51,163 [LowLevelDataService-contextInitializer-6 | u:p | context: --Demo Projects] INFO  com.polarion.platform.startup -  -- Initializing context: --Demo Projects (3/9)
2025-07-30 15:41:51,164 [LowLevelDataService-contextInitializer-1 | u:p | context: [GLOBAL]] INFO  com.polarion.platform.startup -  -- Initializing context: [GLOBAL] (4/9)
2025-07-30 15:41:51,165 [LowLevelDataService-contextInitializer-4 | u:p | context: WBSdev] INFO  com.polarion.platform.startup -  -- Initializing context: WBSdev (6/9)
2025-07-30 15:41:51,165 [LowLevelDataService-contextInitializer-3 | u:p | context: hesai] INFO  com.polarion.platform.startup -  -- Initializing context: hesai (5/9)
2025-07-30 15:41:51,279 [LowLevelDataService-contextInitializer-1 | u:p | context: library] INFO  com.polarion.platform.startup -  -- Initializing context: library (7/9)
2025-07-30 15:41:51,441 [LowLevelDataService-contextInitializer-6 | u:p | context: drivepilot] INFO  com.polarion.platform.startup -  -- Initializing context: drivepilot (8/9)
2025-07-30 15:41:51,566 [LowLevelDataService-contextInitializer-1 | u:p | context: elibrary] INFO  com.polarion.platform.startup -  -- Initializing context: elibrary (9/9)
2025-07-30 15:41:52,104 [main | u:p] INFO  com.polarion.platform.startup - Context initialization ... took  [ TIME 0.99 s. ]
2025-07-30 15:41:52,121 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:52,121 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing (Phase: 4/9)
2025-07-30 15:41:52,419 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing new revisions from repository default in context ContextId[context [global]]
2025-07-30 15:41:52,433 [main | u:p] INFO  com.polarion.platform.startup - Revisions processing ... took  [ TIME 0.33 s. ]
2025-07-30 15:41:52,463 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:52,463 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition (Phase: 5/9)
2025-07-30 15:41:52,466 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: [GLOBAL] (1/9)
2025-07-30 15:41:52,522 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from cluster: default (2/9)
2025-07-30 15:41:52,574 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: hesai (3/9)
2025-07-30 15:41:52,614 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBSdev (4/9)
2025-07-30 15:41:52,669 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: WBS (5/9)
2025-07-30 15:41:52,708 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: --Demo Projects (6/9)
2025-07-30 15:41:52,739 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: library (7/9)
2025-07-30 15:41:52,796 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: drivepilot (8/9)
2025-07-30 15:41:52,854 [main | u:p] INFO  com.polarion.platform.startup -  -- Processing build artifacts from context: elibrary (9/9)
2025-07-30 15:41:52,854 [main | u:p] INFO  com.polarion.platform.startup - Build artifacts recognition ... took  [ TIME 0.42 s. ]
2025-07-30 15:41:52,854 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:52,854 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection (Phase: 6/9)
2025-07-30 15:41:52,873 [main | u:p] INFO  com.polarion.platform.startup - BIR inspection ... took  [ TIME 0.02 s. ]
2025-07-30 15:41:52,875 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:52,875 [main | u:p] INFO  com.polarion.platform.startup - Data indexing (Phase: 7/9)
2025-07-30 15:41:53,025 [main | u:p] INFO  com.polarion.platform.startup -  -- Refreshing 1 objects in index: Revision
2025-07-30 15:41:53,028 [main | u:p] INFO  com.polarion.platform.startup -  -- -- 1 from 1 objects were refreshed (100%)
2025-07-30 15:41:53,162 [main | u:p] INFO  com.polarion.platform.startup - Data indexing ... took  [ TIME 0.29 s. ]
2025-07-30 15:41:53,162 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:53,162 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation (Phase: 8/9)
2025-07-30 15:41:53,168 [main | u:p] INFO  com.polarion.platform.startup - Fields recalculation ... took  [ TIME 0.01 s. ]
2025-07-30 15:41:53,169 [main | u:p] INFO  com.polarion.platform.startup - ------------------------------
2025-07-30 15:41:53,169 [main | u:p] INFO  com.polarion.platform.startup - Polarion startup (Phase: 9/9)
2025-07-30 15:41:56,488 [main] INFO  com.polarion.platform.startup - Polarion startup ... took  [ TIME 3.32 s. ]
2025-07-30 15:41:56,489 [main] INFO  com.polarion.platform.startup - ****************************************************************
2025-07-30 15:41:56,489 [main] INFO  com.polarion.platform.startup -                  Polarion started:  [ TIME 11.5 s. ]
2025-07-30 15:41:56,489 [main] INFO  com.polarion.platform.startup - ****************************************************************
